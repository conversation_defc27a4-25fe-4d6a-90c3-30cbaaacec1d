'use client';

import React, { useState, useRef, useEffect } from 'react';
import { X, RefreshCw, ExternalLink, FileText, FormInput } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

interface FormsPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

const FormsPanel: React.FC<FormsPanelProps> = ({ isOpen, onClose }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFallback, setShowFallback] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const { toast } = useToast();

  const FORMS_URL = 'https://vice-forms.vercel.app/';

  // Add timestamp to force refresh and avoid cache issues
  const getFormsUrlWithTimestamp = () => {
    return `${FORMS_URL}?t=${Date.now()}`;
  };

  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      setError(null);
    }
  }, [isOpen]);

  const handleIframeLoad = () => {
    setIsLoading(false);
    setError(null);
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setError('Forms cannot load due to Electron security restrictions. Please use "Open in Browser" to access Vice IDE Forms with full functionality.');
    setShowFallback(true);
    toast({
      title: 'Iframe Blocked',
      description: 'Electron security prevents iframe loading. Click "Open in Browser" for full access.',
      variant: 'destructive',
    });
  };

  const handleOpenInBrowser = () => {
    window.open(FORMS_URL, '_blank');
    toast({
      title: 'Forms Opened',
      description: 'Vice IDE Forms opened in your default browser.',
    });
  };

  const handleRefresh = () => {
    if (iframeRef.current) {
      setIsLoading(true);
      setError(null);
      iframeRef.current.src = getFormsUrlWithTimestamp();
    }
  };

  const handleOpenExternal = () => {
    window.open(FORMS_URL, '_blank');
    toast({
      title: 'Opening Externally',
      description: 'Vice IDE Forms opened in your default browser.',
    });
  };

  if (!isOpen) return null;

  return (
    <div className="h-full w-full bg-background flex flex-col">
      {/* Toolbar */}
      <div className="flex items-center justify-between p-3 border-b bg-card/50">
        <div className="flex items-center space-x-2">
          <div className="p-1.5 bg-primary/10 rounded-md">
            <FormInput className="h-4 w-4 text-primary" />
          </div>
          <span className="text-sm font-medium">Vice IDE Forms</span>
        </div>

        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
            title="Refresh Forms"
          >
            <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleOpenExternal}
            title="Open in Browser"
          >
            <ExternalLink className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 relative bg-background">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-10">
            <div className="flex flex-col items-center space-y-4">
              <div className="p-4 bg-primary/10 rounded-full">
                <FormInput className="h-8 w-8 text-primary animate-pulse" />
              </div>
              <div className="text-center">
                <p className="text-lg font-medium">Loading Vice IDE Forms...</p>
                <p className="text-sm text-muted-foreground">
                  Connecting to https://viceide-forms.vercel.app/
                </p>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-background z-10">
            <div className="text-center space-y-4 max-w-md mx-auto p-6">
              <div className="p-4 bg-destructive/10 rounded-full w-fit mx-auto">
                <FileText className="h-8 w-8 text-destructive" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-destructive">Connection Failed</h3>
                <p className="text-sm text-muted-foreground mt-2">{error}</p>
              </div>
              <div className="flex space-x-2 justify-center">
                <Button onClick={handleRefresh} variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                <Button onClick={handleOpenExternal} variant="default">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open in Browser
                </Button>
              </div>
            </div>
          </div>
        )}

        <iframe
          ref={iframeRef}
          src={getFormsUrlWithTimestamp()}
          className="w-full h-full border-0"
          title="Vice IDE Forms"
          onLoad={handleIframeLoad}
          onError={handleIframeError}
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-downloads allow-modals allow-orientation-lock allow-pointer-lock allow-presentation allow-top-navigation allow-top-navigation-by-user-activation"
          allow="clipboard-read; clipboard-write; fullscreen; geolocation; microphone; camera; midi; encrypted-media; autoplay; payment; display-capture"
          referrerPolicy="no-referrer-when-downgrade"
          loading="lazy"
        />
      </div>

      {/* Footer */}
      <div className="border-t bg-card px-4 py-2">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-4">
            <span>Vice IDE Forms Platform</span>
            <span>•</span>
            <span>Powered by Vercel</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${error ? 'bg-destructive' : isLoading ? 'bg-yellow-500' : 'bg-green-500'}`} />
            <span>{error ? 'Disconnected' : isLoading ? 'Connecting...' : 'Connected'}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FormsPanel;
