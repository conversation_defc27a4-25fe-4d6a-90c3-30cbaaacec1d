'use client';

import React, { useState, useRef, useEffect } from 'react';
import { X, RefreshCw, ExternalLink, FileText, FormInput } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

interface FormsPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

const FormsPanel: React.FC<FormsPanelProps> = ({ isOpen, onClose }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const { toast } = useToast();

  const FORMS_URL = 'https://viceide-forms.vercel.app/';

  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      setError(null);
    }
  }, [isOpen]);

  const handleIframeLoad = () => {
    setIsLoading(false);
    setError(null);
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setError('Failed to load Vice IDE Forms. Please check your internet connection.');
    toast({
      title: 'Connection Error',
      description: 'Unable to load Vice IDE Forms. Please try again.',
      variant: 'destructive',
    });
  };

  const handleRefresh = () => {
    if (iframeRef.current) {
      setIsLoading(true);
      setError(null);
      iframeRef.current.src = FORMS_URL;
    }
  };

  const handleOpenExternal = () => {
    window.open(FORMS_URL, '_blank');
    toast({
      title: 'Opening Externally',
      description: 'Vice IDE Forms opened in your default browser.',
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-background z-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-card">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <FormInput className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h1 className="text-lg font-semibold">Vice IDE Forms</h1>
            <p className="text-sm text-muted-foreground">
              Project forms and templates for Vice IDE
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
            title="Refresh Forms"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleOpenExternal}
            title="Open in Browser"
          >
            <ExternalLink className="h-4 w-4" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={onClose}
            title="Close Forms Panel"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 relative bg-background">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-10">
            <div className="flex flex-col items-center space-y-4">
              <div className="p-4 bg-primary/10 rounded-full">
                <FormInput className="h-8 w-8 text-primary animate-pulse" />
              </div>
              <div className="text-center">
                <p className="text-lg font-medium">Loading Vice IDE Forms...</p>
                <p className="text-sm text-muted-foreground">
                  Connecting to https://viceide-forms.vercel.app/
                </p>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-background z-10">
            <div className="text-center space-y-4 max-w-md mx-auto p-6">
              <div className="p-4 bg-destructive/10 rounded-full w-fit mx-auto">
                <FileText className="h-8 w-8 text-destructive" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-destructive">Connection Failed</h3>
                <p className="text-sm text-muted-foreground mt-2">{error}</p>
              </div>
              <div className="flex space-x-2 justify-center">
                <Button onClick={handleRefresh} variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                <Button onClick={handleOpenExternal} variant="default">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open in Browser
                </Button>
              </div>
            </div>
          </div>
        )}

        <iframe
          ref={iframeRef}
          src={FORMS_URL}
          className="w-full h-full border-0"
          title="Vice IDE Forms"
          onLoad={handleIframeLoad}
          onError={handleIframeError}
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox"
          allow="clipboard-read; clipboard-write"
        />
      </div>

      {/* Footer */}
      <div className="border-t bg-card px-4 py-2">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-4">
            <span>Vice IDE Forms Platform</span>
            <span>•</span>
            <span>Powered by Vercel</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${error ? 'bg-destructive' : isLoading ? 'bg-yellow-500' : 'bg-green-500'}`} />
            <span>{error ? 'Disconnected' : isLoading ? 'Connecting...' : 'Connected'}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FormsPanel;
