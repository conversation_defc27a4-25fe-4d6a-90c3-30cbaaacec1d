/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stack/[id]/route";
exports.ids = ["app/api/stack/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstack%2F%5Bid%5D%2Froute&page=%2Fapi%2Fstack%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstack%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstack%2F%5Bid%5D%2Froute&page=%2Fapi%2Fstack%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstack%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_projects_New_folder_Vice_Vice_src_app_api_stack_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stack/[id]/route.ts */ \"(rsc)/./src/app/api/stack/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stack/[id]/route\",\n        pathname: \"/api/stack/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/stack/[id]/route\"\n    },\n    resolvedPagePath: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\app\\\\api\\\\stack\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_projects_New_folder_Vice_Vice_src_app_api_stack_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstack%2F%5Bid%5D%2Froute&page=%2Fapi%2Fstack%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstack%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stack/[id]/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/stack/[id]/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_debug__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/debug */ \"(rsc)/./src/lib/debug.ts\");\n\n\n\nasync function GET(request, { params }) {\n    try {\n        const { id } = await params;\n        const stackName = id;\n        // Log the requested stack name for debugging\n        (0,_lib_debug__WEBPACK_IMPORTED_MODULE_2__.debugLog)(`API: Requested stack name: \"${stackName}\"`);\n        // Get all stacks first for debugging\n        const allStacks = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].devStack.findMany({\n            select: {\n                id: true,\n                name: true\n            }\n        });\n        (0,_lib_debug__WEBPACK_IMPORTED_MODULE_2__.debugLog)('API: Available stacks:', allStacks);\n        // Try to find the stack by exact name first\n        let stack = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].devStack.findFirst({\n            where: {\n                name: stackName\n            },\n            include: {\n                projects: true\n            }\n        });\n        // If not found, try case-insensitive search by getting all stacks and filtering\n        if (!stack) {\n            (0,_lib_debug__WEBPACK_IMPORTED_MODULE_2__.debugLog)(`API: Exact match not found, trying case-insensitive search`);\n            const allStacks = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].devStack.findMany({\n                include: {\n                    projects: true\n                }\n            });\n            stack = allStacks.find((s)=>s.name.toLowerCase() === stackName.toLowerCase()) || null;\n        }\n        // If still not found, try to capitalize first letter\n        if (!stack) {\n            const capitalizedName = stackName.charAt(0).toUpperCase() + stackName.slice(1).toLowerCase();\n            (0,_lib_debug__WEBPACK_IMPORTED_MODULE_2__.debugLog)(`API: Case-insensitive match not found, trying capitalized name: \"${capitalizedName}\"`);\n            stack = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].devStack.findFirst({\n                where: {\n                    name: capitalizedName\n                },\n                include: {\n                    projects: true\n                }\n            });\n        }\n        if (stack) {\n            (0,_lib_debug__WEBPACK_IMPORTED_MODULE_2__.debugLog)(`API: Found stack: \"${stack.name}\" with ${stack.projects.length} projects`);\n        } else {\n            (0,_lib_debug__WEBPACK_IMPORTED_MODULE_2__.debugError)(`API: Stack not found for name: \"${stackName}\"`);\n        }\n        if (!stack) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Stack not found'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(stack);\n    } catch (error) {\n        (0,_lib_debug__WEBPACK_IMPORTED_MODULE_2__.debugError)('Error fetching stack details:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch stack details',\n            message: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stack/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/debug.ts":
/*!**************************!*\
  !*** ./src/lib/debug.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   debugError: () => (/* binding */ debugError),\n/* harmony export */   debugLog: () => (/* binding */ debugLog)\n/* harmony export */ });\n/**\n * Utility functions for debugging\n */ /**\n * Log a message to the console with a timestamp\n */ function debugLog(message, data) {\n    const timestamp = new Date().toISOString();\n    console.log(`[${timestamp}] ${message}`);\n    if (data !== undefined) {\n        console.log(data);\n    }\n}\n/**\n * Log an error to the console with a timestamp\n */ function debugError(message, error) {\n    const timestamp = new Date().toISOString();\n    console.error(`[${timestamp}] ERROR: ${message}`);\n    if (error !== undefined) {\n        console.error(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RlYnVnLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7O0NBRUMsR0FFRDs7Q0FFQyxHQUNNLFNBQVNBLFNBQVNDLE9BQWUsRUFBRUMsSUFBVTtJQUNsRCxNQUFNQyxZQUFZLElBQUlDLE9BQU9DLFdBQVc7SUFDeENDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRUosVUFBVSxFQUFFLEVBQUVGLFNBQVM7SUFDdkMsSUFBSUMsU0FBU00sV0FBVztRQUN0QkYsUUFBUUMsR0FBRyxDQUFDTDtJQUNkO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLFNBQVNPLFdBQVdSLE9BQWUsRUFBRVMsS0FBVztJQUNyRCxNQUFNUCxZQUFZLElBQUlDLE9BQU9DLFdBQVc7SUFDeENDLFFBQVFJLEtBQUssQ0FBQyxDQUFDLENBQUMsRUFBRVAsVUFBVSxTQUFTLEVBQUVGLFNBQVM7SUFDaEQsSUFBSVMsVUFBVUYsV0FBVztRQUN2QkYsUUFBUUksS0FBSyxDQUFDQTtJQUNoQjtBQUNGIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXE5ldyBmb2xkZXJcXFZpY2VcXFZpY2VcXHNyY1xcbGliXFxkZWJ1Zy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFV0aWxpdHkgZnVuY3Rpb25zIGZvciBkZWJ1Z2dpbmdcbiAqL1xuXG4vKipcbiAqIExvZyBhIG1lc3NhZ2UgdG8gdGhlIGNvbnNvbGUgd2l0aCBhIHRpbWVzdGFtcFxuICovXG5leHBvcnQgZnVuY3Rpb24gZGVidWdMb2cobWVzc2FnZTogc3RyaW5nLCBkYXRhPzogYW55KTogdm9pZCB7XG4gIGNvbnN0IHRpbWVzdGFtcCA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKTtcbiAgY29uc29sZS5sb2coYFske3RpbWVzdGFtcH1dICR7bWVzc2FnZX1gKTtcbiAgaWYgKGRhdGEgIT09IHVuZGVmaW5lZCkge1xuICAgIGNvbnNvbGUubG9nKGRhdGEpO1xuICB9XG59XG5cbi8qKlxuICogTG9nIGFuIGVycm9yIHRvIHRoZSBjb25zb2xlIHdpdGggYSB0aW1lc3RhbXBcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGRlYnVnRXJyb3IobWVzc2FnZTogc3RyaW5nLCBlcnJvcj86IGFueSk6IHZvaWQge1xuICBjb25zdCB0aW1lc3RhbXAgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCk7XG4gIGNvbnNvbGUuZXJyb3IoYFske3RpbWVzdGFtcH1dIEVSUk9SOiAke21lc3NhZ2V9YCk7XG4gIGlmIChlcnJvciAhPT0gdW5kZWZpbmVkKSB7XG4gICAgY29uc29sZS5lcnJvcihlcnJvcik7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJkZWJ1Z0xvZyIsIm1lc3NhZ2UiLCJkYXRhIiwidGltZXN0YW1wIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiY29uc29sZSIsImxvZyIsInVuZGVmaW5lZCIsImRlYnVnRXJyb3IiLCJlcnJvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/debug.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// PrismaClient is attached to the `global` object in development to prevent\n// exhausting your database connection limit.\n// Learn more: https://pris.ly/d/help/next-js-best-practices\nconst globalForPrisma = global;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQThDO0FBRTlDLDRFQUE0RTtBQUM1RSw2Q0FBNkM7QUFDN0MsNERBQTREO0FBRTVELE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBcUMsRUFBRUgsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBRXBFLGlFQUFlQSxNQUFNQSxFQUFDIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXE5ldyBmb2xkZXJcXFZpY2VcXFZpY2VcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG4vLyBQcmlzbWFDbGllbnQgaXMgYXR0YWNoZWQgdG8gdGhlIGBnbG9iYWxgIG9iamVjdCBpbiBkZXZlbG9wbWVudCB0byBwcmV2ZW50XG4vLyBleGhhdXN0aW5nIHlvdXIgZGF0YWJhc2UgY29ubmVjdGlvbiBsaW1pdC5cbi8vIExlYXJuIG1vcmU6IGh0dHBzOi8vcHJpcy5seS9kL2hlbHAvbmV4dC1qcy1iZXN0LXByYWN0aWNlc1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWwgYXMgdW5rbm93biBhcyB7IHByaXNtYTogUHJpc21hQ2xpZW50IH07XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hIHx8IG5ldyBQcmlzbWFDbGllbnQoKTtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XG5cbmV4cG9ydCBkZWZhdWx0IHByaXNtYTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWwiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstack%2F%5Bid%5D%2Froute&page=%2Fapi%2Fstack%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstack%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();