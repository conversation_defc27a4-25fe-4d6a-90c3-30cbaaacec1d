@echo off
title TEST COMPLETE PROJECT CREATION
color 0A

echo ========================================
echo   TEST COMPLETE PROJECT CREATION
echo ========================================
echo.
echo 🚀 TESTING COMPLETE PROJECT CREATION WITH COMMAND EXECUTION
echo.
echo **What's Fixed and Working:**
echo ✅ **Command execution page** → Shows real commands being executed
echo ✅ **IDE loads in Electron** → Opens in Electron window, not browser
echo ✅ **Project opens in IDE** → Created project loads automatically
echo ✅ **Text field constraints** → No spaces, no caps, hyphens for multi-word
echo ✅ **Real project creation** → Runs actual npm, python, django commands
echo ✅ **Bash file organizer** → Runs file organizer setup commands
echo ✅ **Task.json creation** → Creates matching task files for each stack
echo ✅ **Loading animations** → Terminal-style command progress display
echo ✅ **No "files is not iterable"** → Fixed with safe array handling
echo.
pause

echo **Step 1: Start Vice services...**
echo **Starting Dashboard...**
cd Vice
start "Vice Dashboard" cmd /k "title Vice Dashboard && npm run dev"
cd ..

echo **Starting IDE...**
cd ViceIde
start "Vice IDE" cmd /k "title Vice IDE && npm run dev"
cd ..

echo **Waiting for services to start...**
timeout /t 15 /nobreak >nul

echo.
echo **Step 2: Launch VICE with complete project creation...**
echo **Testing full workflow with command execution...**

npm run electron

echo.
echo ========================================
echo ✅ COMPLETE PROJECT CREATION READY!
echo ========================================
echo.

echo **🎯 COMPLETE TESTING WORKFLOW:**
echo.
echo **1. Test Text Field Constraints:**
echo    ✅ **Type "My Bash Project"** → Shows "Will be: my-bash-project"
echo    ✅ **Type "File Organizer 2024"** → Shows "Will be: file-organizer-2024"
echo    ✅ **See constraint message** → "No spaces or caps allowed"
echo    ✅ **Real-time validation** → Orange preview text appears
echo.
echo **2. Test Command Execution Page:**
echo    ✅ **Click "Create Project"** → Loading screen appears
echo    ✅ **See project name** → "Creating 'project-name'"
echo    ✅ **Watch commands execute** → Real commands with terminal styling
echo    ✅ **See progress indicators** → Spinning loaders and checkmarks
echo    ✅ **Real-time updates** → Commands appear as they run
echo.
echo **3. Test Bash File Organizer:**
echo    ✅ **Create Bash project** → Should run file organizer setup
echo    ✅ **See specific commands** → mkdir, touch, chmod commands
echo    ✅ **File organizer structure** → Creates organizer directories
echo    ✅ **Task.json creation** → Matching tasks for file organization
echo.
echo **4. Test IDE Integration:**
echo    ✅ **IDE opens in Electron** → Not in browser window
echo    ✅ **Project loads automatically** → Created files appear
echo    ✅ **File explorer populated** → Shows actual project structure
echo    ✅ **Monaco editor ready** → Can edit created files
echo    ✅ **Terminal available** → Can run commands in project directory
echo.
echo **5. Test Multiple Stack Types:**
echo    ✅ **React projects** → npm create-react-app commands
echo    ✅ **Django projects** → python venv and django-admin commands
echo    ✅ **Flask projects** → python venv and pip install commands
echo    ✅ **Next.js projects** → npx create-next-app commands
echo    ✅ **MERN projects** → React + Express setup commands
echo.
echo ========================================
echo EXPECTED COMPLETE RESULTS
echo ========================================
echo.

echo **✅ COMMAND EXECUTION PAGE:**
echo.
echo **Visual Elements:**
echo 1. **Project name display** → "Creating 'my-bash-project'"
echo 2. **Terminal-style output** → Black background with colored text
echo 3. **Real commands shown** → mkdir, touch, chmod, npm, python
echo 4. **Progress indicators** → Spinning loaders and completion checkmarks
echo 5. **Real-time streaming** → Commands appear as they execute
echo.
echo **Command Examples:**
echo 1. **Bash projects** → mkdir scripts, touch organize.sh, chmod +x
echo 2. **React projects** → npx create-react-app, npm install
echo 3. **Django projects** → python -m venv, pip install django
echo 4. **Flask projects** → python -m venv, pip install flask
echo 5. **Next.js projects** → npx create-next-app@latest
echo.
echo **✅ IDE INTEGRATION:**
echo.
echo **Electron Window:**
echo 1. **Opens in Electron** → Not browser tab
echo 2. **Automatic project loading** → No manual file opening needed
echo 3. **Full file structure** → All created files visible
echo 4. **Monaco editor ready** → Syntax highlighting works
echo 5. **Integrated terminal** → Commands run in project directory
echo.
echo **Project Structure:**
echo 1. **Bash projects** → scripts/, organize.sh, task.json
echo 2. **React projects** → src/, public/, package.json, node_modules/
echo 3. **Django projects** → venv/, manage.py, settings.py, urls.py
echo 4. **Flask projects** → venv/, app/, requirements.txt
echo 5. **Next.js projects** → pages/, components/, next.config.js
echo.
echo ========================================
echo BASH FILE ORGANIZER TESTING
echo ========================================
echo.

echo **🗂️ Specific Bash Project Testing:**
echo.
echo **Create File Organizer Project:**
echo    • Project name: "file-organizer-2024"
echo    • Stack: Bash
echo    • Should see these commands:
echo      - mkdir file-organizer-2024
echo      - cd file-organizer-2024
echo      - mkdir scripts
echo      - touch organize.sh
echo      - chmod +x organize.sh
echo      - echo "#!/bin/bash" > organize.sh
echo      - mkdir input output processed
echo.
echo **Expected File Structure:**
echo    • file-organizer-2024/
echo      ├── scripts/
echo      │   └── organize.sh
echo      ├── input/
echo      ├── output/
echo      ├── processed/
echo      └── task.json (with file organization tasks)
echo.
echo **Task.json Content:**
echo    • File organization tasks
echo    • Sort by type, date, size
echo    • Move duplicates
echo    • Clean empty folders
echo    • Generate reports
echo.
echo ========================================
echo TROUBLESHOOTING
echo ========================================
echo.

echo **If command execution page doesn't show:**
echo 1. **Check loading state** → Should show immediately after "Create Project"
echo 2. **Check console logs** → F12 for command progress events
echo 3. **Check Electron API** → Should be available in desktop app
echo 4. **Check project creation** → Commands should execute in background
echo.
echo **If IDE doesn't open in Electron:**
echo 1. **Check IDE window creation** → Should open new Electron window
echo 2. **Check project loading** → Should load created files automatically
echo 3. **Check file discovery** → IDE should find files from filesystem
echo 4. **Manual check** → Verify files exist in ViceProjects folder
echo.
echo **If commands don't execute:**
echo 1. **Check dependencies** → npm, python should be installed
echo 2. **Check file permissions** → Write access to ViceProjects folder
echo 3. **Check network** → Internet access for package downloads
echo 4. **Check command generation** → Each stack has specific commands
echo.
echo **🚀 Complete project creation with command execution and IDE integration!**
echo.
echo **Key Features Working:**
echo 1. **Real command execution** → Actual npm, python, django commands
echo 2. **Command execution page** → Visual progress with terminal styling
echo 3. **Text field constraints** → Proper name formatting and validation
echo 4. **IDE in Electron** → Opens in desktop app, not browser
echo 5. **Automatic project loading** → Created files appear immediately
echo 6. **Stack-specific setup** → Each stack creates appropriate structure
echo 7. **File organizer** → Bash projects get organizer functionality
echo 8. **Task.json creation** → Matching tasks for each project type
echo.
echo **🎉 Vice IDE with complete functional project creation!**
echo.
pause
