"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-resizable-panels";
exports.ids = ["vendor-chunks/react-resizable-panels"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-resizable-panels/dist/react-resizable-panels.development.node.esm.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/react-resizable-panels/dist/react-resizable-panels.development.node.esm.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DATA_ATTRIBUTES: () => (/* binding */ DATA_ATTRIBUTES),\n/* harmony export */   Panel: () => (/* binding */ Panel),\n/* harmony export */   PanelGroup: () => (/* binding */ PanelGroup),\n/* harmony export */   PanelResizeHandle: () => (/* binding */ PanelResizeHandle),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   disableGlobalCursorStyles: () => (/* binding */ disableGlobalCursorStyles),\n/* harmony export */   enableGlobalCursorStyles: () => (/* binding */ enableGlobalCursorStyles),\n/* harmony export */   getIntersectingRectangle: () => (/* binding */ getIntersectingRectangle),\n/* harmony export */   getPanelElement: () => (/* binding */ getPanelElement),\n/* harmony export */   getPanelElementsForGroup: () => (/* binding */ getPanelElementsForGroup),\n/* harmony export */   getPanelGroupElement: () => (/* binding */ getPanelGroupElement),\n/* harmony export */   getResizeHandleElement: () => (/* binding */ getResizeHandleElement),\n/* harmony export */   getResizeHandleElementIndex: () => (/* binding */ getResizeHandleElementIndex),\n/* harmony export */   getResizeHandleElementsForGroup: () => (/* binding */ getResizeHandleElementsForGroup),\n/* harmony export */   getResizeHandlePanelIds: () => (/* binding */ getResizeHandlePanelIds),\n/* harmony export */   intersects: () => (/* binding */ intersects),\n/* harmony export */   setNonce: () => (/* binding */ setNonce)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n\n// The \"contextmenu\" event is not supported as a PointerEvent in all browsers yet, so MouseEvent still need to be handled\n\nconst PanelGroupContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nPanelGroupContext.displayName = \"PanelGroupContext\";\n\nconst DATA_ATTRIBUTES = {\n  group: \"data-panel-group\",\n  groupDirection: \"data-panel-group-direction\",\n  groupId: \"data-panel-group-id\",\n  panel: \"data-panel\",\n  panelCollapsible: \"data-panel-collapsible\",\n  panelId: \"data-panel-id\",\n  panelSize: \"data-panel-size\",\n  resizeHandle: \"data-resize-handle\",\n  resizeHandleActive: \"data-resize-handle-active\",\n  resizeHandleEnabled: \"data-panel-resize-handle-enabled\",\n  resizeHandleId: \"data-panel-resize-handle-id\",\n  resizeHandleState: \"data-resize-handle-state\"\n};\nconst PRECISION = 10;\n\nconst useId = react__WEBPACK_IMPORTED_MODULE_0__[\"useId\".toString()];\nconst wrappedUseId = typeof useId === \"function\" ? useId : () => null;\nlet counter = 0;\nfunction useUniqueId(idFromParams = null) {\n  const idFromUseId = wrappedUseId();\n  const idRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(idFromParams || idFromUseId || null);\n  if (idRef.current === null) {\n    idRef.current = \"\" + counter++;\n  }\n  return idFromParams !== null && idFromParams !== void 0 ? idFromParams : idRef.current;\n}\n\nfunction PanelWithForwardedRef({\n  children,\n  className: classNameFromProps = \"\",\n  collapsedSize,\n  collapsible,\n  defaultSize,\n  forwardedRef,\n  id: idFromProps,\n  maxSize,\n  minSize,\n  onCollapse,\n  onExpand,\n  onResize,\n  order,\n  style: styleFromProps,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(PanelGroupContext);\n  if (context === null) {\n    throw Error(`Panel components must be rendered within a PanelGroup container`);\n  }\n  const {\n    collapsePanel,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    reevaluatePanelConstraints,\n    registerPanel,\n    resizePanel,\n    unregisterPanel\n  } = context;\n  const panelId = useUniqueId(idFromProps);\n  const panelDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    callbacks: {\n      onCollapse,\n      onExpand,\n      onResize\n    },\n    constraints: {\n      collapsedSize,\n      collapsible,\n      defaultSize,\n      maxSize,\n      minSize\n    },\n    id: panelId,\n    idIsFromProps: idFromProps !== undefined,\n    order\n  });\n  const devWarningsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    didLogMissingDefaultSizeWarning: false\n  });\n\n  // Normally we wouldn't log a warning during render,\n  // but effects don't run on the server, so we can't do it there\n  {\n    if (!devWarningsRef.current.didLogMissingDefaultSizeWarning) {\n      if (defaultSize == null) {\n        devWarningsRef.current.didLogMissingDefaultSizeWarning = true;\n        console.warn(`WARNING: Panel defaultSize prop recommended to avoid layout shift after server rendering`);\n      }\n    }\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(forwardedRef, () => ({\n    collapse: () => {\n      collapsePanel(panelDataRef.current);\n    },\n    expand: minSize => {\n      expandPanel(panelDataRef.current, minSize);\n    },\n    getId() {\n      return panelId;\n    },\n    getSize() {\n      return getPanelSize(panelDataRef.current);\n    },\n    isCollapsed() {\n      return isPanelCollapsed(panelDataRef.current);\n    },\n    isExpanded() {\n      return !isPanelCollapsed(panelDataRef.current);\n    },\n    resize: size => {\n      resizePanel(panelDataRef.current, size);\n    }\n  }), [collapsePanel, expandPanel, getPanelSize, isPanelCollapsed, panelId, resizePanel]);\n  const style = getPanelStyle(panelDataRef.current, defaultSize);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: panelId,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    // CSS selectors\n    [DATA_ATTRIBUTES.groupId]: groupId,\n    [DATA_ATTRIBUTES.panel]: \"\",\n    [DATA_ATTRIBUTES.panelCollapsible]: collapsible || undefined,\n    [DATA_ATTRIBUTES.panelId]: panelId,\n    [DATA_ATTRIBUTES.panelSize]: parseFloat(\"\" + style.flexGrow).toFixed(1)\n  });\n}\nconst Panel = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(PanelWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelWithForwardedRef.displayName = \"Panel\";\nPanel.displayName = \"forwardRef(Panel)\";\n\nlet nonce;\nfunction getNonce() {\n  return nonce;\n}\nfunction setNonce(value) {\n  nonce = value;\n}\n\nlet currentCursorStyle = null;\nlet enabled = true;\nlet prevRuleIndex = -1;\nlet styleElement = null;\nfunction disableGlobalCursorStyles() {\n  enabled = false;\n}\nfunction enableGlobalCursorStyles() {\n  enabled = true;\n}\nfunction getCursorStyle(state, constraintFlags) {\n  if (constraintFlags) {\n    const horizontalMin = (constraintFlags & EXCEEDED_HORIZONTAL_MIN) !== 0;\n    const horizontalMax = (constraintFlags & EXCEEDED_HORIZONTAL_MAX) !== 0;\n    const verticalMin = (constraintFlags & EXCEEDED_VERTICAL_MIN) !== 0;\n    const verticalMax = (constraintFlags & EXCEEDED_VERTICAL_MAX) !== 0;\n    if (horizontalMin) {\n      if (verticalMin) {\n        return \"se-resize\";\n      } else if (verticalMax) {\n        return \"ne-resize\";\n      } else {\n        return \"e-resize\";\n      }\n    } else if (horizontalMax) {\n      if (verticalMin) {\n        return \"sw-resize\";\n      } else if (verticalMax) {\n        return \"nw-resize\";\n      } else {\n        return \"w-resize\";\n      }\n    } else if (verticalMin) {\n      return \"s-resize\";\n    } else if (verticalMax) {\n      return \"n-resize\";\n    }\n  }\n  switch (state) {\n    case \"horizontal\":\n      return \"ew-resize\";\n    case \"intersection\":\n      return \"move\";\n    case \"vertical\":\n      return \"ns-resize\";\n  }\n}\nfunction resetGlobalCursorStyle() {\n  if (styleElement !== null) {\n    document.head.removeChild(styleElement);\n    currentCursorStyle = null;\n    styleElement = null;\n    prevRuleIndex = -1;\n  }\n}\nfunction setGlobalCursorStyle(state, constraintFlags) {\n  var _styleElement$sheet$i, _styleElement$sheet2;\n  if (!enabled) {\n    return;\n  }\n  const style = getCursorStyle(state, constraintFlags);\n  if (currentCursorStyle === style) {\n    return;\n  }\n  currentCursorStyle = style;\n  if (styleElement === null) {\n    styleElement = document.createElement(\"style\");\n    const nonce = getNonce();\n    if (nonce) {\n      styleElement.setAttribute(\"nonce\", nonce);\n    }\n    document.head.appendChild(styleElement);\n  }\n  if (prevRuleIndex >= 0) {\n    var _styleElement$sheet;\n    (_styleElement$sheet = styleElement.sheet) === null || _styleElement$sheet === void 0 ? void 0 : _styleElement$sheet.removeRule(prevRuleIndex);\n  }\n  prevRuleIndex = (_styleElement$sheet$i = (_styleElement$sheet2 = styleElement.sheet) === null || _styleElement$sheet2 === void 0 ? void 0 : _styleElement$sheet2.insertRule(`*{cursor: ${style} !important;}`)) !== null && _styleElement$sheet$i !== void 0 ? _styleElement$sheet$i : -1;\n}\n\nfunction isKeyDown(event) {\n  return event.type === \"keydown\";\n}\nfunction isPointerEvent(event) {\n  return event.type.startsWith(\"pointer\");\n}\nfunction isMouseEvent(event) {\n  return event.type.startsWith(\"mouse\");\n}\n\nfunction getResizeEventCoordinates(event) {\n  if (isPointerEvent(event)) {\n    if (event.isPrimary) {\n      return {\n        x: event.clientX,\n        y: event.clientY\n      };\n    }\n  } else if (isMouseEvent(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n  return {\n    x: Infinity,\n    y: Infinity\n  };\n}\n\nfunction getInputType() {\n  if (typeof matchMedia === \"function\") {\n    return matchMedia(\"(pointer:coarse)\").matches ? \"coarse\" : \"fine\";\n  }\n}\n\nfunction intersects(rectOne, rectTwo, strict) {\n  if (strict) {\n    return rectOne.x < rectTwo.x + rectTwo.width && rectOne.x + rectOne.width > rectTwo.x && rectOne.y < rectTwo.y + rectTwo.height && rectOne.y + rectOne.height > rectTwo.y;\n  } else {\n    return rectOne.x <= rectTwo.x + rectTwo.width && rectOne.x + rectOne.width >= rectTwo.x && rectOne.y <= rectTwo.y + rectTwo.height && rectOne.y + rectOne.height >= rectTwo.y;\n  }\n}\n\n// Forked from NPM stacking-order@2.0.0\n\n/**\n * Determine which of two nodes appears in front of the other —\n * if `a` is in front, returns 1, otherwise returns -1\n * @param {HTMLElement | SVGElement} a\n * @param {HTMLElement | SVGElement} b\n */\nfunction compare(a, b) {\n  if (a === b) throw new Error(\"Cannot compare node with itself\");\n  const ancestors = {\n    a: get_ancestors(a),\n    b: get_ancestors(b)\n  };\n  let common_ancestor;\n\n  // remove shared ancestors\n  while (ancestors.a.at(-1) === ancestors.b.at(-1)) {\n    a = ancestors.a.pop();\n    b = ancestors.b.pop();\n    common_ancestor = a;\n  }\n  assert(common_ancestor, \"Stacking order can only be calculated for elements with a common ancestor\");\n  const z_indexes = {\n    a: get_z_index(find_stacking_context(ancestors.a)),\n    b: get_z_index(find_stacking_context(ancestors.b))\n  };\n  if (z_indexes.a === z_indexes.b) {\n    const children = common_ancestor.childNodes;\n    const furthest_ancestors = {\n      a: ancestors.a.at(-1),\n      b: ancestors.b.at(-1)\n    };\n    let i = children.length;\n    while (i--) {\n      const child = children[i];\n      if (child === furthest_ancestors.a) return 1;\n      if (child === furthest_ancestors.b) return -1;\n    }\n  }\n  return Math.sign(z_indexes.a - z_indexes.b);\n}\nconst props = /\\b(?:position|zIndex|opacity|transform|webkitTransform|mixBlendMode|filter|webkitFilter|isolation)\\b/;\n\n/** @param {HTMLElement | SVGElement} node */\nfunction is_flex_item(node) {\n  var _get_parent;\n  // @ts-ignore\n  const display = getComputedStyle((_get_parent = get_parent(node)) !== null && _get_parent !== void 0 ? _get_parent : node).display;\n  return display === \"flex\" || display === \"inline-flex\";\n}\n\n/** @param {HTMLElement | SVGElement} node */\nfunction creates_stacking_context(node) {\n  const style = getComputedStyle(node);\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Positioning/Understanding_z_index/The_stacking_context\n  if (style.position === \"fixed\") return true;\n  // Forked to fix upstream bug https://github.com/Rich-Harris/stacking-order/issues/3\n  // if (\n  //   (style.zIndex !== \"auto\" && style.position !== \"static\") ||\n  //   is_flex_item(node)\n  // )\n  if (style.zIndex !== \"auto\" && (style.position !== \"static\" || is_flex_item(node))) return true;\n  if (+style.opacity < 1) return true;\n  if (\"transform\" in style && style.transform !== \"none\") return true;\n  if (\"webkitTransform\" in style && style.webkitTransform !== \"none\") return true;\n  if (\"mixBlendMode\" in style && style.mixBlendMode !== \"normal\") return true;\n  if (\"filter\" in style && style.filter !== \"none\") return true;\n  if (\"webkitFilter\" in style && style.webkitFilter !== \"none\") return true;\n  if (\"isolation\" in style && style.isolation === \"isolate\") return true;\n  if (props.test(style.willChange)) return true;\n  // @ts-expect-error\n  if (style.webkitOverflowScrolling === \"touch\") return true;\n  return false;\n}\n\n/** @param {(HTMLElement| SVGElement)[]} nodes */\nfunction find_stacking_context(nodes) {\n  let i = nodes.length;\n  while (i--) {\n    const node = nodes[i];\n    assert(node, \"Missing node\");\n    if (creates_stacking_context(node)) return node;\n  }\n  return null;\n}\n\n/** @param {HTMLElement | SVGElement} node */\nfunction get_z_index(node) {\n  return node && Number(getComputedStyle(node).zIndex) || 0;\n}\n\n/** @param {HTMLElement} node */\nfunction get_ancestors(node) {\n  const ancestors = [];\n  while (node) {\n    ancestors.push(node);\n    // @ts-ignore\n    node = get_parent(node);\n  }\n  return ancestors; // [ node, ... <body>, <html>, document ]\n}\n\n/** @param {HTMLElement} node */\nfunction get_parent(node) {\n  const {\n    parentNode\n  } = node;\n  if (parentNode && parentNode instanceof ShadowRoot) {\n    return parentNode.host;\n  }\n  return parentNode;\n}\n\nconst EXCEEDED_HORIZONTAL_MIN = 0b0001;\nconst EXCEEDED_HORIZONTAL_MAX = 0b0010;\nconst EXCEEDED_VERTICAL_MIN = 0b0100;\nconst EXCEEDED_VERTICAL_MAX = 0b1000;\nconst isCoarsePointer = getInputType() === \"coarse\";\nlet intersectingHandles = [];\nlet isPointerDown = false;\nlet ownerDocumentCounts = new Map();\nlet panelConstraintFlags = new Map();\nconst registeredResizeHandlers = new Set();\nfunction registerResizeHandle(resizeHandleId, element, direction, hitAreaMargins, setResizeHandlerState) {\n  var _ownerDocumentCounts$;\n  const {\n    ownerDocument\n  } = element;\n  const data = {\n    direction,\n    element,\n    hitAreaMargins,\n    setResizeHandlerState\n  };\n  const count = (_ownerDocumentCounts$ = ownerDocumentCounts.get(ownerDocument)) !== null && _ownerDocumentCounts$ !== void 0 ? _ownerDocumentCounts$ : 0;\n  ownerDocumentCounts.set(ownerDocument, count + 1);\n  registeredResizeHandlers.add(data);\n  updateListeners();\n  return function unregisterResizeHandle() {\n    var _ownerDocumentCounts$2;\n    panelConstraintFlags.delete(resizeHandleId);\n    registeredResizeHandlers.delete(data);\n    const count = (_ownerDocumentCounts$2 = ownerDocumentCounts.get(ownerDocument)) !== null && _ownerDocumentCounts$2 !== void 0 ? _ownerDocumentCounts$2 : 1;\n    ownerDocumentCounts.set(ownerDocument, count - 1);\n    updateListeners();\n    if (count === 1) {\n      ownerDocumentCounts.delete(ownerDocument);\n    }\n\n    // If the resize handle that is currently unmounting is intersecting with the pointer,\n    // update the global pointer to account for the change\n    if (intersectingHandles.includes(data)) {\n      const index = intersectingHandles.indexOf(data);\n      if (index >= 0) {\n        intersectingHandles.splice(index, 1);\n      }\n      updateCursor();\n\n      // Also instruct the handle to stop dragging; this prevents the parent group from being left in an inconsistent state\n      // See github.com/bvaughn/react-resizable-panels/issues/402\n      setResizeHandlerState(\"up\", true, null);\n    }\n  };\n}\nfunction handlePointerDown(event) {\n  const {\n    target\n  } = event;\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  isPointerDown = true;\n  recalculateIntersectingHandles({\n    target,\n    x,\n    y\n  });\n  updateListeners();\n  if (intersectingHandles.length > 0) {\n    updateResizeHandlerStates(\"down\", event);\n    event.preventDefault();\n    if (!isWithinResizeHandle(target)) {\n      event.stopImmediatePropagation();\n    }\n  }\n}\nfunction handlePointerMove(event) {\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n\n  // Edge case (see #340)\n  // Detect when the pointer has been released outside an iframe on a different domain\n  if (isPointerDown && event.buttons === 0) {\n    isPointerDown = false;\n    updateResizeHandlerStates(\"up\", event);\n  }\n  if (!isPointerDown) {\n    const {\n      target\n    } = event;\n\n    // Recalculate intersecting handles whenever the pointer moves, except if it has already been pressed\n    // at that point, the handles may not move with the pointer (depending on constraints)\n    // but the same set of active handles should be locked until the pointer is released\n    recalculateIntersectingHandles({\n      target,\n      x,\n      y\n    });\n  }\n  updateResizeHandlerStates(\"move\", event);\n\n  // Update cursor based on return value(s) from active handles\n  updateCursor();\n  if (intersectingHandles.length > 0) {\n    event.preventDefault();\n  }\n}\nfunction handlePointerUp(event) {\n  const {\n    target\n  } = event;\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  panelConstraintFlags.clear();\n  isPointerDown = false;\n  if (intersectingHandles.length > 0) {\n    event.preventDefault();\n    if (!isWithinResizeHandle(target)) {\n      event.stopImmediatePropagation();\n    }\n  }\n  updateResizeHandlerStates(\"up\", event);\n  recalculateIntersectingHandles({\n    target,\n    x,\n    y\n  });\n  updateCursor();\n  updateListeners();\n}\nfunction isWithinResizeHandle(element) {\n  let currentElement = element;\n  while (currentElement) {\n    if (currentElement.hasAttribute(DATA_ATTRIBUTES.resizeHandle)) {\n      return true;\n    }\n    currentElement = currentElement.parentElement;\n  }\n  return false;\n}\nfunction recalculateIntersectingHandles({\n  target,\n  x,\n  y\n}) {\n  intersectingHandles.splice(0);\n  let targetElement = null;\n  if (target instanceof HTMLElement || target instanceof SVGElement) {\n    targetElement = target;\n  }\n  registeredResizeHandlers.forEach(data => {\n    const {\n      element: dragHandleElement,\n      hitAreaMargins\n    } = data;\n    const dragHandleRect = dragHandleElement.getBoundingClientRect();\n    const {\n      bottom,\n      left,\n      right,\n      top\n    } = dragHandleRect;\n    const margin = isCoarsePointer ? hitAreaMargins.coarse : hitAreaMargins.fine;\n    const eventIntersects = x >= left - margin && x <= right + margin && y >= top - margin && y <= bottom + margin;\n    if (eventIntersects) {\n      // TRICKY\n      // We listen for pointers events at the root in order to support hit area margins\n      // (determining when the pointer is close enough to an element to be considered a \"hit\")\n      // Clicking on an element \"above\" a handle (e.g. a modal) should prevent a hit though\n      // so at this point we need to compare stacking order of a potentially intersecting drag handle,\n      // and the element that was actually clicked/touched\n      if (targetElement !== null && document.contains(targetElement) && dragHandleElement !== targetElement && !dragHandleElement.contains(targetElement) && !targetElement.contains(dragHandleElement) &&\n      // Calculating stacking order has a cost, so we should avoid it if possible\n      // That is why we only check potentially intersecting handles,\n      // and why we skip if the event target is within the handle's DOM\n      compare(targetElement, dragHandleElement) > 0) {\n        // If the target is above the drag handle, then we also need to confirm they overlap\n        // If they are beside each other (e.g. a panel and its drag handle) then the handle is still interactive\n        //\n        // It's not enough to compare only the target\n        // The target might be a small element inside of a larger container\n        // (For example, a SPAN or a DIV inside of a larger modal dialog)\n        let currentElement = targetElement;\n        let didIntersect = false;\n        while (currentElement) {\n          if (currentElement.contains(dragHandleElement)) {\n            break;\n          } else if (intersects(currentElement.getBoundingClientRect(), dragHandleRect, true)) {\n            didIntersect = true;\n            break;\n          }\n          currentElement = currentElement.parentElement;\n        }\n        if (didIntersect) {\n          return;\n        }\n      }\n      intersectingHandles.push(data);\n    }\n  });\n}\nfunction reportConstraintsViolation(resizeHandleId, flag) {\n  panelConstraintFlags.set(resizeHandleId, flag);\n}\nfunction updateCursor() {\n  let intersectsHorizontal = false;\n  let intersectsVertical = false;\n  intersectingHandles.forEach(data => {\n    const {\n      direction\n    } = data;\n    if (direction === \"horizontal\") {\n      intersectsHorizontal = true;\n    } else {\n      intersectsVertical = true;\n    }\n  });\n  let constraintFlags = 0;\n  panelConstraintFlags.forEach(flag => {\n    constraintFlags |= flag;\n  });\n  if (intersectsHorizontal && intersectsVertical) {\n    setGlobalCursorStyle(\"intersection\", constraintFlags);\n  } else if (intersectsHorizontal) {\n    setGlobalCursorStyle(\"horizontal\", constraintFlags);\n  } else if (intersectsVertical) {\n    setGlobalCursorStyle(\"vertical\", constraintFlags);\n  } else {\n    resetGlobalCursorStyle();\n  }\n}\nlet listenersAbortController = new AbortController();\nfunction updateListeners() {\n  listenersAbortController.abort();\n  listenersAbortController = new AbortController();\n  const options = {\n    capture: true,\n    signal: listenersAbortController.signal\n  };\n  if (!registeredResizeHandlers.size) {\n    return;\n  }\n  if (isPointerDown) {\n    if (intersectingHandles.length > 0) {\n      ownerDocumentCounts.forEach((count, ownerDocument) => {\n        const {\n          body\n        } = ownerDocument;\n        if (count > 0) {\n          body.addEventListener(\"contextmenu\", handlePointerUp, options);\n          body.addEventListener(\"pointerleave\", handlePointerMove, options);\n          body.addEventListener(\"pointermove\", handlePointerMove, options);\n        }\n      });\n    }\n    window.addEventListener(\"pointerup\", handlePointerUp, options);\n    window.addEventListener(\"pointercancel\", handlePointerUp, options);\n  } else {\n    ownerDocumentCounts.forEach((count, ownerDocument) => {\n      const {\n        body\n      } = ownerDocument;\n      if (count > 0) {\n        body.addEventListener(\"pointerdown\", handlePointerDown, options);\n        body.addEventListener(\"pointermove\", handlePointerMove, options);\n      }\n    });\n  }\n}\nfunction updateResizeHandlerStates(action, event) {\n  registeredResizeHandlers.forEach(data => {\n    const {\n      setResizeHandlerState\n    } = data;\n    const isActive = intersectingHandles.includes(data);\n    setResizeHandlerState(action, isActive, event);\n  });\n}\n\nfunction useForceUpdate() {\n  const [_, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => setCount(prevCount => prevCount + 1), []);\n}\n\nfunction assert(expectedCondition, message) {\n  if (!expectedCondition) {\n    console.error(message);\n    throw Error(message);\n  }\n}\n\nfunction fuzzyCompareNumbers(actual, expected, fractionDigits = PRECISION) {\n  if (actual.toFixed(fractionDigits) === expected.toFixed(fractionDigits)) {\n    return 0;\n  } else {\n    return actual > expected ? 1 : -1;\n  }\n}\nfunction fuzzyNumbersEqual$1(actual, expected, fractionDigits = PRECISION) {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;\n}\n\nfunction fuzzyNumbersEqual(actual, expected, fractionDigits) {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;\n}\n\nfunction fuzzyLayoutsEqual(actual, expected, fractionDigits) {\n  if (actual.length !== expected.length) {\n    return false;\n  }\n  for (let index = 0; index < actual.length; index++) {\n    const actualSize = actual[index];\n    const expectedSize = expected[index];\n    if (!fuzzyNumbersEqual(actualSize, expectedSize, fractionDigits)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// Panel size must be in percentages; pixel values should be pre-converted\nfunction resizePanel({\n  panelConstraints: panelConstraintsArray,\n  panelIndex,\n  size\n}) {\n  const panelConstraints = panelConstraintsArray[panelIndex];\n  assert(panelConstraints != null, `Panel constraints not found for index ${panelIndex}`);\n  let {\n    collapsedSize = 0,\n    collapsible,\n    maxSize = 100,\n    minSize = 0\n  } = panelConstraints;\n  if (fuzzyCompareNumbers(size, minSize) < 0) {\n    if (collapsible) {\n      // Collapsible panels should snap closed or open only once they cross the halfway point between collapsed and min size.\n      const halfwayPoint = (collapsedSize + minSize) / 2;\n      if (fuzzyCompareNumbers(size, halfwayPoint) < 0) {\n        size = collapsedSize;\n      } else {\n        size = minSize;\n      }\n    } else {\n      size = minSize;\n    }\n  }\n  size = Math.min(maxSize, size);\n  size = parseFloat(size.toFixed(PRECISION));\n  return size;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction adjustLayoutByDelta({\n  delta,\n  initialLayout,\n  panelConstraints: panelConstraintsArray,\n  pivotIndices,\n  prevLayout,\n  trigger\n}) {\n  if (fuzzyNumbersEqual(delta, 0)) {\n    return initialLayout;\n  }\n  const nextLayout = [...initialLayout];\n  const [firstPivotIndex, secondPivotIndex] = pivotIndices;\n  assert(firstPivotIndex != null, \"Invalid first pivot index\");\n  assert(secondPivotIndex != null, \"Invalid second pivot index\");\n  let deltaApplied = 0;\n\n  // const DEBUG = [];\n  // DEBUG.push(`adjustLayoutByDelta()`);\n  // DEBUG.push(`  initialLayout: ${initialLayout.join(\", \")}`);\n  // DEBUG.push(`  prevLayout: ${prevLayout.join(\", \")}`);\n  // DEBUG.push(`  delta: ${delta}`);\n  // DEBUG.push(`  pivotIndices: ${pivotIndices.join(\", \")}`);\n  // DEBUG.push(`  trigger: ${trigger}`);\n  // DEBUG.push(\"\");\n\n  // A resizing panel affects the panels before or after it.\n  //\n  // A negative delta means the panel(s) immediately after the resize handle should grow/expand by decreasing its offset.\n  // Other panels may also need to shrink/contract (and shift) to make room, depending on the min weights.\n  //\n  // A positive delta means the panel(s) immediately before the resize handle should \"expand\".\n  // This is accomplished by shrinking/contracting (and shifting) one or more of the panels after the resize handle.\n\n  {\n    // If this is a resize triggered by a keyboard event, our logic for expanding/collapsing is different.\n    // We no longer check the halfway threshold because this may prevent the panel from expanding at all.\n    if (trigger === \"keyboard\") {\n      {\n        // Check if we should expand a collapsed panel\n        const index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n        const panelConstraints = panelConstraintsArray[index];\n        assert(panelConstraints, `Panel constraints not found for index ${index}`);\n        const {\n          collapsedSize = 0,\n          collapsible,\n          minSize = 0\n        } = panelConstraints;\n\n        // DEBUG.push(`edge case check 1: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${collapsible}`);\n        if (collapsible) {\n          const prevSize = initialLayout[index];\n          assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n          if (fuzzyNumbersEqual(prevSize, collapsedSize)) {\n            const localDelta = minSize - prevSize;\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              // DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n\n      {\n        // Check if we should collapse a panel at its minimum size\n        const index = delta < 0 ? firstPivotIndex : secondPivotIndex;\n        const panelConstraints = panelConstraintsArray[index];\n        assert(panelConstraints, `No panel constraints found for index ${index}`);\n        const {\n          collapsedSize = 0,\n          collapsible,\n          minSize = 0\n        } = panelConstraints;\n\n        // DEBUG.push(`edge case check 2: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${collapsible}`);\n        if (collapsible) {\n          const prevSize = initialLayout[index];\n          assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n          if (fuzzyNumbersEqual(prevSize, minSize)) {\n            const localDelta = prevSize - collapsedSize;\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              // DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n    }\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Pre-calculate max available delta in the opposite direction of our pivot.\n    // This will be the maximum amount we're allowed to expand/contract the panels in the primary direction.\n    // If this amount is less than the requested delta, adjust the requested delta.\n    // If this amount is greater than the requested delta, that's useful information too–\n    // as an expanding panel might change from collapsed to min size.\n\n    const increment = delta < 0 ? 1 : -1;\n    let index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    let maxAvailableDelta = 0;\n\n    // DEBUG.push(\"pre calc...\");\n    while (true) {\n      const prevSize = initialLayout[index];\n      assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n      const maxSafeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: 100\n      });\n      const delta = maxSafeSize - prevSize;\n      // DEBUG.push(`  ${index}: ${prevSize} -> ${maxSafeSize}`);\n\n      maxAvailableDelta += delta;\n      index += increment;\n      if (index < 0 || index >= panelConstraintsArray.length) {\n        break;\n      }\n    }\n\n    // DEBUG.push(`  -> max available delta: ${maxAvailableDelta}`);\n    const minAbsDelta = Math.min(Math.abs(delta), Math.abs(maxAvailableDelta));\n    delta = delta < 0 ? 0 - minAbsDelta : minAbsDelta;\n    // DEBUG.push(`  -> adjusted delta: ${delta}`);\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Delta added to a panel needs to be subtracted from other panels (within the constraints that those panels allow).\n\n    const pivotIndex = delta < 0 ? firstPivotIndex : secondPivotIndex;\n    let index = pivotIndex;\n    while (index >= 0 && index < panelConstraintsArray.length) {\n      const deltaRemaining = Math.abs(delta) - Math.abs(deltaApplied);\n      const prevSize = initialLayout[index];\n      assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n      const unsafeSize = prevSize - deltaRemaining;\n      const safeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n        deltaApplied += prevSize - safeSize;\n        nextLayout[index] = safeSize;\n        if (deltaApplied.toPrecision(3).localeCompare(Math.abs(delta).toPrecision(3), undefined, {\n          numeric: true\n        }) >= 0) {\n          break;\n        }\n      }\n      if (delta < 0) {\n        index--;\n      } else {\n        index++;\n      }\n    }\n  }\n  // DEBUG.push(`after 1: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  // If we were unable to resize any of the panels panels, return the previous state.\n  // This will essentially bailout and ignore e.g. drags past a panel's boundaries\n  if (fuzzyLayoutsEqual(prevLayout, nextLayout)) {\n    // DEBUG.push(`bailout to previous layout: ${prevLayout.join(\", \")}`);\n    // console.log(DEBUG.join(\"\\n\"));\n\n    return prevLayout;\n  }\n  {\n    // Now distribute the applied delta to the panels in the other direction\n    const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    const prevSize = initialLayout[pivotIndex];\n    assert(prevSize != null, `Previous layout not found for panel index ${pivotIndex}`);\n    const unsafeSize = prevSize + deltaApplied;\n    const safeSize = resizePanel({\n      panelConstraints: panelConstraintsArray,\n      panelIndex: pivotIndex,\n      size: unsafeSize\n    });\n\n    // Adjust the pivot panel before, but only by the amount that surrounding panels were able to shrink/contract.\n    nextLayout[pivotIndex] = safeSize;\n\n    // Edge case where expanding or contracting one panel caused another one to change collapsed state\n    if (!fuzzyNumbersEqual(safeSize, unsafeSize)) {\n      let deltaRemaining = unsafeSize - safeSize;\n      const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n      let index = pivotIndex;\n      while (index >= 0 && index < panelConstraintsArray.length) {\n        const prevSize = nextLayout[index];\n        assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n        const unsafeSize = prevSize + deltaRemaining;\n        const safeSize = resizePanel({\n          panelConstraints: panelConstraintsArray,\n          panelIndex: index,\n          size: unsafeSize\n        });\n        if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n          deltaRemaining -= safeSize - prevSize;\n          nextLayout[index] = safeSize;\n        }\n        if (fuzzyNumbersEqual(deltaRemaining, 0)) {\n          break;\n        }\n        if (delta > 0) {\n          index--;\n        } else {\n          index++;\n        }\n      }\n    }\n  }\n  // DEBUG.push(`after 2: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  const totalSize = nextLayout.reduce((total, size) => size + total, 0);\n  // DEBUG.push(`total size: ${totalSize}`);\n\n  // If our new layout doesn't add up to 100%, that means the requested delta can't be applied\n  // In that case, fall back to our most recent valid layout\n  if (!fuzzyNumbersEqual(totalSize, 100)) {\n    // DEBUG.push(`bailout to previous layout: ${prevLayout.join(\", \")}`);\n    // console.log(DEBUG.join(\"\\n\"));\n\n    return prevLayout;\n  }\n\n  // console.log(DEBUG.join(\"\\n\"));\n  return nextLayout;\n}\n\nfunction getResizeHandleElementsForGroup(groupId, scope = document) {\n  return Array.from(scope.querySelectorAll(`[${DATA_ATTRIBUTES.resizeHandleId}][data-panel-group-id=\"${groupId}\"]`));\n}\n\nfunction getResizeHandleElementIndex(groupId, id, scope = document) {\n  const handles = getResizeHandleElementsForGroup(groupId, scope);\n  const index = handles.findIndex(handle => handle.getAttribute(DATA_ATTRIBUTES.resizeHandleId) === id);\n  return index !== null && index !== void 0 ? index : null;\n}\n\nfunction determinePivotIndices(groupId, dragHandleId, panelGroupElement) {\n  const index = getResizeHandleElementIndex(groupId, dragHandleId, panelGroupElement);\n  return index != null ? [index, index + 1] : [-1, -1];\n}\n\nfunction getPanelGroupElement(id, rootElement = document) {\n  var _dataset;\n  //If the root element is the PanelGroup\n  if (rootElement instanceof HTMLElement && (rootElement === null || rootElement === void 0 ? void 0 : (_dataset = rootElement.dataset) === null || _dataset === void 0 ? void 0 : _dataset.panelGroupId) == id) {\n    return rootElement;\n  }\n\n  //Else query children\n  const element = rootElement.querySelector(`[data-panel-group][data-panel-group-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getResizeHandleElement(id, scope = document) {\n  const element = scope.querySelector(`[${DATA_ATTRIBUTES.resizeHandleId}=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getResizeHandlePanelIds(groupId, handleId, panelsArray, scope = document) {\n  var _panelsArray$index$id, _panelsArray$index, _panelsArray$id, _panelsArray;\n  const handle = getResizeHandleElement(handleId, scope);\n  const handles = getResizeHandleElementsForGroup(groupId, scope);\n  const index = handle ? handles.indexOf(handle) : -1;\n  const idBefore = (_panelsArray$index$id = (_panelsArray$index = panelsArray[index]) === null || _panelsArray$index === void 0 ? void 0 : _panelsArray$index.id) !== null && _panelsArray$index$id !== void 0 ? _panelsArray$index$id : null;\n  const idAfter = (_panelsArray$id = (_panelsArray = panelsArray[index + 1]) === null || _panelsArray === void 0 ? void 0 : _panelsArray.id) !== null && _panelsArray$id !== void 0 ? _panelsArray$id : null;\n  return [idBefore, idAfter];\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterPanelGroupBehavior({\n  committedValuesRef,\n  eagerValuesRef,\n  groupId,\n  layout,\n  panelDataArray,\n  panelGroupElement,\n  setLayout\n}) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    didWarnAboutMissingResizeHandle: false\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!panelGroupElement) {\n      return;\n    }\n    const eagerValues = eagerValuesRef.current;\n    assert(eagerValues, `Eager values not found`);\n    const {\n      panelDataArray\n    } = eagerValues;\n    const groupElement = getPanelGroupElement(groupId, panelGroupElement);\n    assert(groupElement != null, `No group found for id \"${groupId}\"`);\n    const handles = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n    assert(handles, `No resize handles found for group id \"${groupId}\"`);\n    const cleanupFunctions = handles.map(handle => {\n      const handleId = handle.getAttribute(DATA_ATTRIBUTES.resizeHandleId);\n      assert(handleId, `Resize handle element has no handle id attribute`);\n      const [idBefore, idAfter] = getResizeHandlePanelIds(groupId, handleId, panelDataArray, panelGroupElement);\n      if (idBefore == null || idAfter == null) {\n        return () => {};\n      }\n      const onKeyDown = event => {\n        if (event.defaultPrevented) {\n          return;\n        }\n        switch (event.key) {\n          case \"Enter\":\n            {\n              event.preventDefault();\n              const index = panelDataArray.findIndex(panelData => panelData.id === idBefore);\n              if (index >= 0) {\n                const panelData = panelDataArray[index];\n                assert(panelData, `No panel data found for index ${index}`);\n                const size = layout[index];\n                const {\n                  collapsedSize = 0,\n                  collapsible,\n                  minSize = 0\n                } = panelData.constraints;\n                if (size != null && collapsible) {\n                  const nextLayout = adjustLayoutByDelta({\n                    delta: fuzzyNumbersEqual(size, collapsedSize) ? minSize - collapsedSize : collapsedSize - size,\n                    initialLayout: layout,\n                    panelConstraints: panelDataArray.map(panelData => panelData.constraints),\n                    pivotIndices: determinePivotIndices(groupId, handleId, panelGroupElement),\n                    prevLayout: layout,\n                    trigger: \"keyboard\"\n                  });\n                  if (layout !== nextLayout) {\n                    setLayout(nextLayout);\n                  }\n                }\n              }\n              break;\n            }\n        }\n      };\n      handle.addEventListener(\"keydown\", onKeyDown);\n      return () => {\n        handle.removeEventListener(\"keydown\", onKeyDown);\n      };\n    });\n    return () => {\n      cleanupFunctions.forEach(cleanupFunction => cleanupFunction());\n    };\n  }, [panelGroupElement, committedValuesRef, eagerValuesRef, groupId, layout, panelDataArray, setLayout]);\n}\n\nfunction areEqual(arrayA, arrayB) {\n  if (arrayA.length !== arrayB.length) {\n    return false;\n  }\n  for (let index = 0; index < arrayA.length; index++) {\n    if (arrayA[index] !== arrayB[index]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction getResizeEventCursorPosition(direction, event) {\n  const isHorizontal = direction === \"horizontal\";\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  return isHorizontal ? x : y;\n}\n\nfunction calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState, panelGroupElement) {\n  const isHorizontal = direction === \"horizontal\";\n  const handleElement = getResizeHandleElement(dragHandleId, panelGroupElement);\n  assert(handleElement, `No resize handle element found for id \"${dragHandleId}\"`);\n  const groupId = handleElement.getAttribute(DATA_ATTRIBUTES.groupId);\n  assert(groupId, `Resize handle element has no group id attribute`);\n  let {\n    initialCursorPosition\n  } = initialDragState;\n  const cursorPosition = getResizeEventCursorPosition(direction, event);\n  const groupElement = getPanelGroupElement(groupId, panelGroupElement);\n  assert(groupElement, `No group element found for id \"${groupId}\"`);\n  const groupRect = groupElement.getBoundingClientRect();\n  const groupSizeInPixels = isHorizontal ? groupRect.width : groupRect.height;\n  const offsetPixels = cursorPosition - initialCursorPosition;\n  const offsetPercentage = offsetPixels / groupSizeInPixels * 100;\n  return offsetPercentage;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/movementX\nfunction calculateDeltaPercentage(event, dragHandleId, direction, initialDragState, keyboardResizeBy, panelGroupElement) {\n  if (isKeyDown(event)) {\n    const isHorizontal = direction === \"horizontal\";\n    let delta = 0;\n    if (event.shiftKey) {\n      delta = 100;\n    } else if (keyboardResizeBy != null) {\n      delta = keyboardResizeBy;\n    } else {\n      delta = 10;\n    }\n    let movement = 0;\n    switch (event.key) {\n      case \"ArrowDown\":\n        movement = isHorizontal ? 0 : delta;\n        break;\n      case \"ArrowLeft\":\n        movement = isHorizontal ? -delta : 0;\n        break;\n      case \"ArrowRight\":\n        movement = isHorizontal ? delta : 0;\n        break;\n      case \"ArrowUp\":\n        movement = isHorizontal ? 0 : -delta;\n        break;\n      case \"End\":\n        movement = 100;\n        break;\n      case \"Home\":\n        movement = -100;\n        break;\n    }\n    return movement;\n  } else {\n    if (initialDragState == null) {\n      return 0;\n    }\n    return calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState, panelGroupElement);\n  }\n}\n\n// Layout should be pre-converted into percentages\nfunction callPanelCallbacks(panelsArray, layout, panelIdToLastNotifiedSizeMap) {\n  layout.forEach((size, index) => {\n    const panelData = panelsArray[index];\n    assert(panelData, `Panel data not found for index ${index}`);\n    const {\n      callbacks,\n      constraints,\n      id: panelId\n    } = panelData;\n    const {\n      collapsedSize = 0,\n      collapsible\n    } = constraints;\n    const lastNotifiedSize = panelIdToLastNotifiedSizeMap[panelId];\n    if (lastNotifiedSize == null || size !== lastNotifiedSize) {\n      panelIdToLastNotifiedSizeMap[panelId] = size;\n      const {\n        onCollapse,\n        onExpand,\n        onResize\n      } = callbacks;\n      if (onResize) {\n        onResize(size, lastNotifiedSize);\n      }\n      if (collapsible && (onCollapse || onExpand)) {\n        if (onExpand && (lastNotifiedSize == null || fuzzyNumbersEqual$1(lastNotifiedSize, collapsedSize)) && !fuzzyNumbersEqual$1(size, collapsedSize)) {\n          onExpand();\n        }\n        if (onCollapse && (lastNotifiedSize == null || !fuzzyNumbersEqual$1(lastNotifiedSize, collapsedSize)) && fuzzyNumbersEqual$1(size, collapsedSize)) {\n          onCollapse();\n        }\n      }\n    }\n  });\n}\n\nfunction compareLayouts(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  } else {\n    for (let index = 0; index < a.length; index++) {\n      if (a[index] != b[index]) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\n// This method returns a number between 1 and 100 representing\n\n// the % of the group's overall space this panel should occupy.\nfunction computePanelFlexBoxStyle({\n  defaultSize,\n  dragState,\n  layout,\n  panelData,\n  panelIndex,\n  precision = 3\n}) {\n  const size = layout[panelIndex];\n  let flexGrow;\n  if (size == null) {\n    // Initial render (before panels have registered themselves)\n    // In order to support server rendering, fall back to default size if provided\n    flexGrow = defaultSize != undefined ? defaultSize.toPrecision(precision) : \"1\";\n  } else if (panelData.length === 1) {\n    // Special case: Single panel group should always fill full width/height\n    flexGrow = \"1\";\n  } else {\n    flexGrow = size.toPrecision(precision);\n  }\n  return {\n    flexBasis: 0,\n    flexGrow,\n    flexShrink: 1,\n    // Without this, Panel sizes may be unintentionally overridden by their content\n    overflow: \"hidden\",\n    // Disable pointer events inside of a panel during resize\n    // This avoid edge cases like nested iframes\n    pointerEvents: dragState !== null ? \"none\" : undefined\n  };\n}\n\nfunction debounce(callback, durationMs = 10) {\n  let timeoutId = null;\n  let callable = (...args) => {\n    if (timeoutId !== null) {\n      clearTimeout(timeoutId);\n    }\n    timeoutId = setTimeout(() => {\n      callback(...args);\n    }, durationMs);\n  };\n  return callable;\n}\n\n// PanelGroup might be rendering in a server-side environment where localStorage is not available\n// or on a browser with cookies/storage disabled.\n// In either case, this function avoids accessing localStorage until needed,\n// and avoids throwing user-visible errors.\nfunction initializeDefaultStorage(storageObject) {\n  try {\n    if (typeof localStorage !== \"undefined\") {\n      // Bypass this check for future calls\n      storageObject.getItem = name => {\n        return localStorage.getItem(name);\n      };\n      storageObject.setItem = (name, value) => {\n        localStorage.setItem(name, value);\n      };\n    } else {\n      throw new Error(\"localStorage not supported in this environment\");\n    }\n  } catch (error) {\n    console.error(error);\n    storageObject.getItem = () => null;\n    storageObject.setItem = () => {};\n  }\n}\n\nfunction getPanelGroupKey(autoSaveId) {\n  return `react-resizable-panels:${autoSaveId}`;\n}\n\n// Note that Panel ids might be user-provided (stable) or useId generated (non-deterministic)\n// so they should not be used as part of the serialization key.\n// Using the min/max size attributes should work well enough as a backup.\n// Pre-sorting by minSize allows remembering layouts even if panels are re-ordered/dragged.\nfunction getPanelKey(panels) {\n  return panels.map(panel => {\n    const {\n      constraints,\n      id,\n      idIsFromProps,\n      order\n    } = panel;\n    if (idIsFromProps) {\n      return id;\n    } else {\n      return order ? `${order}:${JSON.stringify(constraints)}` : JSON.stringify(constraints);\n    }\n  }).sort((a, b) => a.localeCompare(b)).join(\",\");\n}\nfunction loadSerializedPanelGroupState(autoSaveId, storage) {\n  try {\n    const panelGroupKey = getPanelGroupKey(autoSaveId);\n    const serialized = storage.getItem(panelGroupKey);\n    if (serialized) {\n      const parsed = JSON.parse(serialized);\n      if (typeof parsed === \"object\" && parsed != null) {\n        return parsed;\n      }\n    }\n  } catch (error) {}\n  return null;\n}\nfunction savePanelGroupState(autoSaveId, panels, panelSizesBeforeCollapse, sizes, storage) {\n  var _loadSerializedPanelG2;\n  const panelGroupKey = getPanelGroupKey(autoSaveId);\n  const panelKey = getPanelKey(panels);\n  const state = (_loadSerializedPanelG2 = loadSerializedPanelGroupState(autoSaveId, storage)) !== null && _loadSerializedPanelG2 !== void 0 ? _loadSerializedPanelG2 : {};\n  state[panelKey] = {\n    expandToSizes: Object.fromEntries(panelSizesBeforeCollapse.entries()),\n    layout: sizes\n  };\n  try {\n    storage.setItem(panelGroupKey, JSON.stringify(state));\n  } catch (error) {\n    console.error(error);\n  }\n}\n\nfunction validatePanelConstraints({\n  panelConstraints: panelConstraintsArray,\n  panelId,\n  panelIndex\n}) {\n  {\n    const warnings = [];\n    const panelConstraints = panelConstraintsArray[panelIndex];\n    assert(panelConstraints, `No panel constraints found for index ${panelIndex}`);\n    const {\n      collapsedSize = 0,\n      collapsible = false,\n      defaultSize,\n      maxSize = 100,\n      minSize = 0\n    } = panelConstraints;\n    if (minSize > maxSize) {\n      warnings.push(`min size (${minSize}%) should not be greater than max size (${maxSize}%)`);\n    }\n    if (defaultSize != null) {\n      if (defaultSize < 0) {\n        warnings.push(\"default size should not be less than 0\");\n      } else if (defaultSize < minSize && (!collapsible || defaultSize !== collapsedSize)) {\n        warnings.push(\"default size should not be less than min size\");\n      }\n      if (defaultSize > 100) {\n        warnings.push(\"default size should not be greater than 100\");\n      } else if (defaultSize > maxSize) {\n        warnings.push(\"default size should not be greater than max size\");\n      }\n    }\n    if (collapsedSize > minSize) {\n      warnings.push(\"collapsed size should not be greater than min size\");\n    }\n    if (warnings.length > 0) {\n      const name = panelId != null ? `Panel \"${panelId}\"` : \"Panel\";\n      console.warn(`${name} has an invalid configuration:\\n\\n${warnings.join(\"\\n\")}`);\n      return false;\n    }\n  }\n  return true;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction validatePanelGroupLayout({\n  layout: prevLayout,\n  panelConstraints\n}) {\n  const nextLayout = [...prevLayout];\n  const nextLayoutTotalSize = nextLayout.reduce((accumulated, current) => accumulated + current, 0);\n\n  // Validate layout expectations\n  if (nextLayout.length !== panelConstraints.length) {\n    throw Error(`Invalid ${panelConstraints.length} panel layout: ${nextLayout.map(size => `${size}%`).join(\", \")}`);\n  } else if (!fuzzyNumbersEqual(nextLayoutTotalSize, 100) && nextLayout.length > 0) {\n    // This is not ideal so we should warn about it, but it may be recoverable in some cases\n    // (especially if the amount is small)\n    {\n      console.warn(`WARNING: Invalid layout total size: ${nextLayout.map(size => `${size}%`).join(\", \")}. Layout normalization will be applied.`);\n    }\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const unsafeSize = nextLayout[index];\n      assert(unsafeSize != null, `No layout data found for index ${index}`);\n      const safeSize = 100 / nextLayoutTotalSize * unsafeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n  let remainingSize = 0;\n\n  // First pass: Validate the proposed layout given each panel's constraints\n  for (let index = 0; index < panelConstraints.length; index++) {\n    const unsafeSize = nextLayout[index];\n    assert(unsafeSize != null, `No layout data found for index ${index}`);\n    const safeSize = resizePanel({\n      panelConstraints,\n      panelIndex: index,\n      size: unsafeSize\n    });\n    if (unsafeSize != safeSize) {\n      remainingSize += unsafeSize - safeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n\n  // If there is additional, left over space, assign it to any panel(s) that permits it\n  // (It's not worth taking multiple additional passes to evenly distribute)\n  if (!fuzzyNumbersEqual(remainingSize, 0)) {\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const prevSize = nextLayout[index];\n      assert(prevSize != null, `No layout data found for index ${index}`);\n      const unsafeSize = prevSize + remainingSize;\n      const safeSize = resizePanel({\n        panelConstraints,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (prevSize !== safeSize) {\n        remainingSize -= safeSize - prevSize;\n        nextLayout[index] = safeSize;\n\n        // Once we've used up the remainder, bail\n        if (fuzzyNumbersEqual(remainingSize, 0)) {\n          break;\n        }\n      }\n    }\n  }\n  return nextLayout;\n}\n\nconst LOCAL_STORAGE_DEBOUNCE_INTERVAL = 100;\nconst defaultStorage = {\n  getItem: name => {\n    initializeDefaultStorage(defaultStorage);\n    return defaultStorage.getItem(name);\n  },\n  setItem: (name, value) => {\n    initializeDefaultStorage(defaultStorage);\n    defaultStorage.setItem(name, value);\n  }\n};\nconst debounceMap = {};\nfunction PanelGroupWithForwardedRef({\n  autoSaveId = null,\n  children,\n  className: classNameFromProps = \"\",\n  direction,\n  forwardedRef,\n  id: idFromProps = null,\n  onLayout = null,\n  keyboardResizeBy = null,\n  storage = defaultStorage,\n  style: styleFromProps,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  const groupId = useUniqueId(idFromProps);\n  const panelGroupElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [dragState, setDragState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [layout, setLayout] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const forceUpdate = useForceUpdate();\n  const panelIdToLastNotifiedSizeMapRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n  const panelSizeBeforeCollapseRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new Map());\n  const prevDeltaRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n  const committedValuesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    autoSaveId,\n    direction,\n    dragState,\n    id: groupId,\n    keyboardResizeBy,\n    onLayout,\n    storage\n  });\n  const eagerValuesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    layout,\n    panelDataArray: [],\n    panelDataArrayChanged: false\n  });\n  const devWarningsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    didLogIdAndOrderWarning: false,\n    didLogPanelConstraintsWarning: false,\n    prevPanelIds: []\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(forwardedRef, () => ({\n    getId: () => committedValuesRef.current.id,\n    getLayout: () => {\n      const {\n        layout\n      } = eagerValuesRef.current;\n      return layout;\n    },\n    setLayout: unsafeLayout => {\n      const {\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const safeLayout = validatePanelGroupLayout({\n        layout: unsafeLayout,\n        panelConstraints: panelDataArray.map(panelData => panelData.constraints)\n      });\n      if (!areEqual(prevLayout, safeLayout)) {\n        setLayout(safeLayout);\n        eagerValuesRef.current.layout = safeLayout;\n        if (onLayout) {\n          onLayout(safeLayout);\n        }\n        callPanelCallbacks(panelDataArray, safeLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    }\n  }), []);\n  useWindowSplitterPanelGroupBehavior({\n    committedValuesRef,\n    eagerValuesRef,\n    groupId,\n    layout,\n    panelDataArray: eagerValuesRef.current.panelDataArray,\n    setLayout,\n    panelGroupElement: panelGroupElementRef.current\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n\n    // If this panel has been configured to persist sizing information, save sizes to local storage.\n    if (autoSaveId) {\n      if (layout.length === 0 || layout.length !== panelDataArray.length) {\n        return;\n      }\n      let debouncedSave = debounceMap[autoSaveId];\n\n      // Limit the frequency of localStorage updates.\n      if (debouncedSave == null) {\n        debouncedSave = debounce(savePanelGroupState, LOCAL_STORAGE_DEBOUNCE_INTERVAL);\n        debounceMap[autoSaveId] = debouncedSave;\n      }\n\n      // Clone mutable data before passing to the debounced function,\n      // else we run the risk of saving an incorrect combination of mutable and immutable values to state.\n      const clonedPanelDataArray = [...panelDataArray];\n      const clonedPanelSizesBeforeCollapse = new Map(panelSizeBeforeCollapseRef.current);\n      debouncedSave(autoSaveId, clonedPanelDataArray, clonedPanelSizesBeforeCollapse, layout, storage);\n    }\n  }, [autoSaveId, layout, storage]);\n\n  // DEV warnings\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    {\n      const {\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        didLogIdAndOrderWarning,\n        didLogPanelConstraintsWarning,\n        prevPanelIds\n      } = devWarningsRef.current;\n      if (!didLogIdAndOrderWarning) {\n        const panelIds = panelDataArray.map(({\n          id\n        }) => id);\n        devWarningsRef.current.prevPanelIds = panelIds;\n        const panelsHaveChanged = prevPanelIds.length > 0 && !areEqual(prevPanelIds, panelIds);\n        if (panelsHaveChanged) {\n          if (panelDataArray.find(({\n            idIsFromProps,\n            order\n          }) => !idIsFromProps || order == null)) {\n            devWarningsRef.current.didLogIdAndOrderWarning = true;\n            console.warn(`WARNING: Panel id and order props recommended when panels are dynamically rendered`);\n          }\n        }\n      }\n      if (!didLogPanelConstraintsWarning) {\n        const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n        for (let panelIndex = 0; panelIndex < panelConstraints.length; panelIndex++) {\n          const panelData = panelDataArray[panelIndex];\n          assert(panelData, `Panel data not found for index ${panelIndex}`);\n          const isValid = validatePanelConstraints({\n            panelConstraints,\n            panelId: panelData.id,\n            panelIndex\n          });\n          if (!isValid) {\n            devWarningsRef.current.didLogPanelConstraintsWarning = true;\n            break;\n          }\n        }\n      }\n    }\n  });\n\n  // External APIs are safe to memoize via committed values ref\n  const collapsePanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSize = 0,\n        panelSize,\n        pivotIndices\n      } = panelDataHelper(panelDataArray, panelData, prevLayout);\n      assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n      if (!fuzzyNumbersEqual$1(panelSize, collapsedSize)) {\n        // Store size before collapse;\n        // This is the size that gets restored if the expand() API is used.\n        panelSizeBeforeCollapseRef.current.set(panelData.id, panelSize);\n        const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSize - collapsedSize : collapsedSize - panelSize;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          initialLayout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          prevLayout,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout);\n          }\n          callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n        }\n      }\n    }\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const expandPanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, minSizeOverride) => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSize = 0,\n        panelSize = 0,\n        minSize: minSizeFromProps = 0,\n        pivotIndices\n      } = panelDataHelper(panelDataArray, panelData, prevLayout);\n      const minSize = minSizeOverride !== null && minSizeOverride !== void 0 ? minSizeOverride : minSizeFromProps;\n      if (fuzzyNumbersEqual$1(panelSize, collapsedSize)) {\n        // Restore this panel to the size it was before it was collapsed, if possible.\n        const prevPanelSize = panelSizeBeforeCollapseRef.current.get(panelData.id);\n        const baseSize = prevPanelSize != null && prevPanelSize >= minSize ? prevPanelSize : minSize;\n        const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSize - baseSize : baseSize - panelSize;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          initialLayout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          prevLayout,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout);\n          }\n          callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n        }\n      }\n    }\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const getPanelSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return panelSize;\n  }, []);\n\n  // This API should never read from committedValuesRef\n  const getPanelStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, defaultSize) => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelIndex = findPanelDataIndex(panelDataArray, panelData);\n    return computePanelFlexBoxStyle({\n      defaultSize,\n      dragState,\n      layout,\n      panelData: panelDataArray,\n      panelIndex\n    });\n  }, [dragState, layout]);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelCollapsed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize = 0,\n      collapsible,\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return collapsible === true && fuzzyNumbersEqual$1(panelSize, collapsedSize);\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelExpanded = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize = 0,\n      collapsible,\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return !collapsible || fuzzyCompareNumbers(panelSize, collapsedSize) > 0;\n  }, []);\n  const registerPanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    panelDataArray.push(panelData);\n    panelDataArray.sort((panelA, panelB) => {\n      const orderA = panelA.order;\n      const orderB = panelB.order;\n      if (orderA == null && orderB == null) {\n        return 0;\n      } else if (orderA == null) {\n        return -1;\n      } else if (orderB == null) {\n        return 1;\n      } else {\n        return orderA - orderB;\n      }\n    });\n    eagerValuesRef.current.panelDataArrayChanged = true;\n    forceUpdate();\n  }, [forceUpdate]);\n  const registerResizeHandle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(dragHandleId => {\n    let isRTL = false;\n    const panelGroupElement = panelGroupElementRef.current;\n    if (panelGroupElement) {\n      const style = window.getComputedStyle(panelGroupElement, null);\n      if (style.getPropertyValue(\"direction\") === \"rtl\") {\n        isRTL = true;\n      }\n    }\n    return function resizeHandler(event) {\n      event.preventDefault();\n      const panelGroupElement = panelGroupElementRef.current;\n      if (!panelGroupElement) {\n        return () => null;\n      }\n      const {\n        direction,\n        dragState,\n        id: groupId,\n        keyboardResizeBy,\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        initialLayout\n      } = dragState !== null && dragState !== void 0 ? dragState : {};\n      const pivotIndices = determinePivotIndices(groupId, dragHandleId, panelGroupElement);\n      let delta = calculateDeltaPercentage(event, dragHandleId, direction, dragState, keyboardResizeBy, panelGroupElement);\n      const isHorizontal = direction === \"horizontal\";\n      if (isHorizontal && isRTL) {\n        delta = -delta;\n      }\n      const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n      const nextLayout = adjustLayoutByDelta({\n        delta,\n        initialLayout: initialLayout !== null && initialLayout !== void 0 ? initialLayout : prevLayout,\n        panelConstraints,\n        pivotIndices,\n        prevLayout,\n        trigger: isKeyDown(event) ? \"keyboard\" : \"mouse-or-touch\"\n      });\n      const layoutChanged = !compareLayouts(prevLayout, nextLayout);\n\n      // Only update the cursor for layout changes triggered by touch/mouse events (not keyboard)\n      // Update the cursor even if the layout hasn't changed (we may need to show an invalid cursor state)\n      if (isPointerEvent(event) || isMouseEvent(event)) {\n        // Watch for multiple subsequent deltas; this might occur for tiny cursor movements.\n        // In this case, Panel sizes might not change–\n        // but updating cursor in this scenario would cause a flicker.\n        if (prevDeltaRef.current != delta) {\n          prevDeltaRef.current = delta;\n          if (!layoutChanged && delta !== 0) {\n            // If the pointer has moved too far to resize the panel any further, note this so we can update the cursor.\n            // This mimics VS Code behavior.\n            if (isHorizontal) {\n              reportConstraintsViolation(dragHandleId, delta < 0 ? EXCEEDED_HORIZONTAL_MIN : EXCEEDED_HORIZONTAL_MAX);\n            } else {\n              reportConstraintsViolation(dragHandleId, delta < 0 ? EXCEEDED_VERTICAL_MIN : EXCEEDED_VERTICAL_MAX);\n            }\n          } else {\n            reportConstraintsViolation(dragHandleId, 0);\n          }\n        }\n      }\n      if (layoutChanged) {\n        setLayout(nextLayout);\n        eagerValuesRef.current.layout = nextLayout;\n        if (onLayout) {\n          onLayout(nextLayout);\n        }\n        callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    };\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const resizePanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, unsafePanelSize) => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n    const {\n      panelSize,\n      pivotIndices\n    } = panelDataHelper(panelDataArray, panelData, prevLayout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n    const delta = isLastPanel ? panelSize - unsafePanelSize : unsafePanelSize - panelSize;\n    const nextLayout = adjustLayoutByDelta({\n      delta,\n      initialLayout: prevLayout,\n      panelConstraints: panelConstraintsArray,\n      pivotIndices,\n      prevLayout,\n      trigger: \"imperative-api\"\n    });\n    if (!compareLayouts(prevLayout, nextLayout)) {\n      setLayout(nextLayout);\n      eagerValuesRef.current.layout = nextLayout;\n      if (onLayout) {\n        onLayout(nextLayout);\n      }\n      callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n    }\n  }, []);\n  const reevaluatePanelConstraints = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, prevConstraints) => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize: prevCollapsedSize = 0,\n      collapsible: prevCollapsible\n    } = prevConstraints;\n    const {\n      collapsedSize: nextCollapsedSize = 0,\n      collapsible: nextCollapsible,\n      maxSize: nextMaxSize = 100,\n      minSize: nextMinSize = 0\n    } = panelData.constraints;\n    const {\n      panelSize: prevPanelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    if (prevPanelSize == null) {\n      // It's possible that the panels in this group have changed since the last render\n      return;\n    }\n    if (prevCollapsible && nextCollapsible && fuzzyNumbersEqual$1(prevPanelSize, prevCollapsedSize)) {\n      if (!fuzzyNumbersEqual$1(prevCollapsedSize, nextCollapsedSize)) {\n        resizePanel(panelData, nextCollapsedSize);\n      }\n    } else if (prevPanelSize < nextMinSize) {\n      resizePanel(panelData, nextMinSize);\n    } else if (prevPanelSize > nextMaxSize) {\n      resizePanel(panelData, nextMaxSize);\n    }\n  }, [resizePanel]);\n\n  // TODO Multiple drag handles can be active at the same time so this API is a bit awkward now\n  const startDragging = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((dragHandleId, event) => {\n    const {\n      direction\n    } = committedValuesRef.current;\n    const {\n      layout\n    } = eagerValuesRef.current;\n    if (!panelGroupElementRef.current) {\n      return;\n    }\n    const handleElement = getResizeHandleElement(dragHandleId, panelGroupElementRef.current);\n    assert(handleElement, `Drag handle element not found for id \"${dragHandleId}\"`);\n    const initialCursorPosition = getResizeEventCursorPosition(direction, event);\n    setDragState({\n      dragHandleId,\n      dragHandleRect: handleElement.getBoundingClientRect(),\n      initialCursorPosition,\n      initialLayout: layout\n    });\n  }, []);\n  const stopDragging = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setDragState(null);\n  }, []);\n  const unregisterPanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const index = findPanelDataIndex(panelDataArray, panelData);\n    if (index >= 0) {\n      panelDataArray.splice(index, 1);\n\n      // TRICKY\n      // When a panel is removed from the group, we should delete the most recent prev-size entry for it.\n      // If we don't do this, then a conditionally rendered panel might not call onResize when it's re-mounted.\n      // Strict effects mode makes this tricky though because all panels will be registered, unregistered, then re-registered on mount.\n      delete panelIdToLastNotifiedSizeMapRef.current[panelData.id];\n      eagerValuesRef.current.panelDataArrayChanged = true;\n      forceUpdate();\n    }\n  }, [forceUpdate]);\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    collapsePanel,\n    direction,\n    dragState,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    isPanelExpanded,\n    reevaluatePanelConstraints,\n    registerPanel,\n    registerResizeHandle,\n    resizePanel,\n    startDragging,\n    stopDragging,\n    unregisterPanel,\n    panelGroupElement: panelGroupElementRef.current\n  }), [collapsePanel, dragState, direction, expandPanel, getPanelSize, getPanelStyle, groupId, isPanelCollapsed, isPanelExpanded, reevaluatePanelConstraints, registerPanel, registerResizeHandle, resizePanel, startDragging, stopDragging, unregisterPanel]);\n  const style = {\n    display: \"flex\",\n    flexDirection: direction === \"horizontal\" ? \"row\" : \"column\",\n    height: \"100%\",\n    overflow: \"hidden\",\n    width: \"100%\"\n  };\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(PanelGroupContext.Provider, {\n    value: context\n  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: idFromProps,\n    ref: panelGroupElementRef,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    // CSS selectors\n    [DATA_ATTRIBUTES.group]: \"\",\n    [DATA_ATTRIBUTES.groupDirection]: direction,\n    [DATA_ATTRIBUTES.groupId]: groupId\n  }));\n}\nconst PanelGroup = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(PanelGroupWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelGroupWithForwardedRef.displayName = \"PanelGroup\";\nPanelGroup.displayName = \"forwardRef(PanelGroup)\";\nfunction findPanelDataIndex(panelDataArray, panelData) {\n  return panelDataArray.findIndex(prevPanelData => prevPanelData === panelData || prevPanelData.id === panelData.id);\n}\nfunction panelDataHelper(panelDataArray, panelData, layout) {\n  const panelIndex = findPanelDataIndex(panelDataArray, panelData);\n  const isLastPanel = panelIndex === panelDataArray.length - 1;\n  const pivotIndices = isLastPanel ? [panelIndex - 1, panelIndex] : [panelIndex, panelIndex + 1];\n  const panelSize = layout[panelIndex];\n  return {\n    ...panelData.constraints,\n    panelSize,\n    pivotIndices\n  };\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterResizeHandlerBehavior({\n  disabled,\n  handleId,\n  resizeHandler,\n  panelGroupElement\n}) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled || resizeHandler == null || panelGroupElement == null) {\n      return;\n    }\n    const handleElement = getResizeHandleElement(handleId, panelGroupElement);\n    if (handleElement == null) {\n      return;\n    }\n    const onKeyDown = event => {\n      if (event.defaultPrevented) {\n        return;\n      }\n      switch (event.key) {\n        case \"ArrowDown\":\n        case \"ArrowLeft\":\n        case \"ArrowRight\":\n        case \"ArrowUp\":\n        case \"End\":\n        case \"Home\":\n          {\n            event.preventDefault();\n            resizeHandler(event);\n            break;\n          }\n        case \"F6\":\n          {\n            event.preventDefault();\n            const groupId = handleElement.getAttribute(DATA_ATTRIBUTES.groupId);\n            assert(groupId, `No group element found for id \"${groupId}\"`);\n            const handles = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n            const index = getResizeHandleElementIndex(groupId, handleId, panelGroupElement);\n            assert(index !== null, `No resize element found for id \"${handleId}\"`);\n            const nextIndex = event.shiftKey ? index > 0 ? index - 1 : handles.length - 1 : index + 1 < handles.length ? index + 1 : 0;\n            const nextHandle = handles[nextIndex];\n            nextHandle.focus();\n            break;\n          }\n      }\n    };\n    handleElement.addEventListener(\"keydown\", onKeyDown);\n    return () => {\n      handleElement.removeEventListener(\"keydown\", onKeyDown);\n    };\n  }, [panelGroupElement, disabled, handleId, resizeHandler]);\n}\n\nfunction PanelResizeHandle({\n  children = null,\n  className: classNameFromProps = \"\",\n  disabled = false,\n  hitAreaMargins,\n  id: idFromProps,\n  onBlur,\n  onClick,\n  onDragging,\n  onFocus,\n  onPointerDown,\n  onPointerUp,\n  style: styleFromProps = {},\n  tabIndex = 0,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  var _hitAreaMargins$coars, _hitAreaMargins$fine;\n  const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n\n  // Use a ref to guard against users passing inline props\n  const callbacksRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    onClick,\n    onDragging,\n    onPointerDown,\n    onPointerUp\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    callbacksRef.current.onClick = onClick;\n    callbacksRef.current.onDragging = onDragging;\n    callbacksRef.current.onPointerDown = onPointerDown;\n    callbacksRef.current.onPointerUp = onPointerUp;\n  });\n  const panelGroupContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(PanelGroupContext);\n  if (panelGroupContext === null) {\n    throw Error(`PanelResizeHandle components must be rendered within a PanelGroup container`);\n  }\n  const {\n    direction,\n    groupId,\n    registerResizeHandle: registerResizeHandleWithParentGroup,\n    startDragging,\n    stopDragging,\n    panelGroupElement\n  } = panelGroupContext;\n  const resizeHandleId = useUniqueId(idFromProps);\n  const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"inactive\");\n  const [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [resizeHandler, setResizeHandler] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const committedValuesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    state\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) {\n      setResizeHandler(null);\n    } else {\n      const resizeHandler = registerResizeHandleWithParentGroup(resizeHandleId);\n      setResizeHandler(() => resizeHandler);\n    }\n  }, [disabled, resizeHandleId, registerResizeHandleWithParentGroup]);\n\n  // Extract hit area margins before passing them to the effect's dependency array\n  // so that inline object values won't trigger re-renders\n  const coarseHitAreaMargins = (_hitAreaMargins$coars = hitAreaMargins === null || hitAreaMargins === void 0 ? void 0 : hitAreaMargins.coarse) !== null && _hitAreaMargins$coars !== void 0 ? _hitAreaMargins$coars : 15;\n  const fineHitAreaMargins = (_hitAreaMargins$fine = hitAreaMargins === null || hitAreaMargins === void 0 ? void 0 : hitAreaMargins.fine) !== null && _hitAreaMargins$fine !== void 0 ? _hitAreaMargins$fine : 5;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled || resizeHandler == null) {\n      return;\n    }\n    const element = elementRef.current;\n    assert(element, \"Element ref not attached\");\n    let didMove = false;\n    const setResizeHandlerState = (action, isActive, event) => {\n      if (!isActive) {\n        setState(\"inactive\");\n        return;\n      }\n      switch (action) {\n        case \"down\":\n          {\n            setState(\"drag\");\n            didMove = false;\n            assert(event, 'Expected event to be defined for \"down\" action');\n            startDragging(resizeHandleId, event);\n            const {\n              onDragging,\n              onPointerDown\n            } = callbacksRef.current;\n            onDragging === null || onDragging === void 0 ? void 0 : onDragging(true);\n            onPointerDown === null || onPointerDown === void 0 ? void 0 : onPointerDown();\n            break;\n          }\n        case \"move\":\n          {\n            const {\n              state\n            } = committedValuesRef.current;\n            didMove = true;\n            if (state !== \"drag\") {\n              setState(\"hover\");\n            }\n            assert(event, 'Expected event to be defined for \"move\" action');\n            resizeHandler(event);\n            break;\n          }\n        case \"up\":\n          {\n            setState(\"hover\");\n            stopDragging();\n            const {\n              onClick,\n              onDragging,\n              onPointerUp\n            } = callbacksRef.current;\n            onDragging === null || onDragging === void 0 ? void 0 : onDragging(false);\n            onPointerUp === null || onPointerUp === void 0 ? void 0 : onPointerUp();\n            if (!didMove) {\n              onClick === null || onClick === void 0 ? void 0 : onClick();\n            }\n            break;\n          }\n      }\n    };\n    return registerResizeHandle(resizeHandleId, element, direction, {\n      coarse: coarseHitAreaMargins,\n      fine: fineHitAreaMargins\n    }, setResizeHandlerState);\n  }, [coarseHitAreaMargins, direction, disabled, fineHitAreaMargins, registerResizeHandleWithParentGroup, resizeHandleId, resizeHandler, startDragging, stopDragging]);\n  useWindowSplitterResizeHandlerBehavior({\n    disabled,\n    handleId: resizeHandleId,\n    resizeHandler,\n    panelGroupElement\n  });\n  const style = {\n    touchAction: \"none\",\n    userSelect: \"none\"\n  };\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: idFromProps,\n    onBlur: () => {\n      setIsFocused(false);\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur();\n    },\n    onFocus: () => {\n      setIsFocused(true);\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus();\n    },\n    ref: elementRef,\n    role: \"separator\",\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    tabIndex,\n    // CSS selectors\n    [DATA_ATTRIBUTES.groupDirection]: direction,\n    [DATA_ATTRIBUTES.groupId]: groupId,\n    [DATA_ATTRIBUTES.resizeHandle]: \"\",\n    [DATA_ATTRIBUTES.resizeHandleActive]: state === \"drag\" ? \"pointer\" : isFocused ? \"keyboard\" : undefined,\n    [DATA_ATTRIBUTES.resizeHandleEnabled]: !disabled,\n    [DATA_ATTRIBUTES.resizeHandleId]: resizeHandleId,\n    [DATA_ATTRIBUTES.resizeHandleState]: state\n  });\n}\nPanelResizeHandle.displayName = \"PanelResizeHandle\";\n\nfunction getPanelElement(id, scope = document) {\n  const element = scope.querySelector(`[data-panel-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getPanelElementsForGroup(groupId, scope = document) {\n  return Array.from(scope.querySelectorAll(`[data-panel][data-panel-group-id=\"${groupId}\"]`));\n}\n\nfunction getIntersectingRectangle(rectOne, rectTwo, strict) {\n  if (!intersects(rectOne, rectTwo, strict)) {\n    return {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n  }\n  return {\n    x: Math.max(rectOne.x, rectTwo.x),\n    y: Math.max(rectOne.y, rectTwo.y),\n    width: Math.min(rectOne.x + rectOne.width, rectTwo.x + rectTwo.width) - Math.max(rectOne.x, rectTwo.x),\n    height: Math.min(rectOne.y + rectOne.height, rectTwo.y + rectTwo.height) - Math.max(rectOne.y, rectTwo.y)\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-resizable-panels/dist/react-resizable-panels.development.node.esm.js\n");

/***/ })

};
;