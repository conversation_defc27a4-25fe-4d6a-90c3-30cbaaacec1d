
'use client';

import React, { useState, useEffect } from 'react';
import {
  Sun, Moon, Settings2, UserCircle, LogOut, Trash2, AlertTriangle,
  LayoutDashboard // Added Dashboard icon
} from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import {
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarFooter,
  SidebarSeparator,
} from '@/components/ui/sidebar';
import { useTheme } from 'next-themes';
import { cn } from '@/lib/utils';
import Image from 'next/image'; // Import next/image
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'; // Import Popover
import { Button, buttonVariants } from '@/components/ui/button'; // Import Button and buttonVariants
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"; // Import AlertDialog
import { useAuth } from '@/providers/auth-provider'; // Import useAuth
import { deleteUser } from 'firebase/auth'; // Import deleteUser
import { useToast } from '@/hooks/use-toast'; // Import useToast
import { useAIChat } from '@/providers/ai-chat-provider'; // Import AI Chat context
import { useIframePanel } from '@/providers/iframe-panel-provider'; // Import iframe panel context

// Increased SVG sizes to 24x24
const AiChatIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="currentColor"/>
    <circle cx="9" cy="12" r="1.5" fill="currentColor"/>
    <circle cx="15" cy="12" r="1.5" fill="currentColor"/>
    <path d="M12 16C10.33 16 8.82 15.18 7.82 14H16.18C15.18 15.18 13.67 16 12 16Z" fill="currentColor"/>
  </svg>
);
const SketchIcon = () => (
 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
    <circle cx="8.5" cy="8.5" r="1.5"></circle>
    <polyline points="21 15 16 10 5 21"></polyline>
 </svg>
);
const QuizIcon = () => (
 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M9 12l2 2 4-4"></path>
    <path d="M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h18z"></path>
    <path d="M3 12v7c0 .552.448 1 1 1h16c.552 0 1-.448 1-1v-7"></path>
    <circle cx="12" cy="8" r="2"></circle>
 </svg>
);

const GithubIcon = () => (
 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
 </svg>
);
const DiagramIcon = () => (
 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <rect x="3" y="3" width="7" height="7"></rect>
    <rect x="14" y="3" width="7" height="7"></rect>
    <rect x="14" y="14" width="7" height="7"></rect>
    <rect x="3" y="14" width="7" height="7"></rect>
    <line x1="10" y1="6.5" x2="14" y2="6.5"></line>
    <line x1="17.5" y1="10" x2="17.5" y2="14"></line>
    <line x1="10" y1="17.5" x2="14" y2="17.5"></line>
    <line x1="6.5" y1="10" x2="6.5" y2="14"></line>
 </svg>
);
const FormsIcon = () => (
 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
    <polyline points="14 2 14 8 20 8"></polyline>
    <line x1="16" y1="13" x2="8" y2="13"></line>
    <line x1="16" y1="17" x2="8" y2="17"></line>
    <polyline points="10 9 9 9 8 9"></polyline>
 </svg>
);

// Dashboard icon using Lucide component
const DashboardIcon = () => <LayoutDashboard className="h-6 w-6" />;

// Kept settings icons smaller for visual hierarchy
const GeneralSettingIcon = () => <Settings2 className="h-4 w-4" />;
const ProfileSettingIcon = () => <UserCircle className="h-4 w-4" />;


const LeftSidebar = () => {
   const { theme, setTheme } = useTheme();
   const [mounted, setMounted] = useState(false);
   const router = useRouter();
   const pathname = usePathname();

   // Set active button based on current path
   const [activeButton, setActiveButton] = useState('Dashboard');
   const { user, logout, authInstance, deleteUserAccount } = useAuth(); // Get user and logout function

   const { toast } = useToast(); // Get toast function
   const { toggleAIChat, isAIChatOpen } = useAIChat(); // Get AI Chat context
   const { toggleQuiz, toggleGitHub, toggleSketch, toggleDiagram, toggleChatPdf, toggleGeneralSetting, isQuizOpen, isGitHubOpen, isSketchOpen, isDiagramOpen, isChatPdfOpen, isGeneralSettingOpen, closeAllPanels } = useIframePanel(); // Get iframe panel context

   useEffect(() => {
     setMounted(true);

     // Set active button based on current path and panel states
     if (isAIChatOpen) {
       setActiveButton('AI Chat');
     } else if (isQuizOpen) {
       setActiveButton('Vice Quiz');
     } else if (isGitHubOpen) {
       setActiveButton('GitHub');
     } else if (isSketchOpen) {
       setActiveButton('Sketch');
     } else if (isDiagramOpen) {
       setActiveButton('Diagram');
     } else if (isChatPdfOpen) {
       setActiveButton('Forms');
     } else if (isGeneralSettingOpen) {
       setActiveButton('General Setting');
     } else if (pathname === '/') {
       setActiveButton('Dashboard');
     } else if (pathname.startsWith('/stack/')) {
       setActiveButton('Dashboard'); // Still highlight Dashboard when on stack pages
     }
   }, [pathname, isAIChatOpen, isQuizOpen, isGitHubOpen, isSketchOpen, isDiagramOpen, isChatPdfOpen, isGeneralSettingOpen]);

   // Enhanced theme initialization and synchronization (run only once)
   useEffect(() => {
     if (mounted && typeof window !== 'undefined') {
       // Get the actual current theme from multiple sources
       const documentHasDark = document.documentElement.classList.contains('dark');
       const storedTheme = localStorage.getItem('theme');
       const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';

       // Determine the actual current theme
       let actualTheme: string;
       if (storedTheme) {
         actualTheme = storedTheme;
       } else if (documentHasDark) {
         actualTheme = 'dark';
       } else {
         actualTheme = systemTheme;
       }

       console.log('🎨 Theme synchronization on app launch:', {
         stored: storedTheme,
         system: systemTheme,
         documentDark: documentHasDark,
         contextTheme: theme,
         actualTheme: actualTheme
       });

       // Only sync if theme is not set yet (initial load)
       if (!theme) {
         console.log('🔄 Setting initial theme:', actualTheme);
         setTheme(actualTheme);

         // Also ensure DOM is in sync
         if (actualTheme === 'dark' && !documentHasDark) {
           document.documentElement.classList.add('dark');
         } else if (actualTheme === 'light' && documentHasDark) {
           document.documentElement.classList.remove('dark');
         }
       }
     }
   }, [mounted]); // Only depend on mounted to run once

   // Theme synchronization with IDE
   useEffect(() => {
     if (typeof window !== 'undefined' && (window as any).electronAPI && (window as any).electronAPI.theme) {
       // Listen for theme changes from IDE
       const handleThemeChange = (newTheme: string) => {
         console.log('Received theme change from IDE:', newTheme);
         setTheme(newTheme);
       };

       (window as any).electronAPI.theme.onChange(handleThemeChange);

       // Get initial theme from Electron
       (window as any).electronAPI.theme.get().then((result: any) => {
         if (result.success && result.theme !== theme) {
           setTheme(result.theme);
         }
       });

       return () => {
         (window as any).electronAPI.theme.removeOnChange(handleThemeChange);
       };
     }
   }, [setTheme, theme]);

   const menuItems = [
    { icon: DashboardIcon, label: 'Dashboard', tooltip: 'Dashboard' }, // Added Dashboard as first item
    { icon: AiChatIcon, label: 'AI Chat', tooltip: 'AI Chat' },
    { icon: SketchIcon, label: 'Sketch', tooltip: 'Sketch' },
    { icon: QuizIcon, label: 'Vice Quiz', tooltip: 'Vice Quiz' },
    { icon: GithubIcon, label: 'GitHub', tooltip: 'GitHub' },
    { icon: DiagramIcon, label: 'Diagram', tooltip: 'Diagram' },
    { icon: FormsIcon, label: 'Forms', tooltip: 'Vice IDE Forms' },
  ];

   const settingsItems = [
    { icon: GeneralSettingIcon, label: 'General Setting', tooltip: 'General Settings' },
    { icon: ProfileSettingIcon, label: 'Profile Setting', tooltip: 'Profile Settings' },
  ];

   const toggleTheme = async (newTheme: 'light' | 'dark') => {
     setTheme(newTheme);

     // Sync to IDE via Electron
     if (typeof window !== 'undefined' && (window as any).electronAPI && (window as any).electronAPI.theme) {
       try {
         await (window as any).electronAPI.theme.set(newTheme);
         console.log('Theme synced to IDE:', newTheme);
       } catch (error) {
         console.error('Failed to sync theme to IDE:', error);
       }
     }
   };

   const handleDeleteAccount = async () => {
    try {
      await deleteUserAccount();
      toast({ title: "Account Deleted", description: "Your account has been successfully deleted." });
      // logout() is called internally by deleteUserAccount in the provider
    } catch (error: any) {
      console.error("Error deleting account from sidebar:", error);
      toast({
        title: "Deletion Failed",
        description: error.message || "Could not delete account. Please try again.",
        variant: "destructive",
      });
    }
  };


   if (!mounted) {
    return null;
  }

   const isDarkMode = theme === 'dark';

  return (
    <>
      {/* Main content area taking available space */}
      <SidebarContent className="flex-1 overflow-y-auto pt-2">
        {/* Logo Image */}
        <div className="flex items-center justify-center mb-6 px-1 group-data-[collapsible=icon]:hidden">
          <Image
            src="/tab.png"
            alt="VICE Logo"
            width={112}
            height={21}
            priority
          />
        </div>
        {/* Icon-only logo when collapsed */}
        <div className="items-center justify-center mb-6 mx-auto group-data-[collapsible=icon]:flex hidden">
            {/* You might want a smaller version of the logo or just the sparkle icon here */}
            <div className="h-8 w-8 rounded-full bg-sidebar-accent flex items-center justify-center text-sidebar-accent-foreground font-mono text-xs">{"✨"}</div>
        </div>


        <SidebarMenu>
          {menuItems.map((item, index) => (
            <SidebarMenuItem key={index}>
              <SidebarMenuButton
                tooltip={item.tooltip}
                className={cn(
                  "justify-start text-sm font-medium rounded-none w-full h-12", // Changed rounded-lg to rounded-none
                  activeButton === item.label ? "bg-sidebar-accent text-sidebar-accent-foreground" : "text-sidebar-foreground hover:bg-sidebar-accent/80 hover:text-sidebar-accent-foreground"
                )}
                aria-label={item.label}
                onClick={() => {
                  // Handle navigation and state changes
                  if (item.label === 'Dashboard') {
                    // Close any open panels first
                    if (isAIChatOpen) {
                      toggleAIChat();
                    }
                    if (isQuizOpen || isSketchOpen || isDiagramOpen || isChatPdfOpen || isGeneralSettingOpen) {
                      closeAllPanels();
                    }
                    setActiveButton('Dashboard');
                    router.push('/');
                  } else if (item.label === 'AI Chat') {
                    // Close iframe panels if open
                    if (isQuizOpen || isSketchOpen || isDiagramOpen || isChatPdfOpen || isGeneralSettingOpen) {
                      closeAllPanels();
                    }
                    // Toggle AI Chat
                    toggleAIChat();
                    // Active button will be set by useEffect based on isAIChatOpen state
                  } else if (item.label === 'Vice Quiz') {
                    // Close AI chat if open
                    if (isAIChatOpen) {
                      toggleAIChat();
                    }
                    // Toggle Vice Quiz iframe panel
                    toggleQuiz();
                    // Active button will be set by useEffect based on isQuizOpen state
                  } else if (item.label === 'GitHub') {
                    // Close AI chat if open
                    if (isAIChatOpen) {
                      toggleAIChat();
                    }
                    // Toggle GitHub iframe panel
                    toggleGitHub();
                    // Active button will be set by useEffect based on isGitHubOpen state
                  } else if (item.label === 'Sketch') {
                    // Close AI chat if open
                    if (isAIChatOpen) {
                      toggleAIChat();
                    }
                    // Toggle Sketch iframe panel
                    toggleSketch();
                    // Active button will be set by useEffect based on isSketchOpen state
                  } else if (item.label === 'Diagram') {
                    // Close AI chat if open
                    if (isAIChatOpen) {
                      toggleAIChat();
                    }
                    // Toggle Diagram coming soon panel
                    toggleDiagram();
                    // Active button will be set by useEffect based on isDiagramOpen state
                  } else if (item.label === 'Forms') {
                    // Close AI chat if open
                    if (isAIChatOpen) {
                      toggleAIChat();
                    }
                    // Toggle Forms panel
                    toggleChatPdf();
                    // Active button will be set by useEffect based on isChatPdfOpen state
                  } else {
                    // For other menu items, close all panels and set active button
                    if (isAIChatOpen) {
                      toggleAIChat();
                    }
                    if (isQuizOpen || isGitHubOpen || isSketchOpen || isDiagramOpen || isChatPdfOpen || isGeneralSettingOpen) {
                      closeAllPanels();
                    }
                    setActiveButton(item.label);
                  }
                }}
              >
                <item.icon />
                <span className="ml-3 group-data-[collapsible=icon]:hidden">{item.label}</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>

      {/* Bottom container for Settings and Theme Toggle */}
      <div className="mt-auto pb-2"> {/* Adjusted margin-top and added padding-bottom */}
          <SidebarSeparator className="my-2 mx-4 group-data-[collapsible=icon]:mx-auto" />

          <SidebarContent className="px-2 pb-1"> {/* Reduced bottom padding */}
             <p className="px-2 text-xs font-semibold text-sidebar-foreground/60 mb-2 group-data-[collapsible=icon]:hidden">SETTING</p>
            <SidebarMenu>
              {settingsItems.map((item, index) => (
                <SidebarMenuItem key={index}>
                   {item.label === 'Profile Setting' ? (
                       <Popover>
                           <PopoverTrigger asChild>
                               <SidebarMenuButton
                                tooltip={item.tooltip}
                                className="justify-start text-xs font-normal rounded-none text-sidebar-foreground/80 hover:text-sidebar-foreground w-full"
                                aria-label={item.label}
                                variant="default" // Use default for subtle hover
                                size="sm"
                               >
                                 <item.icon />
                                 <span className="ml-2 group-data-[collapsible=icon]:hidden">{item.label}</span>
                               </SidebarMenuButton>
                           </PopoverTrigger>
                           <PopoverContent className="w-48 p-2" side="right" align="start" sideOffset={10}>
                               <div className="space-y-1">
                                   <Button
                                        variant="ghost"
                                        size="sm"
                                        className="w-full justify-start text-xs"
                                        onClick={logout}
                                    >
                                        <LogOut className="mr-2 h-3.5 w-3.5" />
                                        Logout
                                    </Button>
                                    <AlertDialog>
                                      <AlertDialogTrigger asChild>
                                        <Button
                                            variant="destructive"
                                            size="sm"
                                            className="w-full justify-start text-xs"
                                        >
                                            <Trash2 className="mr-2 h-3.5 w-3.5" />
                                            Delete Account
                                        </Button>
                                       </AlertDialogTrigger>
                                       <AlertDialogContent>
                                         <AlertDialogHeader>
                                           <AlertDialogTitle className="flex items-center gap-2">
                                               <AlertTriangle className="text-destructive"/> Are you sure?
                                           </AlertDialogTitle>
                                           <AlertDialogDescription>
                                             This action cannot be undone. This will permanently delete your account
                                             and remove your data from our servers.
                                           </AlertDialogDescription>
                                         </AlertDialogHeader>
                                         <AlertDialogFooter>
                                           <AlertDialogCancel>Cancel</AlertDialogCancel>
                                           {/* We need onClick={handleDeleteAccount} on the Action */}
                                           <AlertDialogAction onClick={handleDeleteAccount} className={cn(buttonVariants({ variant: "destructive" }))}>
                                                Delete
                                           </AlertDialogAction>
                                         </AlertDialogFooter>
                                       </AlertDialogContent>
                                     </AlertDialog>
                               </div>
                           </PopoverContent>
                       </Popover>
                   ) : (
                       <SidebarMenuButton
                         tooltip={item.tooltip}
                         className={cn(
                           "justify-start text-xs font-normal rounded-none text-sidebar-foreground/80 hover:text-sidebar-foreground",
                           activeButton === item.label ? "bg-sidebar-accent text-sidebar-accent-foreground" : ""
                         )}
                         aria-label={item.label}
                         variant="default" // Use default for subtle hover
                         size="sm"
                         onClick={() => {
                           if (item.label === 'General Setting') {
                             // Close AI chat if open
                             if (isAIChatOpen) {
                               toggleAIChat();
                             }
                             // Close other panels if open
                             if (isQuizOpen || isGitHubOpen || isSketchOpen || isDiagramOpen || isChatPdfOpen) {
                               closeAllPanels();
                             }
                             // Toggle General Setting coming soon panel
                             toggleGeneralSetting();
                             // Active button will be set by useEffect based on isGeneralSettingOpen state
                           }
                         }}
                       >
                         <item.icon />
                         <span className="ml-2 group-data-[collapsible=icon]:hidden">{item.label}</span>
                       </SidebarMenuButton>
                   )}
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarContent>

          <SidebarFooter className="p-4 mt-0 group-data-[collapsible=icon]:p-2"> {/* Removed margin-top */}
             {/* Custom Theme Toggle */}
             <div className="theme-toggle-group group-data-[collapsible=icon]:hidden">
               <button
                  onClick={() => toggleTheme('light')}
                  data-state={!isDarkMode ? 'active' : 'inactive'}
                  className="theme-toggle-button flex-1" // Added flex-1 for equal width
               >
                 <Sun className="h-3 w-3 mr-1" /> Light
               </button>
               <button
                  onClick={() => toggleTheme('dark')}
                  data-state={isDarkMode ? 'active' : 'inactive'}
                  className="theme-toggle-button flex-1" // Added flex-1 for equal width
               >
                  <Moon className="h-3 w-3 mr-1" /> Dark
                </button>
             </div>
             {/* Icon-only theme toggle when collapsed */}
             <div className="hidden group-data-[collapsible=icon]:flex items-center justify-center">
                 <button onClick={() => toggleTheme(isDarkMode ? 'light' : 'dark')} className="p-2 rounded-full text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground">
                     {isDarkMode ? <Moon className="h-5 w-5" /> : <Sun className="h-5 w-5" />}
                     <span className="sr-only">Toggle Theme</span>
                 </button>
             </div>
          </SidebarFooter>
        </div>
    </>
  );
};

export default LeftSidebar;

