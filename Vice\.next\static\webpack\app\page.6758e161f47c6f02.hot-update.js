"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/project/create-project-modal.tsx":
/*!*********************************************************!*\
  !*** ./src/components/project/create-project-modal.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateProjectModal: () => (/* binding */ CreateProjectModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* __next_internal_client_entry_do_not_use__ CreateProjectModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CreateProjectModal(param) {\n    let { open, onOpenChange, stackName, projectTitle = '' } = param;\n    var _startingCommands_stackName;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [projectName, setProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(projectTitle);\n    const [projectPath, setProjectPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Documents/ViceProjects/\".concat(stackName.toLowerCase()));\n    const [projectDescription, setProjectDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCommands, setShowCommands] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Starting commands for different stacks\n    const startingCommands = {\n        'Mern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express mongoose cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/models/User.js server/routes/api.js'\n        ],\n        'Pern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express pg cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/db.js server/routes/api.js'\n        ],\n        'NextJS': [\n            'npx create-next-app@latest my-app',\n            'cd my-app',\n            'npm install'\n        ],\n        'Django': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install django djangorestframework',\n            'django-admin startproject myproject .',\n            'python manage.py startapp myapp'\n        ],\n        'Flask': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install flask flask-sqlalchemy flask-cors',\n            'mkdir app',\n            'touch app/__init__.py app/routes.py app/models.py'\n        ],\n        'Bash': [\n            'mkdir my-script',\n            'cd my-script',\n            'touch script.sh',\n            'chmod +x script.sh',\n            'echo \"#!/bin/bash\" > script.sh'\n        ]\n    };\n    const handleCreate = async ()=>{\n        if (!projectName.trim()) {\n            toast({\n                title: 'Project name required',\n                description: 'Please enter a name for your project.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        setIsCreating(true);\n        try {\n            // In a real app, this would create the project\n            // For now, we'll just simulate the creation process\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Show success message\n            toast({\n                title: 'Project created',\n                description: \"\".concat(projectName, \" has been created successfully.\")\n            });\n            // Close the modal\n            onOpenChange(false);\n        } catch (error) {\n            toast({\n                title: 'Failed to create project',\n                description: 'An error occurred while creating the project.',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"sm:max-w-[550px] md:max-w-[600px] rounded-xl overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-20 w-40\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/logo.png\",\n                            alt: \"VICE Logo\",\n                            fill: true,\n                            style: {\n                                objectFit: 'contain'\n                            },\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-[-100%]' : 'translate-x-0'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                    className: \"mb-[5] pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                            className: \"text-center\",\n                                            children: \"Create New Project\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                            className: \"text-center text-sm text-muted-foreground\",\n                                            children: [\n                                                \"Create a new \",\n                                                stackName,\n                                                \" project. Fill in the details below to get started.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4 py-4 px-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"name\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"name\",\n                                                    value: projectName,\n                                                    onChange: (e)=>setProjectName(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"My Awesome Project\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"path\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Path\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"path\",\n                                                    value: projectPath,\n                                                    onChange: (e)=>setProjectPath(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"Documents/ViceProjects/\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"description\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                    id: \"description\",\n                                                    value: projectDescription,\n                                                    onChange: (e)=>setProjectDescription(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"A brief description of your project\",\n                                                    rows: 3\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-2 mb-4 ml-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-xs text-muted-foreground flex items-center gap-1 p-0 h-auto\",\n                                                onClick: ()=>setShowCommands(true),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3.5 w-3.5 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"See Starting Commands\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-3.5 w-3.5 ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                            className: \"flex justify-end items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>onOpenChange(false),\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        onClick: handleCreate,\n                                                        disabled: isCreating,\n                                                        children: isCreating ? 'Creating...' : 'Create Project'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-0' : 'translate-x-[100%]'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 ml-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"pl-0 flex items-center gap-1\",\n                                        onClick: ()=>setShowCommands(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Project Form\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                            children: [\n                                                \"Starting Commands for \",\n                                                stackName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                            children: [\n                                                \"Use these commands to set up your \",\n                                                stackName,\n                                                \" project manually.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-4 px-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-muted p-4 rounded-md font-mono text-xs sm:text-sm overflow-auto max-h-[250px]\",\n                                        children: ((_startingCommands_stackName = startingCommands[stackName]) === null || _startingCommands_stackName === void 0 ? void 0 : _startingCommands_stackName.map((command, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground mr-2 flex-shrink-0\",\n                                                        children: \"$\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"break-all\",\n                                                        children: command\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 19\n                                            }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                \"No commands available for \",\n                                                stackName,\n                                                \".\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>onOpenChange(false),\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateProjectModal, \"6ugheXxQXPDz51FL5V1CmhpiXNw=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = CreateProjectModal;\nvar _c;\n$RefreshReg$(_c, \"CreateProjectModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3Byb2plY3QvY3JlYXRlLXByb2plY3QtbW9kYWwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDcEI7QUFRQztBQUNnQjtBQUNGO0FBQ0E7QUFDTTtBQUNQO0FBQ29CO0FBVTFELFNBQVNpQixtQkFBbUIsS0FLVDtRQUxTLEVBQ2pDQyxJQUFJLEVBQ0pDLFlBQVksRUFDWkMsU0FBUyxFQUNUQyxlQUFlLEVBQUUsRUFDTyxHQUxTO1FBeU5sQkM7O0lBbk5mLE1BQU0sRUFBRUMsS0FBSyxFQUFFLEdBQUdWLDBEQUFRQTtJQUMxQixNQUFNLENBQUNXLGFBQWFDLGVBQWUsR0FBR3hCLCtDQUFRQSxDQUFDb0I7SUFDL0MsTUFBTSxDQUFDSyxhQUFhQyxlQUFlLEdBQUcxQiwrQ0FBUUEsQ0FBQywwQkFBa0QsT0FBeEJtQixVQUFVUSxXQUFXO0lBQzlGLE1BQU0sQ0FBQ0Msb0JBQW9CQyxzQkFBc0IsR0FBRzdCLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQzhCLFlBQVlDLGNBQWMsR0FBRy9CLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2dDLGNBQWNDLGdCQUFnQixHQUFHakMsK0NBQVFBLENBQUM7SUFFakQseUNBQXlDO0lBQ3pDLE1BQU1xQixtQkFBbUI7UUFDdkIsUUFBUTtZQUNOO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUNELFFBQVE7WUFDTjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDRCxVQUFVO1lBQ1I7WUFDQTtZQUNBO1NBQ0Q7UUFDRCxVQUFVO1lBQ1I7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0QsU0FBUztZQUNQO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUNELFFBQVE7WUFDTjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7SUFDSDtJQUVBLE1BQU1hLGVBQWU7UUFDbkIsSUFBSSxDQUFDWCxZQUFZWSxJQUFJLElBQUk7WUFDdkJiLE1BQU07Z0JBQ0pjLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtZQUNBO1FBQ0Y7UUFFQVAsY0FBYztRQUVkLElBQUk7WUFDRiwrQ0FBK0M7WUFDL0Msb0RBQW9EO1lBQ3BELE1BQU0sSUFBSVEsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztZQUVqRCx1QkFBdUI7WUFDdkJsQixNQUFNO2dCQUNKYyxPQUFPO2dCQUNQQyxhQUFhLEdBQWUsT0FBWmQsYUFBWTtZQUM5QjtZQUVBLGtCQUFrQjtZQUNsQkwsYUFBYTtRQUNmLEVBQUUsT0FBT3dCLE9BQU87WUFDZHBCLE1BQU07Z0JBQ0pjLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGLFNBQVU7WUFDUlAsY0FBYztRQUNoQjtJQUNGO0lBRUEscUJBQ0UsOERBQUM3Qix5REFBTUE7UUFBQ2UsTUFBTUE7UUFBTUMsY0FBY0E7a0JBQ2hDLDRFQUFDZixnRUFBYUE7WUFBQ3dDLFdBQVU7OzhCQUV2Qiw4REFBQ0M7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVO2tDQUNiLDRFQUFDMUMsa0RBQUtBOzRCQUNKNEMsS0FBSTs0QkFDSkMsS0FBSTs0QkFDSkMsSUFBSTs0QkFDSkMsT0FBTztnQ0FBRUMsV0FBVzs0QkFBVTs0QkFDOUJDLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTWQsOERBQUNOO29CQUFJRCxXQUFVOztzQ0FFYiw4REFBQ0M7NEJBQ0NELFdBQVcsaURBRVYsT0FEQ1gsZUFBZSx3QkFBd0I7OzhDQUd6Qyw4REFBQzFCLCtEQUFZQTtvQ0FBQ3FDLFdBQVU7O3NEQUN0Qiw4REFBQ3BDLDhEQUFXQTs0Q0FBQ29DLFdBQVU7c0RBQWM7Ozs7OztzREFDckMsOERBQUN2QyxvRUFBaUJBOzRDQUFDdUMsV0FBVTs7Z0RBQTRDO2dEQUN6RHhCO2dEQUFVOzs7Ozs7Ozs7Ozs7OzhDQUc1Qiw4REFBQ3lCO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDakMsdURBQUtBO29EQUFDeUMsU0FBUTtvREFBT1IsV0FBVTs4REFBc0I7Ozs7Ozs4REFHdEQsOERBQUNsQyx1REFBS0E7b0RBQ0oyQyxJQUFHO29EQUNIQyxPQUFPOUI7b0RBQ1ArQixVQUFVLENBQUNDLElBQU0vQixlQUFlK0IsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO29EQUM5Q1YsV0FBVTtvREFDVmMsYUFBWTtvREFDWkMsU0FBUzs7Ozs7Ozs7Ozs7O3NEQUdiLDhEQUFDZDs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNqQyx1REFBS0E7b0RBQUN5QyxTQUFRO29EQUFPUixXQUFVOzhEQUFzQjs7Ozs7OzhEQUd0RCw4REFBQ2xDLHVEQUFLQTtvREFDSjJDLElBQUc7b0RBQ0hDLE9BQU81QjtvREFDUDZCLFVBQVUsQ0FBQ0MsSUFBTTdCLGVBQWU2QixFQUFFQyxNQUFNLENBQUNILEtBQUs7b0RBQzlDVixXQUFVO29EQUNWYyxhQUFZOzs7Ozs7Ozs7Ozs7c0RBR2hCLDhEQUFDYjs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNqQyx1REFBS0E7b0RBQUN5QyxTQUFRO29EQUFjUixXQUFVOzhEQUFzQjs7Ozs7OzhEQUc3RCw4REFBQ2hDLDZEQUFRQTtvREFDUHlDLElBQUc7b0RBQ0hDLE9BQU96QjtvREFDUDBCLFVBQVUsQ0FBQ0MsSUFBTTFCLHNCQUFzQjBCLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztvREFDckRWLFdBQVU7b0RBQ1ZjLGFBQVk7b0RBQ1pFLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJWiw4REFBQ2Y7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTtzREFDYiw0RUFBQ25DLHlEQUFNQTtnREFDTDhCLFNBQVE7Z0RBQ1JzQixNQUFLO2dEQUNMakIsV0FBVTtnREFDVmtCLFNBQVMsSUFBTTVCLGdCQUFnQjs7a0VBRS9CLDhEQUFDbEIsMkdBQVFBO3dEQUFDNEIsV0FBVTs7Ozs7O29EQUFxQjtrRUFFekMsOERBQUM3Qiw0R0FBWUE7d0RBQUM2QixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzREFJNUIsOERBQUN0QywrREFBWUE7NENBQUNzQyxXQUFVO3NEQUN0Qiw0RUFBQ0M7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDbkMseURBQU1BO3dEQUFDOEIsU0FBUTt3REFBVXVCLFNBQVMsSUFBTTNDLGFBQWE7a0VBQVE7Ozs7OztrRUFHOUQsOERBQUNWLHlEQUFNQTt3REFBQ3FELFNBQVMzQjt3REFBYzRCLFVBQVVoQztrRUFDdENBLGFBQWEsZ0JBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FReEMsOERBQUNjOzRCQUNDRCxXQUFXLHFGQUVWLE9BRENYLGVBQWUsa0JBQWtCOzs4Q0FHbkMsOERBQUNZO29DQUFJRCxXQUFVOzhDQUNiLDRFQUFDbkMseURBQU1BO3dDQUNMOEIsU0FBUTt3Q0FDUnNCLE1BQUs7d0NBQ0xqQixXQUFVO3dDQUNWa0IsU0FBUyxJQUFNNUIsZ0JBQWdCOzswREFFL0IsOERBQUNwQiw0R0FBU0E7Z0RBQUM4QixXQUFVOzs7Ozs7NENBQVk7Ozs7Ozs7Ozs7Ozs4Q0FLckMsOERBQUNyQywrREFBWUE7O3NEQUNYLDhEQUFDQyw4REFBV0E7O2dEQUFDO2dEQUF1Qlk7Ozs7Ozs7c0RBQ3BDLDhEQUFDZixvRUFBaUJBOztnREFBQztnREFDa0JlO2dEQUFVOzs7Ozs7Ozs7Ozs7OzhDQUlqRCw4REFBQ3lCO29DQUFJRCxXQUFVOzhDQUNiLDRFQUFDQzt3Q0FBSUQsV0FBVTtrREFDWnRCLEVBQUFBLDhCQUFBQSxnQkFBZ0IsQ0FBQ0YsVUFBMkMsY0FBNURFLGtEQUFBQSw0QkFBOEQwQyxHQUFHLENBQUMsQ0FBQ0MsU0FBU0Msc0JBQzNFLDhEQUFDckI7Z0RBQWdCRCxXQUFVOztrRUFDekIsOERBQUN1Qjt3REFBS3ZCLFdBQVU7a0VBQTJDOzs7Ozs7a0VBQzNELDhEQUFDdUI7d0RBQUt2QixXQUFVO2tFQUFhcUI7Ozs7Ozs7K0NBRnJCQzs7Ozt3RUFLViw4REFBQ3JCOzRDQUFJRCxXQUFVOztnREFBd0I7Z0RBQTJCeEI7Z0RBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUtsRiw4REFBQ2QsK0RBQVlBOzhDQUNYLDRFQUFDRyx5REFBTUE7d0NBQUM4QixTQUFRO3dDQUFVdUIsU0FBUyxJQUFNM0MsYUFBYTtrREFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVM1RTtHQTlPZ0JGOztRQU1JSixzREFBUUE7OztLQU5aSSIsInNvdXJjZXMiOlsiRDpcXHByb2plY3RzXFxOZXcgZm9sZGVyXFxWaWNlXFxWaWNlXFxzcmNcXGNvbXBvbmVudHNcXHByb2plY3RcXGNyZWF0ZS1wcm9qZWN0LW1vZGFsLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnO1xuaW1wb3J0IHtcbiAgRGlhbG9nLFxuICBEaWFsb2dDb250ZW50LFxuICBEaWFsb2dEZXNjcmlwdGlvbixcbiAgRGlhbG9nRm9vdGVyLFxuICBEaWFsb2dIZWFkZXIsXG4gIERpYWxvZ1RpdGxlLFxufSBmcm9tICdAL2NvbXBvbmVudHMvdWkvZGlhbG9nJztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nO1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvaW5wdXQnO1xuaW1wb3J0IHsgTGFiZWwgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvbGFiZWwnO1xuaW1wb3J0IHsgVGV4dGFyZWEgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdGV4dGFyZWEnO1xuaW1wb3J0IHsgdXNlVG9hc3QgfSBmcm9tICdAL2hvb2tzL3VzZS10b2FzdCc7XG5pbXBvcnQgeyBBcnJvd0xlZnQsIENoZXZyb25SaWdodCwgVGVybWluYWwgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IFByb2plY3RDcmVhdGlvbkxvYWRpbmcgZnJvbSAnLi9wcm9qZWN0LWNyZWF0aW9uLWxvYWRpbmcnO1xuXG5pbnRlcmZhY2UgQ3JlYXRlUHJvamVjdE1vZGFsUHJvcHMge1xuICBvcGVuOiBib29sZWFuO1xuICBvbk9wZW5DaGFuZ2U6IChvcGVuOiBib29sZWFuKSA9PiB2b2lkO1xuICBzdGFja05hbWU6IHN0cmluZztcbiAgcHJvamVjdFRpdGxlPzogc3RyaW5nO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gQ3JlYXRlUHJvamVjdE1vZGFsKHtcbiAgb3BlbixcbiAgb25PcGVuQ2hhbmdlLFxuICBzdGFja05hbWUsXG4gIHByb2plY3RUaXRsZSA9ICcnLFxufTogQ3JlYXRlUHJvamVjdE1vZGFsUHJvcHMpIHtcbiAgY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKTtcbiAgY29uc3QgW3Byb2plY3ROYW1lLCBzZXRQcm9qZWN0TmFtZV0gPSB1c2VTdGF0ZShwcm9qZWN0VGl0bGUpO1xuICBjb25zdCBbcHJvamVjdFBhdGgsIHNldFByb2plY3RQYXRoXSA9IHVzZVN0YXRlKGBEb2N1bWVudHMvVmljZVByb2plY3RzLyR7c3RhY2tOYW1lLnRvTG93ZXJDYXNlKCl9YCk7XG4gIGNvbnN0IFtwcm9qZWN0RGVzY3JpcHRpb24sIHNldFByb2plY3REZXNjcmlwdGlvbl0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtpc0NyZWF0aW5nLCBzZXRJc0NyZWF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dDb21tYW5kcywgc2V0U2hvd0NvbW1hbmRzXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyBTdGFydGluZyBjb21tYW5kcyBmb3IgZGlmZmVyZW50IHN0YWNrc1xuICBjb25zdCBzdGFydGluZ0NvbW1hbmRzID0ge1xuICAgICdNZXJuJzogW1xuICAgICAgJ25weCBjcmVhdGUtcmVhY3QtYXBwIG15LWFwcCcsXG4gICAgICAnY2QgbXktYXBwJyxcbiAgICAgICducG0gaW5zdGFsbCBleHByZXNzIG1vbmdvb3NlIGNvcnMgZG90ZW52JyxcbiAgICAgICdta2RpciBzZXJ2ZXInLFxuICAgICAgJ3RvdWNoIHNlcnZlci9pbmRleC5qcyBzZXJ2ZXIvbW9kZWxzL1VzZXIuanMgc2VydmVyL3JvdXRlcy9hcGkuanMnXG4gICAgXSxcbiAgICAnUGVybic6IFtcbiAgICAgICducHggY3JlYXRlLXJlYWN0LWFwcCBteS1hcHAnLFxuICAgICAgJ2NkIG15LWFwcCcsXG4gICAgICAnbnBtIGluc3RhbGwgZXhwcmVzcyBwZyBjb3JzIGRvdGVudicsXG4gICAgICAnbWtkaXIgc2VydmVyJyxcbiAgICAgICd0b3VjaCBzZXJ2ZXIvaW5kZXguanMgc2VydmVyL2RiLmpzIHNlcnZlci9yb3V0ZXMvYXBpLmpzJ1xuICAgIF0sXG4gICAgJ05leHRKUyc6IFtcbiAgICAgICducHggY3JlYXRlLW5leHQtYXBwQGxhdGVzdCBteS1hcHAnLFxuICAgICAgJ2NkIG15LWFwcCcsXG4gICAgICAnbnBtIGluc3RhbGwnXG4gICAgXSxcbiAgICAnRGphbmdvJzogW1xuICAgICAgJ3B5dGhvbiAtbSB2ZW52IHZlbnYnLFxuICAgICAgJ3ZlbnZcXFxcU2NyaXB0c1xcXFxhY3RpdmF0ZScsXG4gICAgICAncGlwIGluc3RhbGwgZGphbmdvIGRqYW5nb3Jlc3RmcmFtZXdvcmsnLFxuICAgICAgJ2RqYW5nby1hZG1pbiBzdGFydHByb2plY3QgbXlwcm9qZWN0IC4nLFxuICAgICAgJ3B5dGhvbiBtYW5hZ2UucHkgc3RhcnRhcHAgbXlhcHAnXG4gICAgXSxcbiAgICAnRmxhc2snOiBbXG4gICAgICAncHl0aG9uIC1tIHZlbnYgdmVudicsXG4gICAgICAndmVudlxcXFxTY3JpcHRzXFxcXGFjdGl2YXRlJyxcbiAgICAgICdwaXAgaW5zdGFsbCBmbGFzayBmbGFzay1zcWxhbGNoZW15IGZsYXNrLWNvcnMnLFxuICAgICAgJ21rZGlyIGFwcCcsXG4gICAgICAndG91Y2ggYXBwL19faW5pdF9fLnB5IGFwcC9yb3V0ZXMucHkgYXBwL21vZGVscy5weSdcbiAgICBdLFxuICAgICdCYXNoJzogW1xuICAgICAgJ21rZGlyIG15LXNjcmlwdCcsXG4gICAgICAnY2QgbXktc2NyaXB0JyxcbiAgICAgICd0b3VjaCBzY3JpcHQuc2gnLFxuICAgICAgJ2NobW9kICt4IHNjcmlwdC5zaCcsXG4gICAgICAnZWNobyBcIiMhL2Jpbi9iYXNoXCIgPiBzY3JpcHQuc2gnXG4gICAgXVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNyZWF0ZSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXByb2plY3ROYW1lLnRyaW0oKSkge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ1Byb2plY3QgbmFtZSByZXF1aXJlZCcsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnUGxlYXNlIGVudGVyIGEgbmFtZSBmb3IgeW91ciBwcm9qZWN0LicsXG4gICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsXG4gICAgICB9KTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRJc0NyZWF0aW5nKHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIEluIGEgcmVhbCBhcHAsIHRoaXMgd291bGQgY3JlYXRlIHRoZSBwcm9qZWN0XG4gICAgICAvLyBGb3Igbm93LCB3ZSdsbCBqdXN0IHNpbXVsYXRlIHRoZSBjcmVhdGlvbiBwcm9jZXNzXG4gICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwMCkpO1xuXG4gICAgICAvLyBTaG93IHN1Y2Nlc3MgbWVzc2FnZVxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ1Byb2plY3QgY3JlYXRlZCcsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBgJHtwcm9qZWN0TmFtZX0gaGFzIGJlZW4gY3JlYXRlZCBzdWNjZXNzZnVsbHkuYCxcbiAgICAgIH0pO1xuXG4gICAgICAvLyBDbG9zZSB0aGUgbW9kYWxcbiAgICAgIG9uT3BlbkNoYW5nZShmYWxzZSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6ICdGYWlsZWQgdG8gY3JlYXRlIHByb2plY3QnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0FuIGVycm9yIG9jY3VycmVkIHdoaWxlIGNyZWF0aW5nIHRoZSBwcm9qZWN0LicsXG4gICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsXG4gICAgICB9KTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNDcmVhdGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPERpYWxvZyBvcGVuPXtvcGVufSBvbk9wZW5DaGFuZ2U9e29uT3BlbkNoYW5nZX0+XG4gICAgICA8RGlhbG9nQ29udGVudCBjbGFzc05hbWU9XCJzbTptYXgtdy1bNTUwcHhdIG1kOm1heC13LVs2MDBweF0gcm91bmRlZC14bCBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgey8qIExvZ28gYXQgdGhlIHRvcCBjZW50ZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBtYi0yXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBoLTIwIHctNDBcIj5cbiAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICBzcmM9XCIvbG9nby5wbmdcIlxuICAgICAgICAgICAgICBhbHQ9XCJWSUNFIExvZ29cIlxuICAgICAgICAgICAgICBmaWxsXG4gICAgICAgICAgICAgIHN0eWxlPXt7IG9iamVjdEZpdDogJ2NvbnRhaW4nIH19XG4gICAgICAgICAgICAgIHByaW9yaXR5XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQ29udGVudCBjb250YWluZXIgd2l0aCBhbmltYXRpb24gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgey8qIFByb2plY3QgRm9ybSAqL31cbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBjbGFzc05hbWU9e2B0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgJHtcbiAgICAgICAgICAgICAgc2hvd0NvbW1hbmRzID8gJ3RyYW5zbGF0ZS14LVstMTAwJV0nIDogJ3RyYW5zbGF0ZS14LTAnXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8RGlhbG9nSGVhZGVyIGNsYXNzTmFtZT1cIm1iLVs1XSBwYi0yXCI+XG4gICAgICAgICAgICAgIDxEaWFsb2dUaXRsZSBjbGFzc05hbWU9J3RleHQtY2VudGVyJz5DcmVhdGUgTmV3IFByb2plY3Q8L0RpYWxvZ1RpdGxlPlxuICAgICAgICAgICAgICA8RGlhbG9nRGVzY3JpcHRpb24gY2xhc3NOYW1lPSd0ZXh0LWNlbnRlciB0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCc+XG4gICAgICAgICAgICAgICAgQ3JlYXRlIGEgbmV3IHtzdGFja05hbWV9IHByb2plY3QuIEZpbGwgaW4gdGhlIGRldGFpbHMgYmVsb3cgdG8gZ2V0IHN0YXJ0ZWQuXG4gICAgICAgICAgICAgIDwvRGlhbG9nRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0RpYWxvZ0hlYWRlcj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtNCBweS00IHB4LTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC0xLjVcIj5cbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cIm5hbWVcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICBOYW1lXG4gICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgIGlkPVwibmFtZVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17cHJvamVjdE5hbWV9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFByb2plY3ROYW1lKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIk15IEF3ZXNvbWUgUHJvamVjdFwiXG4gICAgICAgICAgICAgICAgICBhdXRvRm9jdXNcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC0xLjVcIj5cbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInBhdGhcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICBQYXRoXG4gICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgIGlkPVwicGF0aFwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17cHJvamVjdFBhdGh9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFByb2plY3RQYXRoKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkRvY3VtZW50cy9WaWNlUHJvamVjdHMvXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC0xLjVcIj5cbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImRlc2NyaXB0aW9uXCIgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgRGVzY3JpcHRpb25cbiAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgaWQ9XCJkZXNjcmlwdGlvblwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17cHJvamVjdERlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRQcm9qZWN0RGVzY3JpcHRpb24oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQSBicmllZiBkZXNjcmlwdGlvbiBvZiB5b3VyIHByb2plY3RcIlxuICAgICAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbXQtMiBtYi00IG1sLTNcIj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHAtMCBoLWF1dG9cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0NvbW1hbmRzKHRydWUpfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxUZXJtaW5hbCBjbGFzc05hbWU9XCJoLTMuNSB3LTMuNSBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgIFNlZSBTdGFydGluZyBDb21tYW5kc1xuICAgICAgICAgICAgICAgICAgPENoZXZyb25SaWdodCBjbGFzc05hbWU9XCJoLTMuNSB3LTMuNSBtbC0xXCIgLz5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPERpYWxvZ0Zvb3RlciBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9eygpID0+IG9uT3BlbkNoYW5nZShmYWxzZSl9PlxuICAgICAgICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVDcmVhdGV9IGRpc2FibGVkPXtpc0NyZWF0aW5nfT5cbiAgICAgICAgICAgICAgICAgICAge2lzQ3JlYXRpbmcgPyAnQ3JlYXRpbmcuLi4nIDogJ0NyZWF0ZSBQcm9qZWN0J31cbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L0RpYWxvZ0Zvb3Rlcj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFN0YXJ0aW5nIENvbW1hbmRzICovfVxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIGNsYXNzTmFtZT17YGFic29sdXRlIHRvcC0wIGxlZnQtMCB3LWZ1bGwgaC1mdWxsIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCAke1xuICAgICAgICAgICAgICBzaG93Q29tbWFuZHMgPyAndHJhbnNsYXRlLXgtMCcgOiAndHJhbnNsYXRlLXgtWzEwMCVdJ1xuICAgICAgICAgICAgfWB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00IG1sLTNcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC0wIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Q29tbWFuZHMoZmFsc2UpfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICBCYWNrIHRvIFByb2plY3QgRm9ybVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8RGlhbG9nSGVhZGVyPlxuICAgICAgICAgICAgICA8RGlhbG9nVGl0bGU+U3RhcnRpbmcgQ29tbWFuZHMgZm9yIHtzdGFja05hbWV9PC9EaWFsb2dUaXRsZT5cbiAgICAgICAgICAgICAgPERpYWxvZ0Rlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgIFVzZSB0aGVzZSBjb21tYW5kcyB0byBzZXQgdXAgeW91ciB7c3RhY2tOYW1lfSBwcm9qZWN0IG1hbnVhbGx5LlxuICAgICAgICAgICAgICA8L0RpYWxvZ0Rlc2NyaXB0aW9uPlxuICAgICAgICAgICAgPC9EaWFsb2dIZWFkZXI+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHktNCBweC0zXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctbXV0ZWQgcC00IHJvdW5kZWQtbWQgZm9udC1tb25vIHRleHQteHMgc206dGV4dC1zbSBvdmVyZmxvdy1hdXRvIG1heC1oLVsyNTBweF1cIj5cbiAgICAgICAgICAgICAgICB7c3RhcnRpbmdDb21tYW5kc1tzdGFja05hbWUgYXMga2V5b2YgdHlwZW9mIHN0YXJ0aW5nQ29tbWFuZHNdPy5tYXAoKGNvbW1hbmQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgbXItMiBmbGV4LXNocmluay0wXCI+JDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYnJlYWstYWxsXCI+e2NvbW1hbmR9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSkgfHwgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5ObyBjb21tYW5kcyBhdmFpbGFibGUgZm9yIHtzdGFja05hbWV9LjwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxEaWFsb2dGb290ZXI+XG4gICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXsoKSA9PiBvbk9wZW5DaGFuZ2UoZmFsc2UpfT5cbiAgICAgICAgICAgICAgICBDbG9zZVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvRGlhbG9nRm9vdGVyPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICA8L0RpYWxvZz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiSW1hZ2UiLCJEaWFsb2ciLCJEaWFsb2dDb250ZW50IiwiRGlhbG9nRGVzY3JpcHRpb24iLCJEaWFsb2dGb290ZXIiLCJEaWFsb2dIZWFkZXIiLCJEaWFsb2dUaXRsZSIsIkJ1dHRvbiIsIklucHV0IiwiTGFiZWwiLCJUZXh0YXJlYSIsInVzZVRvYXN0IiwiQXJyb3dMZWZ0IiwiQ2hldnJvblJpZ2h0IiwiVGVybWluYWwiLCJDcmVhdGVQcm9qZWN0TW9kYWwiLCJvcGVuIiwib25PcGVuQ2hhbmdlIiwic3RhY2tOYW1lIiwicHJvamVjdFRpdGxlIiwic3RhcnRpbmdDb21tYW5kcyIsInRvYXN0IiwicHJvamVjdE5hbWUiLCJzZXRQcm9qZWN0TmFtZSIsInByb2plY3RQYXRoIiwic2V0UHJvamVjdFBhdGgiLCJ0b0xvd2VyQ2FzZSIsInByb2plY3REZXNjcmlwdGlvbiIsInNldFByb2plY3REZXNjcmlwdGlvbiIsImlzQ3JlYXRpbmciLCJzZXRJc0NyZWF0aW5nIiwic2hvd0NvbW1hbmRzIiwic2V0U2hvd0NvbW1hbmRzIiwiaGFuZGxlQ3JlYXRlIiwidHJpbSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJ2YXJpYW50IiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiZXJyb3IiLCJjbGFzc05hbWUiLCJkaXYiLCJzcmMiLCJhbHQiLCJmaWxsIiwic3R5bGUiLCJvYmplY3RGaXQiLCJwcmlvcml0eSIsImh0bWxGb3IiLCJpZCIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJhdXRvRm9jdXMiLCJyb3dzIiwic2l6ZSIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsIm1hcCIsImNvbW1hbmQiLCJpbmRleCIsInNwYW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/create-project-modal.tsx\n"));

/***/ })

});