"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@monaco-editor";
exports.ids = ["vendor-chunks/@monaco-editor"];
exports.modules = {

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrayLikeToArray: () => (/* binding */ _arrayLikeToArray),\n/* harmony export */   arrayWithHoles: () => (/* binding */ _arrayWithHoles),\n/* harmony export */   defineProperty: () => (/* binding */ _defineProperty),\n/* harmony export */   iterableToArrayLimit: () => (/* binding */ _iterableToArrayLimit),\n/* harmony export */   nonIterableRest: () => (/* binding */ _nonIterableRest),\n/* harmony export */   objectSpread2: () => (/* binding */ _objectSpread2),\n/* harmony export */   objectWithoutProperties: () => (/* binding */ _objectWithoutProperties),\n/* harmony export */   objectWithoutPropertiesLoose: () => (/* binding */ _objectWithoutPropertiesLoose),\n/* harmony export */   slicedToArray: () => (/* binding */ _slicedToArray),\n/* harmony export */   unsupportedIterableToArray: () => (/* binding */ _unsupportedIterableToArray)\n/* harmony export */ });\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/config/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/config/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar config = {\n  paths: {\n    vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs'\n  }\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy9jb25maWcvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaUVBQWUsTUFBTSxFQUFDIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXE5ldyBmb2xkZXJcXFZpY2VcXFZpY2VJZGVcXG5vZGVfbW9kdWxlc1xcQG1vbmFjby1lZGl0b3JcXGxvYWRlclxcbGliXFxlc1xcY29uZmlnXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgY29uZmlnID0ge1xuICBwYXRoczoge1xuICAgIHZzOiAnaHR0cHM6Ly9jZG4uanNkZWxpdnIubmV0L25wbS9tb25hY28tZWRpdG9yQDAuNTIuMi9taW4vdnMnXG4gIH1cbn07XG5cbmV4cG9ydCBkZWZhdWx0IGNvbmZpZztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/config/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _loader_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _loader_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./loader/index.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1QztBQUNLIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXE5ldyBmb2xkZXJcXFZpY2VcXFZpY2VJZGVcXG5vZGVfbW9kdWxlc1xcQG1vbmFjby1lZGl0b3JcXGxvYWRlclxcbGliXFxlc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGxvYWRlciBmcm9tICcuL2xvYWRlci9pbmRleC5qcyc7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSAnLi9sb2FkZXIvaW5kZXguanMnO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/loader/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var state_local__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! state-local */ \"(ssr)/./node_modules/state-local/lib/es/state-local.js\");\n/* harmony import */ var _config_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../config/index.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/config/index.js\");\n/* harmony import */ var _validators_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../validators/index.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js\");\n/* harmony import */ var _utils_compose_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/compose.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js\");\n/* harmony import */ var _utils_deepMerge_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/deepMerge.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js\");\n/* harmony import */ var _utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/makeCancelable.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js\");\n\n\n\n\n\n\n\n\n/** the local state of the module */\n\nvar _state$create = state_local__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n  config: _config_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n  isInitialized: false,\n  resolve: null,\n  reject: null,\n  monaco: null\n}),\n    _state$create2 = (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.slicedToArray)(_state$create, 2),\n    getState = _state$create2[0],\n    setState = _state$create2[1];\n/**\n * set the loader configuration\n * @param {Object} config - the configuration object\n */\n\n\nfunction config(globalConfig) {\n  var _validators$config = _validators_index_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].config(globalConfig),\n      monaco = _validators$config.monaco,\n      config = (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectWithoutProperties)(_validators$config, [\"monaco\"]);\n\n  setState(function (state) {\n    return {\n      config: (0,_utils_deepMerge_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(state.config, config),\n      monaco: monaco\n    };\n  });\n}\n/**\n * handles the initialization of the monaco-editor\n * @return {Promise} - returns an instance of monaco (with a cancelable promise)\n */\n\n\nfunction init() {\n  var state = getState(function (_ref) {\n    var monaco = _ref.monaco,\n        isInitialized = _ref.isInitialized,\n        resolve = _ref.resolve;\n    return {\n      monaco: monaco,\n      isInitialized: isInitialized,\n      resolve: resolve\n    };\n  });\n\n  if (!state.isInitialized) {\n    setState({\n      isInitialized: true\n    });\n\n    if (state.monaco) {\n      state.resolve(state.monaco);\n      return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n    }\n\n    if (window.monaco && window.monaco.editor) {\n      storeMonacoInstance(window.monaco);\n      state.resolve(window.monaco);\n      return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n    }\n\n    (0,_utils_compose_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(injectScripts, getMonacoLoaderScript)(configureLoader);\n  }\n\n  return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n}\n/**\n * injects provided scripts into the document.body\n * @param {Object} script - an HTML script element\n * @return {Object} - the injected HTML script element\n */\n\n\nfunction injectScripts(script) {\n  return document.body.appendChild(script);\n}\n/**\n * creates an HTML script element with/without provided src\n * @param {string} [src] - the source path of the script\n * @return {Object} - the created HTML script element\n */\n\n\nfunction createScript(src) {\n  var script = document.createElement('script');\n  return src && (script.src = src), script;\n}\n/**\n * creates an HTML script element with the monaco loader src\n * @return {Object} - the created HTML script element\n */\n\n\nfunction getMonacoLoaderScript(configureLoader) {\n  var state = getState(function (_ref2) {\n    var config = _ref2.config,\n        reject = _ref2.reject;\n    return {\n      config: config,\n      reject: reject\n    };\n  });\n  var loaderScript = createScript(\"\".concat(state.config.paths.vs, \"/loader.js\"));\n\n  loaderScript.onload = function () {\n    return configureLoader();\n  };\n\n  loaderScript.onerror = state.reject;\n  return loaderScript;\n}\n/**\n * configures the monaco loader\n */\n\n\nfunction configureLoader() {\n  var state = getState(function (_ref3) {\n    var config = _ref3.config,\n        resolve = _ref3.resolve,\n        reject = _ref3.reject;\n    return {\n      config: config,\n      resolve: resolve,\n      reject: reject\n    };\n  });\n  var require = window.require;\n\n  require.config(state.config);\n\n  require(['vs/editor/editor.main'], function (monaco) {\n    storeMonacoInstance(monaco);\n    state.resolve(monaco);\n  }, function (error) {\n    state.reject(error);\n  });\n}\n/**\n * store monaco instance in local state\n */\n\n\nfunction storeMonacoInstance(monaco) {\n  if (!getState().monaco) {\n    setState({\n      monaco: monaco\n    });\n  }\n}\n/**\n * internal helper function\n * extracts stored monaco instance\n * @return {Object|null} - the monaco instance\n */\n\n\nfunction __getMonacoInstance() {\n  return getState(function (_ref4) {\n    var monaco = _ref4.monaco;\n    return monaco;\n  });\n}\n\nvar wrapperPromise = new Promise(function (resolve, reject) {\n  return setState({\n    resolve: resolve,\n    reject: reject\n  });\n});\nvar loader = {\n  config: config,\n  init: init,\n  __getMonacoInstance: __getMonacoInstance\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (loader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js":
/*!********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/compose.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar compose = function compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (compose);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9jb21wb3NlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLHFFQUFxRSxhQUFhO0FBQ2xGO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUEsaUVBQWUsT0FBTyxFQUFDIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXE5ldyBmb2xkZXJcXFZpY2VcXFZpY2VJZGVcXG5vZGVfbW9kdWxlc1xcQG1vbmFjby1lZGl0b3JcXGxvYWRlclxcbGliXFxlc1xcdXRpbHNcXGNvbXBvc2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGNvbXBvc2UgPSBmdW5jdGlvbiBjb21wb3NlKCkge1xuICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgZm5zID0gbmV3IEFycmF5KF9sZW4pLCBfa2V5ID0gMDsgX2tleSA8IF9sZW47IF9rZXkrKykge1xuICAgIGZuc1tfa2V5XSA9IGFyZ3VtZW50c1tfa2V5XTtcbiAgfVxuXG4gIHJldHVybiBmdW5jdGlvbiAoeCkge1xuICAgIHJldHVybiBmbnMucmVkdWNlUmlnaHQoZnVuY3Rpb24gKHksIGYpIHtcbiAgICAgIHJldHVybiBmKHkpO1xuICAgIH0sIHgpO1xuICB9O1xufTtcblxuZXhwb3J0IGRlZmF1bHQgY29tcG9zZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js":
/*!******************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/curry.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        nextArgs[_key2] = arguments[_key2];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (curry);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9jdXJyeS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBOztBQUVBLHdFQUF3RSxhQUFhO0FBQ3JGO0FBQ0E7O0FBRUE7QUFDQSxpRkFBaUYsZUFBZTtBQUNoRztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGlFQUFlLEtBQUssRUFBQyIsInNvdXJjZXMiOlsiRDpcXHByb2plY3RzXFxOZXcgZm9sZGVyXFxWaWNlXFxWaWNlSWRlXFxub2RlX21vZHVsZXNcXEBtb25hY28tZWRpdG9yXFxsb2FkZXJcXGxpYlxcZXNcXHV0aWxzXFxjdXJyeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBjdXJyeShmbikge1xuICByZXR1cm4gZnVuY3Rpb24gY3VycmllZCgpIHtcbiAgICB2YXIgX3RoaXMgPSB0aGlzO1xuXG4gICAgZm9yICh2YXIgX2xlbiA9IGFyZ3VtZW50cy5sZW5ndGgsIGFyZ3MgPSBuZXcgQXJyYXkoX2xlbiksIF9rZXkgPSAwOyBfa2V5IDwgX2xlbjsgX2tleSsrKSB7XG4gICAgICBhcmdzW19rZXldID0gYXJndW1lbnRzW19rZXldO1xuICAgIH1cblxuICAgIHJldHVybiBhcmdzLmxlbmd0aCA+PSBmbi5sZW5ndGggPyBmbi5hcHBseSh0aGlzLCBhcmdzKSA6IGZ1bmN0aW9uICgpIHtcbiAgICAgIGZvciAodmFyIF9sZW4yID0gYXJndW1lbnRzLmxlbmd0aCwgbmV4dEFyZ3MgPSBuZXcgQXJyYXkoX2xlbjIpLCBfa2V5MiA9IDA7IF9rZXkyIDwgX2xlbjI7IF9rZXkyKyspIHtcbiAgICAgICAgbmV4dEFyZ3NbX2tleTJdID0gYXJndW1lbnRzW19rZXkyXTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIGN1cnJpZWQuYXBwbHkoX3RoaXMsIFtdLmNvbmNhdChhcmdzLCBuZXh0QXJncykpO1xuICAgIH07XG4gIH07XG59XG5cbmV4cG9ydCBkZWZhdWx0IGN1cnJ5O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\");\n\n\nfunction merge(target, source) {\n  Object.keys(source).forEach(function (key) {\n    if (source[key] instanceof Object) {\n      if (target[key]) {\n        Object.assign(source[key], merge(target[key], source[key]));\n      }\n    }\n  });\n  return (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectSpread2)((0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectSpread2)({}, target), source);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (merge);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9kZWVwTWVyZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkY7O0FBRTNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILFNBQVMsbUZBQWMsQ0FBQyxtRkFBYyxHQUFHO0FBQ3pDOztBQUVBLGlFQUFlLEtBQUssRUFBQyIsInNvdXJjZXMiOlsiRDpcXHByb2plY3RzXFxOZXcgZm9sZGVyXFxWaWNlXFxWaWNlSWRlXFxub2RlX21vZHVsZXNcXEBtb25hY28tZWRpdG9yXFxsb2FkZXJcXGxpYlxcZXNcXHV0aWxzXFxkZWVwTWVyZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgb2JqZWN0U3ByZWFkMiBhcyBfb2JqZWN0U3ByZWFkMiB9IGZyb20gJy4uL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMnO1xuXG5mdW5jdGlvbiBtZXJnZSh0YXJnZXQsIHNvdXJjZSkge1xuICBPYmplY3Qua2V5cyhzb3VyY2UpLmZvckVhY2goZnVuY3Rpb24gKGtleSkge1xuICAgIGlmIChzb3VyY2Vba2V5XSBpbnN0YW5jZW9mIE9iamVjdCkge1xuICAgICAgaWYgKHRhcmdldFtrZXldKSB7XG4gICAgICAgIE9iamVjdC5hc3NpZ24oc291cmNlW2tleV0sIG1lcmdlKHRhcmdldFtrZXldLCBzb3VyY2Vba2V5XSkpO1xuICAgICAgfVxuICAgIH1cbiAgfSk7XG4gIHJldHVybiBfb2JqZWN0U3ByZWFkMihfb2JqZWN0U3ByZWFkMih7fSwgdGFyZ2V0KSwgc291cmNlKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgbWVyZ2U7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9pc09iamVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxXQUFXO0FBQ1g7O0FBRUEsaUVBQWUsUUFBUSxFQUFDIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXE5ldyBmb2xkZXJcXFZpY2VcXFZpY2VJZGVcXG5vZGVfbW9kdWxlc1xcQG1vbmFjby1lZGl0b3JcXGxvYWRlclxcbGliXFxlc1xcdXRpbHNcXGlzT2JqZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGlzT2JqZWN0KHZhbHVlKSB7XG4gIHJldHVybiB7fS50b1N0cmluZy5jYWxsKHZhbHVlKS5pbmNsdWRlcygnT2JqZWN0Jyk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGlzT2JqZWN0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CANCELATION_MESSAGE: () => (/* binding */ CANCELATION_MESSAGE),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325\nvar CANCELATION_MESSAGE = {\n  type: 'cancelation',\n  msg: 'operation is manually canceled'\n};\n\nfunction makeCancelable(promise) {\n  var hasCanceled_ = false;\n  var wrappedPromise = new Promise(function (resolve, reject) {\n    promise.then(function (val) {\n      return hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val);\n    });\n    promise[\"catch\"](reject);\n  });\n  return wrappedPromise.cancel = function () {\n    return hasCanceled_ = true;\n  }, wrappedPromise;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (makeCancelable);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9tYWtlQ2FuY2VsYWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBLGlFQUFlLGNBQWMsRUFBQztBQUNDIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXE5ldyBmb2xkZXJcXFZpY2VcXFZpY2VJZGVcXG5vZGVfbW9kdWxlc1xcQG1vbmFjby1lZGl0b3JcXGxvYWRlclxcbGliXFxlc1xcdXRpbHNcXG1ha2VDYW5jZWxhYmxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoZSBzb3VyY2UgKGhhcyBiZWVuIGNoYW5nZWQpIGlzIGh0dHBzOi8vZ2l0aHViLmNvbS9mYWNlYm9vay9yZWFjdC9pc3N1ZXMvNTQ2NSNpc3N1ZWNvbW1lbnQtMTU3ODg4MzI1XG52YXIgQ0FOQ0VMQVRJT05fTUVTU0FHRSA9IHtcbiAgdHlwZTogJ2NhbmNlbGF0aW9uJyxcbiAgbXNnOiAnb3BlcmF0aW9uIGlzIG1hbnVhbGx5IGNhbmNlbGVkJ1xufTtcblxuZnVuY3Rpb24gbWFrZUNhbmNlbGFibGUocHJvbWlzZSkge1xuICB2YXIgaGFzQ2FuY2VsZWRfID0gZmFsc2U7XG4gIHZhciB3cmFwcGVkUHJvbWlzZSA9IG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHtcbiAgICBwcm9taXNlLnRoZW4oZnVuY3Rpb24gKHZhbCkge1xuICAgICAgcmV0dXJuIGhhc0NhbmNlbGVkXyA/IHJlamVjdChDQU5DRUxBVElPTl9NRVNTQUdFKSA6IHJlc29sdmUodmFsKTtcbiAgICB9KTtcbiAgICBwcm9taXNlW1wiY2F0Y2hcIl0ocmVqZWN0KTtcbiAgfSk7XG4gIHJldHVybiB3cmFwcGVkUHJvbWlzZS5jYW5jZWwgPSBmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIGhhc0NhbmNlbGVkXyA9IHRydWU7XG4gIH0sIHdyYXBwZWRQcm9taXNlO1xufVxuXG5leHBvcnQgZGVmYXVsdCBtYWtlQ2FuY2VsYWJsZTtcbmV4cG9ydCB7IENBTkNFTEFUSU9OX01FU1NBR0UgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/validators/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   errorHandler: () => (/* binding */ errorHandler),\n/* harmony export */   errorMessages: () => (/* binding */ errorMessages)\n/* harmony export */ });\n/* harmony import */ var _utils_curry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/curry.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js\");\n/* harmony import */ var _utils_isObject_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/isObject.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js\");\n\n\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */\n\nfunction validateConfig(config) {\n  if (!config) errorHandler('configIsRequired');\n  if (!(0,_utils_isObject_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(config)) errorHandler('configType');\n\n  if (config.urls) {\n    informAboutDeprecation();\n    return {\n      paths: {\n        vs: config.urls.monacoBase\n      }\n    };\n  }\n\n  return config;\n}\n/**\n * logs deprecation message\n */\n\n\nfunction informAboutDeprecation() {\n  console.warn(errorMessages.deprecation);\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  configIsRequired: 'the configuration object is required',\n  configType: 'the configuration object should be an object',\n  \"default\": 'an unknown error accured in `@monaco-editor/loader` package',\n  deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = (0,_utils_curry_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(throwError)(errorMessages);\nvar validators = {\n  config: validateConfig\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validators);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/react/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@monaco-editor/react/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiffEditor: () => (/* binding */ we),\n/* harmony export */   Editor: () => (/* binding */ de),\n/* harmony export */   \"default\": () => (/* binding */ Ft),\n/* harmony export */   loader: () => (/* reexport safe */ _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   useMonaco: () => (/* binding */ Le)\n/* harmony export */ });\n/* harmony import */ var _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @monaco-editor/loader */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar le={wrapper:{display:\"flex\",position:\"relative\",textAlign:\"initial\"},fullWidth:{width:\"100%\"},hide:{display:\"none\"}},v=le;var ae={container:{display:\"flex\",height:\"100%\",width:\"100%\",justifyContent:\"center\",alignItems:\"center\"}},Y=ae;function Me({children:e}){return react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\",{style:Y.container},e)}var Z=Me;var $=Z;function Ee({width:e,height:r,isEditorReady:n,loading:t,_ref:a,className:m,wrapperProps:E}){return react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"section\",{style:{...v.wrapper,width:e,height:r},...E},!n&&react__WEBPACK_IMPORTED_MODULE_1__.createElement($,null,t),react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\",{ref:a,style:{...v.fullWidth,...!n&&v.hide},className:m}))}var ee=Ee;var H=(0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(ee);function Ce(e){(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(e,[])}var k=Ce;function he(e,r,n=!0){let t=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!0);(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(t.current||!n?()=>{t.current=!1}:e,r)}var l=he;function D(){}function h(e,r,n,t){return De(e,t)||be(e,r,n,t)}function De(e,r){return e.editor.getModel(te(e,r))}function be(e,r,n,t){return e.editor.createModel(r,n,t?te(e,t):void 0)}function te(e,r){return e.Uri.parse(r)}function Oe({original:e,modified:r,language:n,originalLanguage:t,modifiedLanguage:a,originalModelPath:m,modifiedModelPath:E,keepCurrentOriginalModel:g=!1,keepCurrentModifiedModel:N=!1,theme:x=\"light\",loading:P=\"Loading...\",options:y={},height:V=\"100%\",width:z=\"100%\",className:F,wrapperProps:j={},beforeMount:A=D,onMount:q=D}){let[M,O]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1),[T,s]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!0),u=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),c=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),w=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),d=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(q),o=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(A),b=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1);k(()=>{let i=_monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init();return i.then(f=>(c.current=f)&&s(!1)).catch(f=>f?.type!==\"cancelation\"&&console.error(\"Monaco initialization: error:\",f)),()=>u.current?I():i.cancel()}),l(()=>{if(u.current&&c.current){let i=u.current.getOriginalEditor(),f=h(c.current,e||\"\",t||n||\"text\",m||\"\");f!==i.getModel()&&i.setModel(f)}},[m],M),l(()=>{if(u.current&&c.current){let i=u.current.getModifiedEditor(),f=h(c.current,r||\"\",a||n||\"text\",E||\"\");f!==i.getModel()&&i.setModel(f)}},[E],M),l(()=>{let i=u.current.getModifiedEditor();i.getOption(c.current.editor.EditorOption.readOnly)?i.setValue(r||\"\"):r!==i.getValue()&&(i.executeEdits(\"\",[{range:i.getModel().getFullModelRange(),text:r||\"\",forceMoveMarkers:!0}]),i.pushUndoStop())},[r],M),l(()=>{u.current?.getModel()?.original.setValue(e||\"\")},[e],M),l(()=>{let{original:i,modified:f}=u.current.getModel();c.current.editor.setModelLanguage(i,t||n||\"text\"),c.current.editor.setModelLanguage(f,a||n||\"text\")},[n,t,a],M),l(()=>{c.current?.editor.setTheme(x)},[x],M),l(()=>{u.current?.updateOptions(y)},[y],M);let L=(0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{if(!c.current)return;o.current(c.current);let i=h(c.current,e||\"\",t||n||\"text\",m||\"\"),f=h(c.current,r||\"\",a||n||\"text\",E||\"\");u.current?.setModel({original:i,modified:f})},[n,r,a,e,t,m,E]),U=(0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{!b.current&&w.current&&(u.current=c.current.editor.createDiffEditor(w.current,{automaticLayout:!0,...y}),L(),c.current?.editor.setTheme(x),O(!0),b.current=!0)},[y,x,L]);(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{M&&d.current(u.current,c.current)},[M]),(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{!T&&!M&&U()},[T,M,U]);function I(){let i=u.current?.getModel();g||i?.original?.dispose(),N||i?.modified?.dispose(),u.current?.dispose()}return react__WEBPACK_IMPORTED_MODULE_1__.createElement(H,{width:z,height:V,isEditorReady:M,loading:P,_ref:w,className:F,wrapperProps:j})}var ie=Oe;var we=(0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(ie);function Pe(){let[e,r]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].__getMonacoInstance());return k(()=>{let n;return e||(n=_monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init(),n.then(t=>{r(t)})),()=>n?.cancel()}),e}var Le=Pe;function He(e){let r=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{r.current=e},[e]),r.current}var se=He;var _=new Map;function Ve({defaultValue:e,defaultLanguage:r,defaultPath:n,value:t,language:a,path:m,theme:E=\"light\",line:g,loading:N=\"Loading...\",options:x={},overrideServices:P={},saveViewState:y=!0,keepCurrentModel:V=!1,width:z=\"100%\",height:F=\"100%\",className:j,wrapperProps:A={},beforeMount:q=D,onMount:M=D,onChange:O,onValidate:T=D}){let[s,u]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1),[c,w]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!0),d=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),o=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),b=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),L=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(M),U=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(q),I=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(),i=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(t),f=se(m),Q=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1),B=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1);k(()=>{let p=_monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init();return p.then(R=>(d.current=R)&&w(!1)).catch(R=>R?.type!==\"cancelation\"&&console.error(\"Monaco initialization: error:\",R)),()=>o.current?pe():p.cancel()}),l(()=>{let p=h(d.current,e||t||\"\",r||a||\"\",m||n||\"\");p!==o.current?.getModel()&&(y&&_.set(f,o.current?.saveViewState()),o.current?.setModel(p),y&&o.current?.restoreViewState(_.get(m)))},[m],s),l(()=>{o.current?.updateOptions(x)},[x],s),l(()=>{!o.current||t===void 0||(o.current.getOption(d.current.editor.EditorOption.readOnly)?o.current.setValue(t):t!==o.current.getValue()&&(B.current=!0,o.current.executeEdits(\"\",[{range:o.current.getModel().getFullModelRange(),text:t,forceMoveMarkers:!0}]),o.current.pushUndoStop(),B.current=!1))},[t],s),l(()=>{let p=o.current?.getModel();p&&a&&d.current?.editor.setModelLanguage(p,a)},[a],s),l(()=>{g!==void 0&&o.current?.revealLine(g)},[g],s),l(()=>{d.current?.editor.setTheme(E)},[E],s);let X=(0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{if(!(!b.current||!d.current)&&!Q.current){U.current(d.current);let p=m||n,R=h(d.current,t||e||\"\",r||a||\"\",p||\"\");o.current=d.current?.editor.create(b.current,{model:R,automaticLayout:!0,...x},P),y&&o.current.restoreViewState(_.get(p)),d.current.editor.setTheme(E),g!==void 0&&o.current.revealLine(g),u(!0),Q.current=!0}},[e,r,n,t,a,m,x,P,y,E,g]);(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{s&&L.current(o.current,d.current)},[s]),(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{!c&&!s&&X()},[c,s,X]),i.current=t,(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{s&&O&&(I.current?.dispose(),I.current=o.current?.onDidChangeModelContent(p=>{B.current||O(o.current.getValue(),p)}))},[s,O]),(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{if(s){let p=d.current.editor.onDidChangeMarkers(R=>{let G=o.current.getModel()?.uri;if(G&&R.find(J=>J.path===G.path)){let J=d.current.editor.getModelMarkers({resource:G});T?.(J)}});return()=>{p?.dispose()}}return()=>{}},[s,T]);function pe(){I.current?.dispose(),V?y&&_.set(m,o.current.saveViewState()):o.current.getModel()?.dispose(),o.current.dispose()}return react__WEBPACK_IMPORTED_MODULE_1__.createElement(H,{width:z,height:F,isEditorReady:s,loading:N,_ref:b,className:j,wrapperProps:A})}var fe=Ve;var de=(0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(fe);var Ft=de;\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvcmVhY3QvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFpUCxRQUFRLFNBQVMsdURBQXVELFlBQVksYUFBYSxPQUFPLGdCQUFnQixNQUE0QixRQUFRLFdBQVcsdUZBQXVGLE1BQU0sYUFBYSxXQUFXLEVBQUUsT0FBTyxnREFBZ0IsUUFBUSxrQkFBa0IsSUFBSSxTQUFTLFFBQVEsYUFBYSw2RUFBNkUsRUFBRSxPQUFPLGdEQUFlLFlBQVksT0FBTyw4QkFBOEIsTUFBTSxLQUFLLGdEQUFlLFdBQVcsZ0RBQWUsUUFBUSxhQUFhLDZCQUE2QixhQUFhLEdBQUcsVUFBVSxNQUFNLDJDQUFFLEtBQXdDLGVBQWUsZ0RBQUUsT0FBTyxTQUF5RCxzQkFBc0IsTUFBTSw2Q0FBRSxLQUFLLGdEQUFFLG9CQUFvQixhQUFhLE1BQU0sU0FBUyxjQUFjLG9CQUFvQiw0QkFBNEIsaUJBQWlCLGtDQUFrQyxxQkFBcUIsa0RBQWtELGlCQUFpQixzQkFBc0IsYUFBYSw4TkFBOE4sNkRBQTZELDZCQUE2QixFQUFFLFNBQVMsK0NBQUUsV0FBVywrQ0FBRSxPQUFPLDZDQUFDLFNBQVMsNkNBQUMsU0FBUyw2Q0FBQyxTQUFTLDZDQUFDLE1BQU0sNkNBQUMsTUFBTSw2Q0FBQyxLQUFLLE9BQU8sTUFBTSw2REFBRSxRQUFRLHdKQUF3SixTQUFTLHlCQUF5Qiw0RUFBNEUsaUNBQWlDLGVBQWUseUJBQXlCLDRFQUE0RSxpQ0FBaUMsZUFBZSxvQ0FBb0MsNkdBQTZHLHNFQUFzRSxxQkFBcUIsZUFBZSxnREFBZ0QsZUFBZSxJQUFJLHNCQUFzQixzQkFBc0Isb0dBQW9HLG1CQUFtQiw4QkFBOEIsZUFBZSw0QkFBNEIsUUFBUSxNQUFNLGtEQUFFLE1BQU0scUJBQXFCLHFCQUFxQixvRkFBb0YscUJBQXFCLHNCQUFzQixFQUFFLG9CQUFvQixrREFBRSxNQUFNLCtFQUErRSx3QkFBd0Isd0RBQXdELFVBQVUsZ0RBQUUsTUFBTSxrQ0FBa0MsTUFBTSxnREFBRSxNQUFNLFlBQVksVUFBVSxhQUFhLDRCQUE0Qix5RUFBeUUsT0FBTyxnREFBZ0IsSUFBSSw2RUFBNkUsRUFBRSxVQUFVLE9BQU8sMkNBQUUsS0FBNkUsY0FBYyxTQUFTLCtDQUFFLENBQUMsNkRBQUUsd0JBQXdCLGNBQWMsTUFBTSxhQUFhLDZEQUFFLG1CQUFtQixLQUFLLG1CQUFtQixJQUFJLFVBQWlOLGVBQWUsTUFBTSw2Q0FBRSxHQUFHLE9BQU8sZ0RBQUUsTUFBTSxZQUFZLGdCQUFnQixVQUFVLGNBQWMsYUFBYSxtSUFBbUksc0JBQXNCLHNHQUFzRyx1REFBdUQsRUFBRSxTQUFTLCtDQUFFLFdBQVcsK0NBQUUsT0FBTyw2Q0FBQyxTQUFTLDZDQUFDLFNBQVMsNkNBQUMsU0FBUyw2Q0FBQyxNQUFNLDZDQUFDLE1BQU0sNkNBQUMsS0FBSyw2Q0FBQyxjQUFjLDZDQUFDLE9BQU8sNkNBQUMsS0FBSyxPQUFPLE1BQU0sNkRBQUUsUUFBUSx5SkFBeUosU0FBUyw4Q0FBOEMsb0lBQW9JLGVBQWUsNEJBQTRCLGVBQWUsK0tBQStLLDBFQUEwRSwyQ0FBMkMsZUFBZSw0QkFBNEIsOENBQThDLGVBQWUscUNBQXFDLGVBQWUsOEJBQThCLFFBQVEsTUFBTSxrREFBRSxNQUFNLDBDQUEwQyxxQkFBcUIsa0RBQWtELDhDQUE4QyxnQ0FBZ0MsaUlBQWlJLDBCQUEwQixnREFBQyxNQUFNLGtDQUFrQyxNQUFNLGdEQUFDLE1BQU0sWUFBWSxzQkFBc0IsZ0RBQUMsTUFBTSw2RUFBNkUscUNBQXFDLEdBQUcsUUFBUSxnREFBQyxNQUFNLE1BQU0sOENBQThDLGdDQUFnQyxrQ0FBa0Msd0NBQXdDLFdBQVcsRUFBRSxRQUFRLEVBQUUsV0FBVyxjQUFjLGFBQWEsUUFBUSxjQUFjLGlIQUFpSCxPQUFPLGdEQUFnQixJQUFJLDZFQUE2RSxFQUFFLFVBQVUsT0FBTywyQ0FBRSxLQUFLLFVBQTJGO0FBQzcxTSIsInNvdXJjZXMiOlsiRDpcXHByb2plY3RzXFxOZXcgZm9sZGVyXFxWaWNlXFxWaWNlSWRlXFxub2RlX21vZHVsZXNcXEBtb25hY28tZWRpdG9yXFxyZWFjdFxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfdCBmcm9tXCJAbW9uYWNvLWVkaXRvci9sb2FkZXJcIjtpbXBvcnR7bWVtbyBhcyBUZX1mcm9tXCJyZWFjdFwiO2ltcG9ydCBrZSx7dXNlU3RhdGUgYXMgcmUsdXNlUmVmIGFzIFMsdXNlQ2FsbGJhY2sgYXMgb2UsdXNlRWZmZWN0IGFzIG5lfWZyb21cInJlYWN0XCI7aW1wb3J0IFNlIGZyb21cIkBtb25hY28tZWRpdG9yL2xvYWRlclwiO2ltcG9ydHttZW1vIGFzIHllfWZyb21cInJlYWN0XCI7aW1wb3J0IEsgZnJvbVwicmVhY3RcIjt2YXIgbGU9e3dyYXBwZXI6e2Rpc3BsYXk6XCJmbGV4XCIscG9zaXRpb246XCJyZWxhdGl2ZVwiLHRleHRBbGlnbjpcImluaXRpYWxcIn0sZnVsbFdpZHRoOnt3aWR0aDpcIjEwMCVcIn0saGlkZTp7ZGlzcGxheTpcIm5vbmVcIn19LHY9bGU7aW1wb3J0IG1lIGZyb21cInJlYWN0XCI7dmFyIGFlPXtjb250YWluZXI6e2Rpc3BsYXk6XCJmbGV4XCIsaGVpZ2h0OlwiMTAwJVwiLHdpZHRoOlwiMTAwJVwiLGp1c3RpZnlDb250ZW50OlwiY2VudGVyXCIsYWxpZ25JdGVtczpcImNlbnRlclwifX0sWT1hZTtmdW5jdGlvbiBNZSh7Y2hpbGRyZW46ZX0pe3JldHVybiBtZS5jcmVhdGVFbGVtZW50KFwiZGl2XCIse3N0eWxlOlkuY29udGFpbmVyfSxlKX12YXIgWj1NZTt2YXIgJD1aO2Z1bmN0aW9uIEVlKHt3aWR0aDplLGhlaWdodDpyLGlzRWRpdG9yUmVhZHk6bixsb2FkaW5nOnQsX3JlZjphLGNsYXNzTmFtZTptLHdyYXBwZXJQcm9wczpFfSl7cmV0dXJuIEsuY3JlYXRlRWxlbWVudChcInNlY3Rpb25cIix7c3R5bGU6ey4uLnYud3JhcHBlcix3aWR0aDplLGhlaWdodDpyfSwuLi5FfSwhbiYmSy5jcmVhdGVFbGVtZW50KCQsbnVsbCx0KSxLLmNyZWF0ZUVsZW1lbnQoXCJkaXZcIix7cmVmOmEsc3R5bGU6ey4uLnYuZnVsbFdpZHRoLC4uLiFuJiZ2LmhpZGV9LGNsYXNzTmFtZTptfSkpfXZhciBlZT1FZTt2YXIgSD15ZShlZSk7aW1wb3J0e3VzZUVmZmVjdCBhcyB4ZX1mcm9tXCJyZWFjdFwiO2Z1bmN0aW9uIENlKGUpe3hlKGUsW10pfXZhciBrPUNlO2ltcG9ydHt1c2VFZmZlY3QgYXMgZ2UsdXNlUmVmIGFzIFJlfWZyb21cInJlYWN0XCI7ZnVuY3Rpb24gaGUoZSxyLG49ITApe2xldCB0PVJlKCEwKTtnZSh0LmN1cnJlbnR8fCFuPygpPT57dC5jdXJyZW50PSExfTplLHIpfXZhciBsPWhlO2Z1bmN0aW9uIEQoKXt9ZnVuY3Rpb24gaChlLHIsbix0KXtyZXR1cm4gRGUoZSx0KXx8YmUoZSxyLG4sdCl9ZnVuY3Rpb24gRGUoZSxyKXtyZXR1cm4gZS5lZGl0b3IuZ2V0TW9kZWwodGUoZSxyKSl9ZnVuY3Rpb24gYmUoZSxyLG4sdCl7cmV0dXJuIGUuZWRpdG9yLmNyZWF0ZU1vZGVsKHIsbix0P3RlKGUsdCk6dm9pZCAwKX1mdW5jdGlvbiB0ZShlLHIpe3JldHVybiBlLlVyaS5wYXJzZShyKX1mdW5jdGlvbiBPZSh7b3JpZ2luYWw6ZSxtb2RpZmllZDpyLGxhbmd1YWdlOm4sb3JpZ2luYWxMYW5ndWFnZTp0LG1vZGlmaWVkTGFuZ3VhZ2U6YSxvcmlnaW5hbE1vZGVsUGF0aDptLG1vZGlmaWVkTW9kZWxQYXRoOkUsa2VlcEN1cnJlbnRPcmlnaW5hbE1vZGVsOmc9ITEsa2VlcEN1cnJlbnRNb2RpZmllZE1vZGVsOk49ITEsdGhlbWU6eD1cImxpZ2h0XCIsbG9hZGluZzpQPVwiTG9hZGluZy4uLlwiLG9wdGlvbnM6eT17fSxoZWlnaHQ6Vj1cIjEwMCVcIix3aWR0aDp6PVwiMTAwJVwiLGNsYXNzTmFtZTpGLHdyYXBwZXJQcm9wczpqPXt9LGJlZm9yZU1vdW50OkE9RCxvbk1vdW50OnE9RH0pe2xldFtNLE9dPXJlKCExKSxbVCxzXT1yZSghMCksdT1TKG51bGwpLGM9UyhudWxsKSx3PVMobnVsbCksZD1TKHEpLG89UyhBKSxiPVMoITEpO2soKCk9PntsZXQgaT1TZS5pbml0KCk7cmV0dXJuIGkudGhlbihmPT4oYy5jdXJyZW50PWYpJiZzKCExKSkuY2F0Y2goZj0+Zj8udHlwZSE9PVwiY2FuY2VsYXRpb25cIiYmY29uc29sZS5lcnJvcihcIk1vbmFjbyBpbml0aWFsaXphdGlvbjogZXJyb3I6XCIsZikpLCgpPT51LmN1cnJlbnQ/SSgpOmkuY2FuY2VsKCl9KSxsKCgpPT57aWYodS5jdXJyZW50JiZjLmN1cnJlbnQpe2xldCBpPXUuY3VycmVudC5nZXRPcmlnaW5hbEVkaXRvcigpLGY9aChjLmN1cnJlbnQsZXx8XCJcIix0fHxufHxcInRleHRcIixtfHxcIlwiKTtmIT09aS5nZXRNb2RlbCgpJiZpLnNldE1vZGVsKGYpfX0sW21dLE0pLGwoKCk9PntpZih1LmN1cnJlbnQmJmMuY3VycmVudCl7bGV0IGk9dS5jdXJyZW50LmdldE1vZGlmaWVkRWRpdG9yKCksZj1oKGMuY3VycmVudCxyfHxcIlwiLGF8fG58fFwidGV4dFwiLEV8fFwiXCIpO2YhPT1pLmdldE1vZGVsKCkmJmkuc2V0TW9kZWwoZil9fSxbRV0sTSksbCgoKT0+e2xldCBpPXUuY3VycmVudC5nZXRNb2RpZmllZEVkaXRvcigpO2kuZ2V0T3B0aW9uKGMuY3VycmVudC5lZGl0b3IuRWRpdG9yT3B0aW9uLnJlYWRPbmx5KT9pLnNldFZhbHVlKHJ8fFwiXCIpOnIhPT1pLmdldFZhbHVlKCkmJihpLmV4ZWN1dGVFZGl0cyhcIlwiLFt7cmFuZ2U6aS5nZXRNb2RlbCgpLmdldEZ1bGxNb2RlbFJhbmdlKCksdGV4dDpyfHxcIlwiLGZvcmNlTW92ZU1hcmtlcnM6ITB9XSksaS5wdXNoVW5kb1N0b3AoKSl9LFtyXSxNKSxsKCgpPT57dS5jdXJyZW50Py5nZXRNb2RlbCgpPy5vcmlnaW5hbC5zZXRWYWx1ZShlfHxcIlwiKX0sW2VdLE0pLGwoKCk9PntsZXR7b3JpZ2luYWw6aSxtb2RpZmllZDpmfT11LmN1cnJlbnQuZ2V0TW9kZWwoKTtjLmN1cnJlbnQuZWRpdG9yLnNldE1vZGVsTGFuZ3VhZ2UoaSx0fHxufHxcInRleHRcIiksYy5jdXJyZW50LmVkaXRvci5zZXRNb2RlbExhbmd1YWdlKGYsYXx8bnx8XCJ0ZXh0XCIpfSxbbix0LGFdLE0pLGwoKCk9PntjLmN1cnJlbnQ/LmVkaXRvci5zZXRUaGVtZSh4KX0sW3hdLE0pLGwoKCk9Pnt1LmN1cnJlbnQ/LnVwZGF0ZU9wdGlvbnMoeSl9LFt5XSxNKTtsZXQgTD1vZSgoKT0+e2lmKCFjLmN1cnJlbnQpcmV0dXJuO28uY3VycmVudChjLmN1cnJlbnQpO2xldCBpPWgoYy5jdXJyZW50LGV8fFwiXCIsdHx8bnx8XCJ0ZXh0XCIsbXx8XCJcIiksZj1oKGMuY3VycmVudCxyfHxcIlwiLGF8fG58fFwidGV4dFwiLEV8fFwiXCIpO3UuY3VycmVudD8uc2V0TW9kZWwoe29yaWdpbmFsOmksbW9kaWZpZWQ6Zn0pfSxbbixyLGEsZSx0LG0sRV0pLFU9b2UoKCk9PnshYi5jdXJyZW50JiZ3LmN1cnJlbnQmJih1LmN1cnJlbnQ9Yy5jdXJyZW50LmVkaXRvci5jcmVhdGVEaWZmRWRpdG9yKHcuY3VycmVudCx7YXV0b21hdGljTGF5b3V0OiEwLC4uLnl9KSxMKCksYy5jdXJyZW50Py5lZGl0b3Iuc2V0VGhlbWUoeCksTyghMCksYi5jdXJyZW50PSEwKX0sW3kseCxMXSk7bmUoKCk9PntNJiZkLmN1cnJlbnQodS5jdXJyZW50LGMuY3VycmVudCl9LFtNXSksbmUoKCk9PnshVCYmIU0mJlUoKX0sW1QsTSxVXSk7ZnVuY3Rpb24gSSgpe2xldCBpPXUuY3VycmVudD8uZ2V0TW9kZWwoKTtnfHxpPy5vcmlnaW5hbD8uZGlzcG9zZSgpLE58fGk/Lm1vZGlmaWVkPy5kaXNwb3NlKCksdS5jdXJyZW50Py5kaXNwb3NlKCl9cmV0dXJuIGtlLmNyZWF0ZUVsZW1lbnQoSCx7d2lkdGg6eixoZWlnaHQ6Vixpc0VkaXRvclJlYWR5Ok0sbG9hZGluZzpQLF9yZWY6dyxjbGFzc05hbWU6Rix3cmFwcGVyUHJvcHM6an0pfXZhciBpZT1PZTt2YXIgd2U9VGUoaWUpO2ltcG9ydHt1c2VTdGF0ZSBhcyBJZX1mcm9tXCJyZWFjdFwiO2ltcG9ydCBjZSBmcm9tXCJAbW9uYWNvLWVkaXRvci9sb2FkZXJcIjtmdW5jdGlvbiBQZSgpe2xldFtlLHJdPUllKGNlLl9fZ2V0TW9uYWNvSW5zdGFuY2UoKSk7cmV0dXJuIGsoKCk9PntsZXQgbjtyZXR1cm4gZXx8KG49Y2UuaW5pdCgpLG4udGhlbih0PT57cih0KX0pKSwoKT0+bj8uY2FuY2VsKCl9KSxlfXZhciBMZT1QZTtpbXBvcnR7bWVtbyBhcyB6ZX1mcm9tXCJyZWFjdFwiO2ltcG9ydCBXZSx7dXNlU3RhdGUgYXMgdWUsdXNlRWZmZWN0IGFzIFcsdXNlUmVmIGFzIEMsdXNlQ2FsbGJhY2sgYXMgX2V9ZnJvbVwicmVhY3RcIjtpbXBvcnQgTmUgZnJvbVwiQG1vbmFjby1lZGl0b3IvbG9hZGVyXCI7aW1wb3J0e3VzZUVmZmVjdCBhcyBVZSx1c2VSZWYgYXMgdmV9ZnJvbVwicmVhY3RcIjtmdW5jdGlvbiBIZShlKXtsZXQgcj12ZSgpO3JldHVybiBVZSgoKT0+e3IuY3VycmVudD1lfSxbZV0pLHIuY3VycmVudH12YXIgc2U9SGU7dmFyIF89bmV3IE1hcDtmdW5jdGlvbiBWZSh7ZGVmYXVsdFZhbHVlOmUsZGVmYXVsdExhbmd1YWdlOnIsZGVmYXVsdFBhdGg6bix2YWx1ZTp0LGxhbmd1YWdlOmEscGF0aDptLHRoZW1lOkU9XCJsaWdodFwiLGxpbmU6Zyxsb2FkaW5nOk49XCJMb2FkaW5nLi4uXCIsb3B0aW9uczp4PXt9LG92ZXJyaWRlU2VydmljZXM6UD17fSxzYXZlVmlld1N0YXRlOnk9ITAsa2VlcEN1cnJlbnRNb2RlbDpWPSExLHdpZHRoOno9XCIxMDAlXCIsaGVpZ2h0OkY9XCIxMDAlXCIsY2xhc3NOYW1lOmosd3JhcHBlclByb3BzOkE9e30sYmVmb3JlTW91bnQ6cT1ELG9uTW91bnQ6TT1ELG9uQ2hhbmdlOk8sb25WYWxpZGF0ZTpUPUR9KXtsZXRbcyx1XT11ZSghMSksW2Msd109dWUoITApLGQ9QyhudWxsKSxvPUMobnVsbCksYj1DKG51bGwpLEw9QyhNKSxVPUMocSksST1DKCksaT1DKHQpLGY9c2UobSksUT1DKCExKSxCPUMoITEpO2soKCk9PntsZXQgcD1OZS5pbml0KCk7cmV0dXJuIHAudGhlbihSPT4oZC5jdXJyZW50PVIpJiZ3KCExKSkuY2F0Y2goUj0+Uj8udHlwZSE9PVwiY2FuY2VsYXRpb25cIiYmY29uc29sZS5lcnJvcihcIk1vbmFjbyBpbml0aWFsaXphdGlvbjogZXJyb3I6XCIsUikpLCgpPT5vLmN1cnJlbnQ/cGUoKTpwLmNhbmNlbCgpfSksbCgoKT0+e2xldCBwPWgoZC5jdXJyZW50LGV8fHR8fFwiXCIscnx8YXx8XCJcIixtfHxufHxcIlwiKTtwIT09by5jdXJyZW50Py5nZXRNb2RlbCgpJiYoeSYmXy5zZXQoZixvLmN1cnJlbnQ/LnNhdmVWaWV3U3RhdGUoKSksby5jdXJyZW50Py5zZXRNb2RlbChwKSx5JiZvLmN1cnJlbnQ/LnJlc3RvcmVWaWV3U3RhdGUoXy5nZXQobSkpKX0sW21dLHMpLGwoKCk9PntvLmN1cnJlbnQ/LnVwZGF0ZU9wdGlvbnMoeCl9LFt4XSxzKSxsKCgpPT57IW8uY3VycmVudHx8dD09PXZvaWQgMHx8KG8uY3VycmVudC5nZXRPcHRpb24oZC5jdXJyZW50LmVkaXRvci5FZGl0b3JPcHRpb24ucmVhZE9ubHkpP28uY3VycmVudC5zZXRWYWx1ZSh0KTp0IT09by5jdXJyZW50LmdldFZhbHVlKCkmJihCLmN1cnJlbnQ9ITAsby5jdXJyZW50LmV4ZWN1dGVFZGl0cyhcIlwiLFt7cmFuZ2U6by5jdXJyZW50LmdldE1vZGVsKCkuZ2V0RnVsbE1vZGVsUmFuZ2UoKSx0ZXh0OnQsZm9yY2VNb3ZlTWFya2VyczohMH1dKSxvLmN1cnJlbnQucHVzaFVuZG9TdG9wKCksQi5jdXJyZW50PSExKSl9LFt0XSxzKSxsKCgpPT57bGV0IHA9by5jdXJyZW50Py5nZXRNb2RlbCgpO3AmJmEmJmQuY3VycmVudD8uZWRpdG9yLnNldE1vZGVsTGFuZ3VhZ2UocCxhKX0sW2FdLHMpLGwoKCk9PntnIT09dm9pZCAwJiZvLmN1cnJlbnQ/LnJldmVhbExpbmUoZyl9LFtnXSxzKSxsKCgpPT57ZC5jdXJyZW50Py5lZGl0b3Iuc2V0VGhlbWUoRSl9LFtFXSxzKTtsZXQgWD1fZSgoKT0+e2lmKCEoIWIuY3VycmVudHx8IWQuY3VycmVudCkmJiFRLmN1cnJlbnQpe1UuY3VycmVudChkLmN1cnJlbnQpO2xldCBwPW18fG4sUj1oKGQuY3VycmVudCx0fHxlfHxcIlwiLHJ8fGF8fFwiXCIscHx8XCJcIik7by5jdXJyZW50PWQuY3VycmVudD8uZWRpdG9yLmNyZWF0ZShiLmN1cnJlbnQse21vZGVsOlIsYXV0b21hdGljTGF5b3V0OiEwLC4uLnh9LFApLHkmJm8uY3VycmVudC5yZXN0b3JlVmlld1N0YXRlKF8uZ2V0KHApKSxkLmN1cnJlbnQuZWRpdG9yLnNldFRoZW1lKEUpLGchPT12b2lkIDAmJm8uY3VycmVudC5yZXZlYWxMaW5lKGcpLHUoITApLFEuY3VycmVudD0hMH19LFtlLHIsbix0LGEsbSx4LFAseSxFLGddKTtXKCgpPT57cyYmTC5jdXJyZW50KG8uY3VycmVudCxkLmN1cnJlbnQpfSxbc10pLFcoKCk9PnshYyYmIXMmJlgoKX0sW2MscyxYXSksaS5jdXJyZW50PXQsVygoKT0+e3MmJk8mJihJLmN1cnJlbnQ/LmRpc3Bvc2UoKSxJLmN1cnJlbnQ9by5jdXJyZW50Py5vbkRpZENoYW5nZU1vZGVsQ29udGVudChwPT57Qi5jdXJyZW50fHxPKG8uY3VycmVudC5nZXRWYWx1ZSgpLHApfSkpfSxbcyxPXSksVygoKT0+e2lmKHMpe2xldCBwPWQuY3VycmVudC5lZGl0b3Iub25EaWRDaGFuZ2VNYXJrZXJzKFI9PntsZXQgRz1vLmN1cnJlbnQuZ2V0TW9kZWwoKT8udXJpO2lmKEcmJlIuZmluZChKPT5KLnBhdGg9PT1HLnBhdGgpKXtsZXQgSj1kLmN1cnJlbnQuZWRpdG9yLmdldE1vZGVsTWFya2Vycyh7cmVzb3VyY2U6R30pO1Q/LihKKX19KTtyZXR1cm4oKT0+e3A/LmRpc3Bvc2UoKX19cmV0dXJuKCk9Pnt9fSxbcyxUXSk7ZnVuY3Rpb24gcGUoKXtJLmN1cnJlbnQ/LmRpc3Bvc2UoKSxWP3kmJl8uc2V0KG0sby5jdXJyZW50LnNhdmVWaWV3U3RhdGUoKSk6by5jdXJyZW50LmdldE1vZGVsKCk/LmRpc3Bvc2UoKSxvLmN1cnJlbnQuZGlzcG9zZSgpfXJldHVybiBXZS5jcmVhdGVFbGVtZW50KEgse3dpZHRoOnosaGVpZ2h0OkYsaXNFZGl0b3JSZWFkeTpzLGxvYWRpbmc6TixfcmVmOmIsY2xhc3NOYW1lOmosd3JhcHBlclByb3BzOkF9KX12YXIgZmU9VmU7dmFyIGRlPXplKGZlKTt2YXIgRnQ9ZGU7ZXhwb3J0e3dlIGFzIERpZmZFZGl0b3IsZGUgYXMgRWRpdG9yLEZ0IGFzIGRlZmF1bHQsX3QgYXMgbG9hZGVyLExlIGFzIHVzZU1vbmFjb307XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/react/dist/index.mjs\n");

/***/ })

};
;