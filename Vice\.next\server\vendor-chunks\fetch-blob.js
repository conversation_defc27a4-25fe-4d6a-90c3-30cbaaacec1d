/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fetch-blob";
exports.ids = ["vendor-chunks/fetch-blob"];
exports.modules = {

/***/ "(action-browser)/./node_modules/fetch-blob/file.js":
/*!*****************************************!*\
  !*** ./node_modules/fetch-blob/file.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   File: () => (/* binding */ File),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(action-browser)/./node_modules/fetch-blob/index.js\");\n\n\nconst _File = class File extends _index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] {\n  #lastModified = 0\n  #name = ''\n\n  /**\n   * @param {*[]} fileBits\n   * @param {string} fileName\n   * @param {{lastModified?: number, type?: string}} options\n   */// @ts-ignore\n  constructor (fileBits, fileName, options = {}) {\n    if (arguments.length < 2) {\n      throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`)\n    }\n    super(fileBits, options)\n\n    if (options === null) options = {}\n\n    // Simulate WebIDL type casting for NaN value in lastModified option.\n    const lastModified = options.lastModified === undefined ? Date.now() : Number(options.lastModified)\n    if (!Number.isNaN(lastModified)) {\n      this.#lastModified = lastModified\n    }\n\n    this.#name = String(fileName)\n  }\n\n  get name () {\n    return this.#name\n  }\n\n  get lastModified () {\n    return this.#lastModified\n  }\n\n  get [Symbol.toStringTag] () {\n    return 'File'\n  }\n\n  static [Symbol.hasInstance] (object) {\n    return !!object && object instanceof _index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] &&\n      /^(File)$/.test(object[Symbol.toStringTag])\n  }\n}\n\n/** @type {typeof globalThis.File} */// @ts-ignore\nconst File = _File\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (File);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fetch-blob/file.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/fetch-blob/from.js":
/*!*****************************************!*\
  !*** ./node_modules/fetch-blob/from.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   File: () => (/* reexport safe */ _file_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   blobFrom: () => (/* binding */ blobFrom),\n/* harmony export */   blobFromSync: () => (/* binding */ blobFromSync),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fileFrom: () => (/* binding */ fileFrom),\n/* harmony export */   fileFromSync: () => (/* binding */ fileFromSync)\n/* harmony export */ });\n/* harmony import */ var node_fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:fs */ \"node:fs\");\n/* harmony import */ var node_path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:path */ \"node:path\");\n/* harmony import */ var node_domexception__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node-domexception */ \"(action-browser)/./node_modules/node-domexception/index.js\");\n/* harmony import */ var _file_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./file.js */ \"(action-browser)/./node_modules/fetch-blob/file.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./index.js */ \"(action-browser)/./node_modules/fetch-blob/index.js\");\n\n\n\n\n\n\n\nconst { stat } = node_fs__WEBPACK_IMPORTED_MODULE_0__.promises\n\n/**\n * @param {string} path filepath on the disk\n * @param {string} [type] mimetype to use\n */\nconst blobFromSync = (path, type) => fromBlob((0,node_fs__WEBPACK_IMPORTED_MODULE_0__.statSync)(path), path, type)\n\n/**\n * @param {string} path filepath on the disk\n * @param {string} [type] mimetype to use\n * @returns {Promise<Blob>}\n */\nconst blobFrom = (path, type) => stat(path).then(stat => fromBlob(stat, path, type))\n\n/**\n * @param {string} path filepath on the disk\n * @param {string} [type] mimetype to use\n * @returns {Promise<File>}\n */\nconst fileFrom = (path, type) => stat(path).then(stat => fromFile(stat, path, type))\n\n/**\n * @param {string} path filepath on the disk\n * @param {string} [type] mimetype to use\n */\nconst fileFromSync = (path, type) => fromFile((0,node_fs__WEBPACK_IMPORTED_MODULE_0__.statSync)(path), path, type)\n\n// @ts-ignore\nconst fromBlob = (stat, path, type = '') => new _index_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]([new BlobDataItem({\n  path,\n  size: stat.size,\n  lastModified: stat.mtimeMs,\n  start: 0\n})], { type })\n\n// @ts-ignore\nconst fromFile = (stat, path, type = '') => new _file_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]([new BlobDataItem({\n  path,\n  size: stat.size,\n  lastModified: stat.mtimeMs,\n  start: 0\n})], (0,node_path__WEBPACK_IMPORTED_MODULE_1__.basename)(path), { type, lastModified: stat.mtimeMs })\n\n/**\n * This is a blob backed up by a file on the disk\n * with minium requirement. Its wrapped around a Blob as a blobPart\n * so you have no direct access to this.\n *\n * @private\n */\nclass BlobDataItem {\n  #path\n  #start\n\n  constructor (options) {\n    this.#path = options.path\n    this.#start = options.start\n    this.size = options.size\n    this.lastModified = options.lastModified\n  }\n\n  /**\n   * Slicing arguments is first validated and formatted\n   * to not be out of range by Blob.prototype.slice\n   */\n  slice (start, end) {\n    return new BlobDataItem({\n      path: this.#path,\n      lastModified: this.lastModified,\n      size: end - start,\n      start: this.#start + start\n    })\n  }\n\n  async * stream () {\n    const { mtimeMs } = await stat(this.#path)\n    if (mtimeMs > this.lastModified) {\n      throw new node_domexception__WEBPACK_IMPORTED_MODULE_2__('The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.', 'NotReadableError')\n    }\n    yield * (0,node_fs__WEBPACK_IMPORTED_MODULE_0__.createReadStream)(this.#path, {\n      start: this.#start,\n      end: this.#start + this.size - 1\n    })\n  }\n\n  get [Symbol.toStringTag] () {\n    return 'Blob'\n  }\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (blobFromSync);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fetch-blob/from.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/fetch-blob/index.js":
/*!******************************************!*\
  !*** ./node_modules/fetch-blob/index.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* binding */ Blob),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _streams_cjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./streams.cjs */ \"(action-browser)/./node_modules/fetch-blob/streams.cjs\");\n/*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */\n\n// TODO (jimmywarting): in the feature use conditional loading with top level await (requires 14.x)\n// Node has recently added whatwg stream into core\n\n\n\n// 64 KiB (same size chrome slice theirs blob into Uint8array's)\nconst POOL_SIZE = 65536\n\n/** @param {(Blob | Uint8Array)[]} parts */\nasync function * toIterator (parts, clone = true) {\n  for (const part of parts) {\n    if ('stream' in part) {\n      yield * (/** @type {AsyncIterableIterator<Uint8Array>} */ (part.stream()))\n    } else if (ArrayBuffer.isView(part)) {\n      if (clone) {\n        let position = part.byteOffset\n        const end = part.byteOffset + part.byteLength\n        while (position !== end) {\n          const size = Math.min(end - position, POOL_SIZE)\n          const chunk = part.buffer.slice(position, position + size)\n          position += chunk.byteLength\n          yield new Uint8Array(chunk)\n        }\n      } else {\n        yield part\n      }\n    /* c8 ignore next 10 */\n    } else {\n      // For blobs that have arrayBuffer but no stream method (nodes buffer.Blob)\n      let position = 0, b = (/** @type {Blob} */ (part))\n      while (position !== b.size) {\n        const chunk = b.slice(position, Math.min(b.size, position + POOL_SIZE))\n        const buffer = await chunk.arrayBuffer()\n        position += buffer.byteLength\n        yield new Uint8Array(buffer)\n      }\n    }\n  }\n}\n\nconst _Blob = class Blob {\n  /** @type {Array.<(Blob|Uint8Array)>} */\n  #parts = []\n  #type = ''\n  #size = 0\n  #endings = 'transparent'\n\n  /**\n   * The Blob() constructor returns a new Blob object. The content\n   * of the blob consists of the concatenation of the values given\n   * in the parameter array.\n   *\n   * @param {*} blobParts\n   * @param {{ type?: string, endings?: string }} [options]\n   */\n  constructor (blobParts = [], options = {}) {\n    if (typeof blobParts !== 'object' || blobParts === null) {\n      throw new TypeError('Failed to construct \\'Blob\\': The provided value cannot be converted to a sequence.')\n    }\n\n    if (typeof blobParts[Symbol.iterator] !== 'function') {\n      throw new TypeError('Failed to construct \\'Blob\\': The object must have a callable @@iterator property.')\n    }\n\n    if (typeof options !== 'object' && typeof options !== 'function') {\n      throw new TypeError('Failed to construct \\'Blob\\': parameter 2 cannot convert to dictionary.')\n    }\n\n    if (options === null) options = {}\n\n    const encoder = new TextEncoder()\n    for (const element of blobParts) {\n      let part\n      if (ArrayBuffer.isView(element)) {\n        part = new Uint8Array(element.buffer.slice(element.byteOffset, element.byteOffset + element.byteLength))\n      } else if (element instanceof ArrayBuffer) {\n        part = new Uint8Array(element.slice(0))\n      } else if (element instanceof Blob) {\n        part = element\n      } else {\n        part = encoder.encode(`${element}`)\n      }\n\n      this.#size += ArrayBuffer.isView(part) ? part.byteLength : part.size\n      this.#parts.push(part)\n    }\n\n    this.#endings = `${options.endings === undefined ? 'transparent' : options.endings}`\n    const type = options.type === undefined ? '' : String(options.type)\n    this.#type = /^[\\x20-\\x7E]*$/.test(type) ? type : ''\n  }\n\n  /**\n   * The Blob interface's size property returns the\n   * size of the Blob in bytes.\n   */\n  get size () {\n    return this.#size\n  }\n\n  /**\n   * The type property of a Blob object returns the MIME type of the file.\n   */\n  get type () {\n    return this.#type\n  }\n\n  /**\n   * The text() method in the Blob interface returns a Promise\n   * that resolves with a string containing the contents of\n   * the blob, interpreted as UTF-8.\n   *\n   * @return {Promise<string>}\n   */\n  async text () {\n    // More optimized than using this.arrayBuffer()\n    // that requires twice as much ram\n    const decoder = new TextDecoder()\n    let str = ''\n    for await (const part of toIterator(this.#parts, false)) {\n      str += decoder.decode(part, { stream: true })\n    }\n    // Remaining\n    str += decoder.decode()\n    return str\n  }\n\n  /**\n   * The arrayBuffer() method in the Blob interface returns a\n   * Promise that resolves with the contents of the blob as\n   * binary data contained in an ArrayBuffer.\n   *\n   * @return {Promise<ArrayBuffer>}\n   */\n  async arrayBuffer () {\n    // Easier way... Just a unnecessary overhead\n    // const view = new Uint8Array(this.size);\n    // await this.stream().getReader({mode: 'byob'}).read(view);\n    // return view.buffer;\n\n    const data = new Uint8Array(this.size)\n    let offset = 0\n    for await (const chunk of toIterator(this.#parts, false)) {\n      data.set(chunk, offset)\n      offset += chunk.length\n    }\n\n    return data.buffer\n  }\n\n  stream () {\n    const it = toIterator(this.#parts, true)\n\n    return new globalThis.ReadableStream({\n      // @ts-ignore\n      type: 'bytes',\n      async pull (ctrl) {\n        const chunk = await it.next()\n        chunk.done ? ctrl.close() : ctrl.enqueue(chunk.value)\n      },\n\n      async cancel () {\n        await it.return()\n      }\n    })\n  }\n\n  /**\n   * The Blob interface's slice() method creates and returns a\n   * new Blob object which contains data from a subset of the\n   * blob on which it's called.\n   *\n   * @param {number} [start]\n   * @param {number} [end]\n   * @param {string} [type]\n   */\n  slice (start = 0, end = this.size, type = '') {\n    const { size } = this\n\n    let relativeStart = start < 0 ? Math.max(size + start, 0) : Math.min(start, size)\n    let relativeEnd = end < 0 ? Math.max(size + end, 0) : Math.min(end, size)\n\n    const span = Math.max(relativeEnd - relativeStart, 0)\n    const parts = this.#parts\n    const blobParts = []\n    let added = 0\n\n    for (const part of parts) {\n      // don't add the overflow to new blobParts\n      if (added >= span) {\n        break\n      }\n\n      const size = ArrayBuffer.isView(part) ? part.byteLength : part.size\n      if (relativeStart && size <= relativeStart) {\n        // Skip the beginning and change the relative\n        // start & end position as we skip the unwanted parts\n        relativeStart -= size\n        relativeEnd -= size\n      } else {\n        let chunk\n        if (ArrayBuffer.isView(part)) {\n          chunk = part.subarray(relativeStart, Math.min(size, relativeEnd))\n          added += chunk.byteLength\n        } else {\n          chunk = part.slice(relativeStart, Math.min(size, relativeEnd))\n          added += chunk.size\n        }\n        relativeEnd -= size\n        blobParts.push(chunk)\n        relativeStart = 0 // All next sequential parts should start at 0\n      }\n    }\n\n    const blob = new Blob([], { type: String(type).toLowerCase() })\n    blob.#size = span\n    blob.#parts = blobParts\n\n    return blob\n  }\n\n  get [Symbol.toStringTag] () {\n    return 'Blob'\n  }\n\n  static [Symbol.hasInstance] (object) {\n    return (\n      object &&\n      typeof object === 'object' &&\n      typeof object.constructor === 'function' &&\n      (\n        typeof object.stream === 'function' ||\n        typeof object.arrayBuffer === 'function'\n      ) &&\n      /^(Blob|File)$/.test(object[Symbol.toStringTag])\n    )\n  }\n}\n\nObject.defineProperties(_Blob.prototype, {\n  size: { enumerable: true },\n  type: { enumerable: true },\n  slice: { enumerable: true }\n})\n\n/** @type {typeof globalThis.Blob} */\nconst Blob = _Blob\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Blob);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fetch-blob/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/fetch-blob/streams.cjs":
/*!*********************************************!*\
  !*** ./node_modules/fetch-blob/streams.cjs ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("/* c8 ignore start */\n// 64 KiB (same size chrome slice theirs blob into Uint8array's)\nconst POOL_SIZE = 65536\n\nif (!globalThis.ReadableStream) {\n  // `node:stream/web` got introduced in v16.5.0 as experimental\n  // and it's preferred over the polyfilled version. So we also\n  // suppress the warning that gets emitted by NodeJS for using it.\n  try {\n    const process = __webpack_require__(/*! node:process */ \"node:process\")\n    const { emitWarning } = process\n    try {\n      process.emitWarning = () => {}\n      Object.assign(globalThis, __webpack_require__(/*! node:stream/web */ \"node:stream/web\"))\n      process.emitWarning = emitWarning\n    } catch (error) {\n      process.emitWarning = emitWarning\n      throw error\n    }\n  } catch (error) {\n    // fallback to polyfill implementation\n    Object.assign(globalThis, __webpack_require__(/*! web-streams-polyfill/dist/ponyfill.es2018.js */ \"(action-browser)/./node_modules/web-streams-polyfill/dist/ponyfill.es2018.js\"))\n  }\n}\n\ntry {\n  // Don't use node: prefix for this, require+node: is not supported until node v14.14\n  // Only `import()` can use prefix in 12.20 and later\n  const { Blob } = __webpack_require__(/*! buffer */ \"buffer\")\n  if (Blob && !Blob.prototype.stream) {\n    Blob.prototype.stream = function name (params) {\n      let position = 0\n      const blob = this\n\n      return new ReadableStream({\n        type: 'bytes',\n        async pull (ctrl) {\n          const chunk = blob.slice(position, Math.min(blob.size, position + POOL_SIZE))\n          const buffer = await chunk.arrayBuffer()\n          position += buffer.byteLength\n          ctrl.enqueue(new Uint8Array(buffer))\n\n          if (position === blob.size) {\n            ctrl.close()\n          }\n        }\n      })\n    }\n  }\n} catch (error) {}\n/* c8 ignore end */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mZXRjaC1ibG9iL3N0cmVhbXMuY2pzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLG1CQUFPLENBQUMsa0NBQWM7QUFDMUMsWUFBWSxjQUFjO0FBQzFCO0FBQ0E7QUFDQSxnQ0FBZ0MsbUJBQU8sQ0FBQyx3Q0FBaUI7QUFDekQ7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsOEJBQThCLG1CQUFPLENBQUMsa0lBQThDO0FBQ3BGO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsVUFBVSxPQUFPLEVBQUUsbUJBQU8sQ0FBQyxzQkFBUTtBQUNuQztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0EsRUFBRTtBQUNGIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXE5ldyBmb2xkZXJcXFZpY2VcXFZpY2VcXG5vZGVfbW9kdWxlc1xcZmV0Y2gtYmxvYlxcc3RyZWFtcy5janMiXSwic291cmNlc0NvbnRlbnQiOlsiLyogYzggaWdub3JlIHN0YXJ0ICovXG4vLyA2NCBLaUIgKHNhbWUgc2l6ZSBjaHJvbWUgc2xpY2UgdGhlaXJzIGJsb2IgaW50byBVaW50OGFycmF5J3MpXG5jb25zdCBQT09MX1NJWkUgPSA2NTUzNlxuXG5pZiAoIWdsb2JhbFRoaXMuUmVhZGFibGVTdHJlYW0pIHtcbiAgLy8gYG5vZGU6c3RyZWFtL3dlYmAgZ290IGludHJvZHVjZWQgaW4gdjE2LjUuMCBhcyBleHBlcmltZW50YWxcbiAgLy8gYW5kIGl0J3MgcHJlZmVycmVkIG92ZXIgdGhlIHBvbHlmaWxsZWQgdmVyc2lvbi4gU28gd2UgYWxzb1xuICAvLyBzdXBwcmVzcyB0aGUgd2FybmluZyB0aGF0IGdldHMgZW1pdHRlZCBieSBOb2RlSlMgZm9yIHVzaW5nIGl0LlxuICB0cnkge1xuICAgIGNvbnN0IHByb2Nlc3MgPSByZXF1aXJlKCdub2RlOnByb2Nlc3MnKVxuICAgIGNvbnN0IHsgZW1pdFdhcm5pbmcgfSA9IHByb2Nlc3NcbiAgICB0cnkge1xuICAgICAgcHJvY2Vzcy5lbWl0V2FybmluZyA9ICgpID0+IHt9XG4gICAgICBPYmplY3QuYXNzaWduKGdsb2JhbFRoaXMsIHJlcXVpcmUoJ25vZGU6c3RyZWFtL3dlYicpKVxuICAgICAgcHJvY2Vzcy5lbWl0V2FybmluZyA9IGVtaXRXYXJuaW5nXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHByb2Nlc3MuZW1pdFdhcm5pbmcgPSBlbWl0V2FybmluZ1xuICAgICAgdGhyb3cgZXJyb3JcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgLy8gZmFsbGJhY2sgdG8gcG9seWZpbGwgaW1wbGVtZW50YXRpb25cbiAgICBPYmplY3QuYXNzaWduKGdsb2JhbFRoaXMsIHJlcXVpcmUoJ3dlYi1zdHJlYW1zLXBvbHlmaWxsL2Rpc3QvcG9ueWZpbGwuZXMyMDE4LmpzJykpXG4gIH1cbn1cblxudHJ5IHtcbiAgLy8gRG9uJ3QgdXNlIG5vZGU6IHByZWZpeCBmb3IgdGhpcywgcmVxdWlyZStub2RlOiBpcyBub3Qgc3VwcG9ydGVkIHVudGlsIG5vZGUgdjE0LjE0XG4gIC8vIE9ubHkgYGltcG9ydCgpYCBjYW4gdXNlIHByZWZpeCBpbiAxMi4yMCBhbmQgbGF0ZXJcbiAgY29uc3QgeyBCbG9iIH0gPSByZXF1aXJlKCdidWZmZXInKVxuICBpZiAoQmxvYiAmJiAhQmxvYi5wcm90b3R5cGUuc3RyZWFtKSB7XG4gICAgQmxvYi5wcm90b3R5cGUuc3RyZWFtID0gZnVuY3Rpb24gbmFtZSAocGFyYW1zKSB7XG4gICAgICBsZXQgcG9zaXRpb24gPSAwXG4gICAgICBjb25zdCBibG9iID0gdGhpc1xuXG4gICAgICByZXR1cm4gbmV3IFJlYWRhYmxlU3RyZWFtKHtcbiAgICAgICAgdHlwZTogJ2J5dGVzJyxcbiAgICAgICAgYXN5bmMgcHVsbCAoY3RybCkge1xuICAgICAgICAgIGNvbnN0IGNodW5rID0gYmxvYi5zbGljZShwb3NpdGlvbiwgTWF0aC5taW4oYmxvYi5zaXplLCBwb3NpdGlvbiArIFBPT0xfU0laRSkpXG4gICAgICAgICAgY29uc3QgYnVmZmVyID0gYXdhaXQgY2h1bmsuYXJyYXlCdWZmZXIoKVxuICAgICAgICAgIHBvc2l0aW9uICs9IGJ1ZmZlci5ieXRlTGVuZ3RoXG4gICAgICAgICAgY3RybC5lbnF1ZXVlKG5ldyBVaW50OEFycmF5KGJ1ZmZlcikpXG5cbiAgICAgICAgICBpZiAocG9zaXRpb24gPT09IGJsb2Iuc2l6ZSkge1xuICAgICAgICAgICAgY3RybC5jbG9zZSgpXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9KVxuICAgIH1cbiAgfVxufSBjYXRjaCAoZXJyb3IpIHt9XG4vKiBjOCBpZ25vcmUgZW5kICovXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fetch-blob/streams.cjs\n");

/***/ })

};
;