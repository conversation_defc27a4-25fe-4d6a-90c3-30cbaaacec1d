"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/stack/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/create-project-modal.tsx":
/*!*********************************************************!*\
  !*** ./src/components/project/create-project-modal.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateProjectModal: () => (/* binding */ CreateProjectModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* __next_internal_client_entry_do_not_use__ CreateProjectModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CreateProjectModal(param) {\n    let { open, onOpenChange, stackName, projectTitle = '' } = param;\n    var _startingCommands_stackName;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [projectName, setProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(projectTitle);\n    const [projectPath, setProjectPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\ViceProjects\\\\\".concat(stackName.toLowerCase()));\n    const [projectDescription, setProjectDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCommands, setShowCommands] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLoading, setShowLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentCommand, setCurrentCommand] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [commandProgress, setCommandProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCommandRunning, setIsCommandRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Update path when stackName changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateProjectModal.useEffect\": ()=>{\n            setProjectPath(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\ViceProjects\\\\\".concat(stackName.toLowerCase()));\n        }\n    }[\"CreateProjectModal.useEffect\"], [\n        stackName\n    ]);\n    // Validate and format project name (no spaces, no caps, use hyphens)\n    const validateProjectName = (name)=>{\n        return name.trim().toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n    };\n    // Handle project name input with real-time validation\n    const handleProjectNameChange = (e)=>{\n        const value = e.target.value;\n        setProjectName(value);\n    };\n    // Listen for command progress events from Electron\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateProjectModal.useEffect\": ()=>{\n            if ( true && window.electronAPI) {\n                const handleCommandProgress = {\n                    \"CreateProjectModal.useEffect.handleCommandProgress\": (data)=>{\n                        console.log('[COMMAND PROGRESS] 📨 Received:', data);\n                        if (data.status === 'running') {\n                            setCurrentCommand(data.command);\n                            setIsCommandRunning(true);\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"▶️ Running: \".concat(data.command)\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                        } else if (data.status === 'completed') {\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"✅ Completed: \".concat(data.command)\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            setIsCommandRunning(false);\n                        } else if (data.status === 'error') {\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"❌ Error: \".concat(data.command, \" - \").concat(data.error || 'Unknown error')\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            setIsCommandRunning(false);\n                        } else if (data.status === 'starting-ide') {\n                            setCurrentCommand(\"Starting IDE for \".concat(data.projectName || 'project', \"...\"));\n                            setIsCommandRunning(true);\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"\\uD83D\\uDE80 Starting IDE and returning to dashboard...\"\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            // Close loading screen and return to dashboard\n                            setTimeout({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": ()=>{\n                                    handleLoadingComplete();\n                                }\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"], 3000);\n                        } else if (data.status === 'ide-opened') {\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"✅ IDE opened successfully! Returning to dashboard...\"\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            setTimeout({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": ()=>{\n                                    handleLoadingComplete();\n                                }\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"], 1000);\n                        }\n                    }\n                }[\"CreateProjectModal.useEffect.handleCommandProgress\"];\n                // Listen for the IPC event\n                if (window.electronAPI.onCommandProgress) {\n                    window.electronAPI.onCommandProgress(handleCommandProgress);\n                }\n                return ({\n                    \"CreateProjectModal.useEffect\": ()=>{\n                        console.log('[PROJECT] Cleaning up command progress listener');\n                    }\n                })[\"CreateProjectModal.useEffect\"];\n            }\n        }\n    }[\"CreateProjectModal.useEffect\"], []);\n    const handleLoadingComplete = ()=>{\n        setShowLoading(false);\n        setIsCreating(false);\n        setCurrentCommand('');\n        setCommandProgress([]);\n        setIsCommandRunning(false);\n        toast({\n            title: 'Project Ready',\n            description: \"\".concat(projectName, \" has been created and opened in the IDE.\")\n        });\n    };\n    // Starting commands for different stacks\n    const startingCommands = {\n        'Mern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express mongoose cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/models/User.js server/routes/api.js'\n        ],\n        'Pern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express pg cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/db.js server/routes/api.js'\n        ],\n        'NextJS': [\n            'npx create-next-app@latest my-app',\n            'cd my-app',\n            'npm install'\n        ],\n        'Django': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install django djangorestframework',\n            'django-admin startproject myproject .',\n            'python manage.py startapp myapp'\n        ],\n        'Flask': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install flask flask-sqlalchemy flask-cors',\n            'mkdir app',\n            'touch app/__init__.py app/routes.py app/models.py'\n        ],\n        'Bash': [\n            'mkdir my-script',\n            'cd my-script',\n            'touch script.sh',\n            'chmod +x script.sh',\n            'echo \"#!/bin/bash\" > script.sh'\n        ]\n    };\n    const handleCreate = async ()=>{\n        if (!projectName.trim()) {\n            toast({\n                title: 'Project name required',\n                description: 'Please enter a name for your project.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        // Validate and format project name\n        const formattedProjectName = validateProjectName(projectName);\n        if (!formattedProjectName) {\n            toast({\n                title: 'Invalid project name',\n                description: 'Project name must contain at least one letter or number.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        if (formattedProjectName !== projectName.trim().toLowerCase().replace(/\\s+/g, '-')) {\n            toast({\n                title: 'Project name formatted',\n                description: \"Project name changed to: \".concat(formattedProjectName)\n            });\n            setProjectName(formattedProjectName);\n        }\n        setIsCreating(true);\n        setShowLoading(true);\n        try {\n            // Create project using Electron API\n            if (window.electronAPI && window.electronAPI.createProjectFiles) {\n                const result = await window.electronAPI.createProjectFiles({\n                    projectName: formattedProjectName,\n                    projectPath,\n                    description: projectDescription,\n                    stackName\n                });\n                if (result && result.success) {\n                    const finalProjectPath = result.actualPath || projectPath;\n                    // Load project in IDE\n                    if (window.electronAPI && window.electronAPI.loadProjectInIde) {\n                        console.log('[PROJECT] Loading project in IDE via Electron API');\n                        window.electronAPI.loadProjectInIde({\n                            projectName: formattedProjectName,\n                            projectPath: finalProjectPath,\n                            files: [],\n                            stackName\n                        }).catch((error)=>{\n                            console.error('[PROJECT] Error loading project in IDE:', error);\n                            // Fallback: direct navigation to IDE\n                            const ideUrl = \"http://localhost:9003?project=\".concat(encodeURIComponent(formattedProjectName), \"&path=\").concat(encodeURIComponent(finalProjectPath));\n                            window.open(ideUrl, '_blank');\n                        });\n                    } else {\n                        // Fallback: open IDE in new window\n                        const ideUrl = \"http://localhost:9003?project=\".concat(encodeURIComponent(formattedProjectName), \"&path=\").concat(encodeURIComponent(finalProjectPath));\n                        window.open(ideUrl, '_blank');\n                    }\n                    toast({\n                        title: 'Project Created Successfully',\n                        description: \"\".concat(formattedProjectName, \" has been created and is opening in the IDE.\")\n                    });\n                    // Reset form\n                    setProjectName('');\n                    setProjectDescription('');\n                } else {\n                    throw new Error((result === null || result === void 0 ? void 0 : result.message) || 'Failed to create project');\n                }\n            } else {\n                throw new Error('Electron API not available');\n            }\n        } catch (error) {\n            console.error('[PROJECT] Error creating project:', error);\n            setIsCreating(false);\n            setShowLoading(false);\n            toast({\n                title: 'Failed to create project',\n                description: error instanceof Error ? error.message : 'An error occurred while creating the project.',\n                variant: 'destructive'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"sm:max-w-[550px] md:max-w-[600px] rounded-xl overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-20 w-40\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/logo.png\",\n                            alt: \"VICE Logo\",\n                            fill: true,\n                            style: {\n                                objectFit: 'contain'\n                            },\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-[-100%]' : 'translate-x-0'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                    className: \"mb-[5] pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                            className: \"text-center\",\n                                            children: \"Create New Project\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                            className: \"text-center text-sm text-muted-foreground\",\n                                            children: [\n                                                \"Create a new \",\n                                                stackName,\n                                                \" project. Fill in the details below to get started.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4 py-4 px-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"name\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"name\",\n                                                    value: projectName,\n                                                    onChange: (e)=>setProjectName(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"My Awesome Project\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"path\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Path\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"path\",\n                                                    value: projectPath,\n                                                    onChange: (e)=>setProjectPath(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"Documents/ViceProjects/\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"description\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                    id: \"description\",\n                                                    value: projectDescription,\n                                                    onChange: (e)=>setProjectDescription(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"A brief description of your project\",\n                                                    rows: 3\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-2 mb-4 ml-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-xs text-muted-foreground flex items-center gap-1 p-0 h-auto\",\n                                                onClick: ()=>setShowCommands(true),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3.5 w-3.5 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"See Starting Commands\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-3.5 w-3.5 ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                            className: \"flex justify-end items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>onOpenChange(false),\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        onClick: handleCreate,\n                                                        disabled: isCreating,\n                                                        children: isCreating ? 'Creating...' : 'Create Project'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-0' : 'translate-x-[100%]'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 ml-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"pl-0 flex items-center gap-1\",\n                                        onClick: ()=>setShowCommands(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Project Form\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                            children: [\n                                                \"Starting Commands for \",\n                                                stackName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                            children: [\n                                                \"Use these commands to set up your \",\n                                                stackName,\n                                                \" project manually.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-4 px-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-muted p-4 rounded-md font-mono text-xs sm:text-sm overflow-auto max-h-[250px]\",\n                                        children: ((_startingCommands_stackName = startingCommands[stackName]) === null || _startingCommands_stackName === void 0 ? void 0 : _startingCommands_stackName.map((command, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground mr-2 flex-shrink-0\",\n                                                        children: \"$\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"break-all\",\n                                                        children: command\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                \"No commands available for \",\n                                                stackName,\n                                                \".\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>onOpenChange(false),\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n            lineNumber: 260,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateProjectModal, \"+Wy90OJPGbFq7Wx8RMMP1TbpegU=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = CreateProjectModal;\nvar _c;\n$RefreshReg$(_c, \"CreateProjectModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/create-project-modal.tsx\n"));

/***/ })

});