"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/genkit";
exports.ids = ["vendor-chunks/genkit"];
exports.modules = {

/***/ "(action-browser)/./node_modules/genkit/lib/common.js":
/*!*******************************************!*\
  !*** ./node_modules/genkit/lib/common.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar common_exports = {};\n__export(common_exports, {\n  BaseDataPointSchema: () => import_ai.BaseDataPointSchema,\n  Chat: () => import_chat.Chat,\n  Document: () => import_ai.Document,\n  DocumentDataSchema: () => import_ai.DocumentDataSchema,\n  GENKIT_CLIENT_HEADER: () => import_core.GENKIT_CLIENT_HEADER,\n  GENKIT_VERSION: () => import_core.GENKIT_VERSION,\n  GenerationBlockedError: () => import_ai.GenerationBlockedError,\n  GenerationCommonConfigSchema: () => import_ai.GenerationCommonConfigSchema,\n  GenerationResponseError: () => import_ai.GenerationResponseError,\n  GenkitError: () => import_core.GenkitError,\n  LlmResponseSchema: () => import_ai.LlmResponseSchema,\n  LlmStatsSchema: () => import_ai.LlmStatsSchema,\n  Message: () => import_ai.Message,\n  MessageSchema: () => import_ai.MessageSchema,\n  ModelRequestSchema: () => import_ai.ModelRequestSchema,\n  ModelResponseSchema: () => import_ai.ModelResponseSchema,\n  PartSchema: () => import_ai.PartSchema,\n  ReflectionServer: () => import_core.ReflectionServer,\n  RoleSchema: () => import_ai.RoleSchema,\n  Session: () => import_session.Session,\n  StatusCodes: () => import_core.StatusCodes,\n  StatusSchema: () => import_core.StatusSchema,\n  ToolCallSchema: () => import_ai.ToolCallSchema,\n  ToolSchema: () => import_ai.ToolSchema,\n  UserFacingError: () => import_core.UserFacingError,\n  defineJsonSchema: () => import_core.defineJsonSchema,\n  defineSchema: () => import_core.defineSchema,\n  embedderRef: () => import_ai.embedderRef,\n  evaluatorRef: () => import_ai.evaluatorRef,\n  getCurrentEnv: () => import_core.getCurrentEnv,\n  getStreamingCallback: () => import_core.getStreamingCallback,\n  indexerRef: () => import_ai.indexerRef,\n  isDevEnv: () => import_core.isDevEnv,\n  rerankerRef: () => import_ai.rerankerRef,\n  retrieverRef: () => import_ai.retrieverRef,\n  runWithStreamingCallback: () => import_core.runWithStreamingCallback,\n  z: () => import_core.z\n});\nmodule.exports = __toCommonJS(common_exports);\nvar import_ai = __webpack_require__(/*! @genkit-ai/ai */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/index.js\");\nvar import_chat = __webpack_require__(/*! @genkit-ai/ai/chat */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/chat.js\");\nvar import_session = __webpack_require__(/*! @genkit-ai/ai/session */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/session.js\");\nvar import_core = __webpack_require__(/*! @genkit-ai/core */ \"(action-browser)/./node_modules/@genkit-ai/core/lib/index.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9nZW5raXQvbGliL2NvbW1vbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixrQ0FBa0M7QUFDaEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2Qiw0RkFBNEY7QUFDekg7QUFDQTtBQUNBO0FBQ0Esb0RBQW9ELGtCQUFrQixhQUFhO0FBQ25GO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBLGdCQUFnQixtQkFBTyxDQUFDLGlGQUFlO0FBQ3ZDLGtCQUFrQixtQkFBTyxDQUFDLHFGQUFvQjtBQUM5QyxxQkFBcUIsbUJBQU8sQ0FBQywyRkFBdUI7QUFDcEQsa0JBQWtCLG1CQUFPLENBQUMscUZBQWlCO0FBQzNDO0FBQ0EsTUFBTSxDQXNDTDtBQUNEIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXE5ldyBmb2xkZXJcXFZpY2VcXFZpY2VcXG5vZGVfbW9kdWxlc1xcZ2Vua2l0XFxsaWJcXGNvbW1vbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2RlZlByb3AgPSBPYmplY3QuZGVmaW5lUHJvcGVydHk7XG52YXIgX19nZXRPd25Qcm9wRGVzYyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3I7XG52YXIgX19nZXRPd25Qcm9wTmFtZXMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlOYW1lcztcbnZhciBfX2hhc093blByb3AgPSBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5O1xudmFyIF9fZXhwb3J0ID0gKHRhcmdldCwgYWxsKSA9PiB7XG4gIGZvciAodmFyIG5hbWUgaW4gYWxsKVxuICAgIF9fZGVmUHJvcCh0YXJnZXQsIG5hbWUsIHsgZ2V0OiBhbGxbbmFtZV0sIGVudW1lcmFibGU6IHRydWUgfSk7XG59O1xudmFyIF9fY29weVByb3BzID0gKHRvLCBmcm9tLCBleGNlcHQsIGRlc2MpID0+IHtcbiAgaWYgKGZyb20gJiYgdHlwZW9mIGZyb20gPT09IFwib2JqZWN0XCIgfHwgdHlwZW9mIGZyb20gPT09IFwiZnVuY3Rpb25cIikge1xuICAgIGZvciAobGV0IGtleSBvZiBfX2dldE93blByb3BOYW1lcyhmcm9tKSlcbiAgICAgIGlmICghX19oYXNPd25Qcm9wLmNhbGwodG8sIGtleSkgJiYga2V5ICE9PSBleGNlcHQpXG4gICAgICAgIF9fZGVmUHJvcCh0bywga2V5LCB7IGdldDogKCkgPT4gZnJvbVtrZXldLCBlbnVtZXJhYmxlOiAhKGRlc2MgPSBfX2dldE93blByb3BEZXNjKGZyb20sIGtleSkpIHx8IGRlc2MuZW51bWVyYWJsZSB9KTtcbiAgfVxuICByZXR1cm4gdG87XG59O1xudmFyIF9fdG9Db21tb25KUyA9IChtb2QpID0+IF9fY29weVByb3BzKF9fZGVmUHJvcCh7fSwgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSksIG1vZCk7XG52YXIgY29tbW9uX2V4cG9ydHMgPSB7fTtcbl9fZXhwb3J0KGNvbW1vbl9leHBvcnRzLCB7XG4gIEJhc2VEYXRhUG9pbnRTY2hlbWE6ICgpID0+IGltcG9ydF9haS5CYXNlRGF0YVBvaW50U2NoZW1hLFxuICBDaGF0OiAoKSA9PiBpbXBvcnRfY2hhdC5DaGF0LFxuICBEb2N1bWVudDogKCkgPT4gaW1wb3J0X2FpLkRvY3VtZW50LFxuICBEb2N1bWVudERhdGFTY2hlbWE6ICgpID0+IGltcG9ydF9haS5Eb2N1bWVudERhdGFTY2hlbWEsXG4gIEdFTktJVF9DTElFTlRfSEVBREVSOiAoKSA9PiBpbXBvcnRfY29yZS5HRU5LSVRfQ0xJRU5UX0hFQURFUixcbiAgR0VOS0lUX1ZFUlNJT046ICgpID0+IGltcG9ydF9jb3JlLkdFTktJVF9WRVJTSU9OLFxuICBHZW5lcmF0aW9uQmxvY2tlZEVycm9yOiAoKSA9PiBpbXBvcnRfYWkuR2VuZXJhdGlvbkJsb2NrZWRFcnJvcixcbiAgR2VuZXJhdGlvbkNvbW1vbkNvbmZpZ1NjaGVtYTogKCkgPT4gaW1wb3J0X2FpLkdlbmVyYXRpb25Db21tb25Db25maWdTY2hlbWEsXG4gIEdlbmVyYXRpb25SZXNwb25zZUVycm9yOiAoKSA9PiBpbXBvcnRfYWkuR2VuZXJhdGlvblJlc3BvbnNlRXJyb3IsXG4gIEdlbmtpdEVycm9yOiAoKSA9PiBpbXBvcnRfY29yZS5HZW5raXRFcnJvcixcbiAgTGxtUmVzcG9uc2VTY2hlbWE6ICgpID0+IGltcG9ydF9haS5MbG1SZXNwb25zZVNjaGVtYSxcbiAgTGxtU3RhdHNTY2hlbWE6ICgpID0+IGltcG9ydF9haS5MbG1TdGF0c1NjaGVtYSxcbiAgTWVzc2FnZTogKCkgPT4gaW1wb3J0X2FpLk1lc3NhZ2UsXG4gIE1lc3NhZ2VTY2hlbWE6ICgpID0+IGltcG9ydF9haS5NZXNzYWdlU2NoZW1hLFxuICBNb2RlbFJlcXVlc3RTY2hlbWE6ICgpID0+IGltcG9ydF9haS5Nb2RlbFJlcXVlc3RTY2hlbWEsXG4gIE1vZGVsUmVzcG9uc2VTY2hlbWE6ICgpID0+IGltcG9ydF9haS5Nb2RlbFJlc3BvbnNlU2NoZW1hLFxuICBQYXJ0U2NoZW1hOiAoKSA9PiBpbXBvcnRfYWkuUGFydFNjaGVtYSxcbiAgUmVmbGVjdGlvblNlcnZlcjogKCkgPT4gaW1wb3J0X2NvcmUuUmVmbGVjdGlvblNlcnZlcixcbiAgUm9sZVNjaGVtYTogKCkgPT4gaW1wb3J0X2FpLlJvbGVTY2hlbWEsXG4gIFNlc3Npb246ICgpID0+IGltcG9ydF9zZXNzaW9uLlNlc3Npb24sXG4gIFN0YXR1c0NvZGVzOiAoKSA9PiBpbXBvcnRfY29yZS5TdGF0dXNDb2RlcyxcbiAgU3RhdHVzU2NoZW1hOiAoKSA9PiBpbXBvcnRfY29yZS5TdGF0dXNTY2hlbWEsXG4gIFRvb2xDYWxsU2NoZW1hOiAoKSA9PiBpbXBvcnRfYWkuVG9vbENhbGxTY2hlbWEsXG4gIFRvb2xTY2hlbWE6ICgpID0+IGltcG9ydF9haS5Ub29sU2NoZW1hLFxuICBVc2VyRmFjaW5nRXJyb3I6ICgpID0+IGltcG9ydF9jb3JlLlVzZXJGYWNpbmdFcnJvcixcbiAgZGVmaW5lSnNvblNjaGVtYTogKCkgPT4gaW1wb3J0X2NvcmUuZGVmaW5lSnNvblNjaGVtYSxcbiAgZGVmaW5lU2NoZW1hOiAoKSA9PiBpbXBvcnRfY29yZS5kZWZpbmVTY2hlbWEsXG4gIGVtYmVkZGVyUmVmOiAoKSA9PiBpbXBvcnRfYWkuZW1iZWRkZXJSZWYsXG4gIGV2YWx1YXRvclJlZjogKCkgPT4gaW1wb3J0X2FpLmV2YWx1YXRvclJlZixcbiAgZ2V0Q3VycmVudEVudjogKCkgPT4gaW1wb3J0X2NvcmUuZ2V0Q3VycmVudEVudixcbiAgZ2V0U3RyZWFtaW5nQ2FsbGJhY2s6ICgpID0+IGltcG9ydF9jb3JlLmdldFN0cmVhbWluZ0NhbGxiYWNrLFxuICBpbmRleGVyUmVmOiAoKSA9PiBpbXBvcnRfYWkuaW5kZXhlclJlZixcbiAgaXNEZXZFbnY6ICgpID0+IGltcG9ydF9jb3JlLmlzRGV2RW52LFxuICByZXJhbmtlclJlZjogKCkgPT4gaW1wb3J0X2FpLnJlcmFua2VyUmVmLFxuICByZXRyaWV2ZXJSZWY6ICgpID0+IGltcG9ydF9haS5yZXRyaWV2ZXJSZWYsXG4gIHJ1bldpdGhTdHJlYW1pbmdDYWxsYmFjazogKCkgPT4gaW1wb3J0X2NvcmUucnVuV2l0aFN0cmVhbWluZ0NhbGxiYWNrLFxuICB6OiAoKSA9PiBpbXBvcnRfY29yZS56XG59KTtcbm1vZHVsZS5leHBvcnRzID0gX190b0NvbW1vbkpTKGNvbW1vbl9leHBvcnRzKTtcbnZhciBpbXBvcnRfYWkgPSByZXF1aXJlKFwiQGdlbmtpdC1haS9haVwiKTtcbnZhciBpbXBvcnRfY2hhdCA9IHJlcXVpcmUoXCJAZ2Vua2l0LWFpL2FpL2NoYXRcIik7XG52YXIgaW1wb3J0X3Nlc3Npb24gPSByZXF1aXJlKFwiQGdlbmtpdC1haS9haS9zZXNzaW9uXCIpO1xudmFyIGltcG9ydF9jb3JlID0gcmVxdWlyZShcIkBnZW5raXQtYWkvY29yZVwiKTtcbi8vIEFubm90YXRlIHRoZSBDb21tb25KUyBleHBvcnQgbmFtZXMgZm9yIEVTTSBpbXBvcnQgaW4gbm9kZTpcbjAgJiYgKG1vZHVsZS5leHBvcnRzID0ge1xuICBCYXNlRGF0YVBvaW50U2NoZW1hLFxuICBDaGF0LFxuICBEb2N1bWVudCxcbiAgRG9jdW1lbnREYXRhU2NoZW1hLFxuICBHRU5LSVRfQ0xJRU5UX0hFQURFUixcbiAgR0VOS0lUX1ZFUlNJT04sXG4gIEdlbmVyYXRpb25CbG9ja2VkRXJyb3IsXG4gIEdlbmVyYXRpb25Db21tb25Db25maWdTY2hlbWEsXG4gIEdlbmVyYXRpb25SZXNwb25zZUVycm9yLFxuICBHZW5raXRFcnJvcixcbiAgTGxtUmVzcG9uc2VTY2hlbWEsXG4gIExsbVN0YXRzU2NoZW1hLFxuICBNZXNzYWdlLFxuICBNZXNzYWdlU2NoZW1hLFxuICBNb2RlbFJlcXVlc3RTY2hlbWEsXG4gIE1vZGVsUmVzcG9uc2VTY2hlbWEsXG4gIFBhcnRTY2hlbWEsXG4gIFJlZmxlY3Rpb25TZXJ2ZXIsXG4gIFJvbGVTY2hlbWEsXG4gIFNlc3Npb24sXG4gIFN0YXR1c0NvZGVzLFxuICBTdGF0dXNTY2hlbWEsXG4gIFRvb2xDYWxsU2NoZW1hLFxuICBUb29sU2NoZW1hLFxuICBVc2VyRmFjaW5nRXJyb3IsXG4gIGRlZmluZUpzb25TY2hlbWEsXG4gIGRlZmluZVNjaGVtYSxcbiAgZW1iZWRkZXJSZWYsXG4gIGV2YWx1YXRvclJlZixcbiAgZ2V0Q3VycmVudEVudixcbiAgZ2V0U3RyZWFtaW5nQ2FsbGJhY2ssXG4gIGluZGV4ZXJSZWYsXG4gIGlzRGV2RW52LFxuICByZXJhbmtlclJlZixcbiAgcmV0cmlldmVyUmVmLFxuICBydW5XaXRoU3RyZWFtaW5nQ2FsbGJhY2ssXG4gIHpcbn0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29tbW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/common.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/embedder.js":
/*!*********************************************!*\
  !*** ./node_modules/genkit/lib/embedder.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar embedder_exports = {};\n__export(embedder_exports, {\n  EmbedderInfoSchema: () => import_embedder.EmbedderInfoSchema,\n  embedderRef: () => import_embedder.embedderRef\n});\nmodule.exports = __toCommonJS(embedder_exports);\nvar import_embedder = __webpack_require__(/*! @genkit-ai/ai/embedder */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/embedder.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=embedder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/embedder.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/genkit.js":
/*!*******************************************!*\
  !*** ./node_modules/genkit/lib/genkit.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar genkit_exports = {};\n__export(genkit_exports, {\n  Genkit: () => Genkit,\n  __disableReflectionApi: () => __disableReflectionApi,\n  genkit: () => genkit\n});\nmodule.exports = __toCommonJS(genkit_exports);\nvar import_ai = __webpack_require__(/*! @genkit-ai/ai */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/index.js\");\nvar import_embedder = __webpack_require__(/*! @genkit-ai/ai/embedder */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/embedder.js\");\nvar import_evaluator = __webpack_require__(/*! @genkit-ai/ai/evaluator */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/evaluator.js\");\nvar import_formats = __webpack_require__(/*! @genkit-ai/ai/formats */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/formats/index.js\");\nvar import_model = __webpack_require__(/*! @genkit-ai/ai/model */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/model.js\");\nvar import_reranker = __webpack_require__(/*! @genkit-ai/ai/reranker */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/reranker.js\");\nvar import_retriever = __webpack_require__(/*! @genkit-ai/ai/retriever */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/retriever.js\");\nvar import_core = __webpack_require__(/*! @genkit-ai/core */ \"(action-browser)/./node_modules/@genkit-ai/core/lib/index.js\");\nvar import_logging = __webpack_require__(/*! ./logging.js */ \"(action-browser)/./node_modules/genkit/lib/logging.js\");\nvar import_registry2 = __webpack_require__(/*! ./registry.js */ \"(action-browser)/./node_modules/genkit/lib/registry.js\");\nclass Genkit {\n  /** Developer-configured options. */\n  options;\n  /** Registry instance that is exclusively modified by this Genkit instance. */\n  registry;\n  /** Reflection server for this registry. May be null if not started. */\n  reflectionServer = null;\n  /** List of flows that have been registered in this instance. */\n  flows = [];\n  get apiStability() {\n    return this.registry.apiStability;\n  }\n  constructor(options) {\n    this.options = options || {};\n    this.registry = new import_registry2.Registry();\n    this.configure();\n    if ((0, import_core.isDevEnv)() && !disableReflectionApi) {\n      this.reflectionServer = new import_core.ReflectionServer(this.registry, {\n        configuredEnvs: [\"dev\"]\n      });\n      this.reflectionServer.start().catch((e) => import_logging.logger.error);\n    }\n  }\n  /**\n   * Defines and registers a flow function.\n   */\n  defineFlow(config, fn) {\n    const flow = (0, import_core.defineFlow)(this.registry, config, fn);\n    this.flows.push(flow);\n    return flow;\n  }\n  /**\n   * Defines and registers a tool.\n   *\n   * Tools can be passed to models by name or value during `generate` calls to be called automatically based on the prompt and situation.\n   */\n  defineTool(config, fn) {\n    return (0, import_ai.defineTool)(this.registry, config, fn);\n  }\n  /**\n   * Defines and registers a schema from a Zod schema.\n   *\n   * Defined schemas can be referenced by `name` in prompts in place of inline schemas.\n   */\n  defineSchema(name, schema) {\n    return (0, import_core.defineSchema)(this.registry, name, schema);\n  }\n  /**\n   * Defines and registers a schema from a JSON schema.\n   *\n   * Defined schemas can be referenced by `name` in prompts in place of inline schemas.\n   */\n  defineJsonSchema(name, jsonSchema) {\n    return (0, import_core.defineJsonSchema)(this.registry, name, jsonSchema);\n  }\n  /**\n   * Defines a new model and adds it to the registry.\n   */\n  defineModel(options, runner) {\n    return (0, import_model.defineModel)(this.registry, options, runner);\n  }\n  /**\n   * Looks up a prompt by `name` (and optionally `variant`). Can be used to lookup\n   * .prompt files or prompts previously defined with {@link Genkit.definePrompt}\n   */\n  prompt(name, options) {\n    return this.wrapExecutablePromptPromise(\n      (0, import_ai.prompt)(this.registry, name, {\n        ...options,\n        dir: this.options.promptDir ?? \"./prompts\"\n      })\n    );\n  }\n  wrapExecutablePromptPromise(promise) {\n    const executablePrompt = async (input, opts) => {\n      return (await promise)(input, opts);\n    };\n    executablePrompt.render = async (opt) => {\n      return (await promise).render(opt.input, opt);\n    };\n    executablePrompt.stream = (input, opts) => {\n      return this.generateStream(\n        promise.then(\n          (action) => action.render(input, {\n            ...opts\n          })\n        )\n      );\n    };\n    executablePrompt.asTool = async () => {\n      return (await promise).asTool();\n    };\n    return executablePrompt;\n  }\n  /**\n   * Defines and registers a prompt based on a function.\n   *\n   * This is an alternative to defining and importing a .prompt file, providing\n   * the most advanced control over how the final request to the model is made.\n   *\n   * @param options - Prompt metadata including model, model params,\n   * input/output schemas, etc\n   * @param fn - A function that returns a {@link GenerateRequest}. Any config\n   * parameters specified by the {@link GenerateRequest} will take precedence\n   * over any parameters specified by `options`.\n   *\n   * ```ts\n   * const hi = ai.definePrompt(\n   *   {\n   *     name: 'hi',\n   *     input: {\n   *       schema: z.object({\n   *         name: z.string(),\n   *       }),\n   *     },\n   *     config: {\n   *       temperature: 1,\n   *     },\n   *   },\n   *   async (input) => {\n   *     return {\n   *       messages: [ { role: 'user', content: [{ text: `hi ${input.name}` }] } ],\n   *     };\n   *   }\n   * );\n   * const { text } = await hi({ name: 'Genkit' });\n   * ```\n   */\n  definePrompt(options, templateOrFn) {\n    if (templateOrFn) {\n      if (options.messages) {\n        throw new import_core.GenkitError({\n          status: \"INVALID_ARGUMENT\",\n          message: \"Cannot specify template/function argument and `options.messages` at the same time\"\n        });\n      }\n      if (typeof templateOrFn === \"string\") {\n        return (0, import_ai.definePrompt)(this.registry, {\n          ...options,\n          messages: templateOrFn\n        });\n      } else {\n        return (0, import_ai.definePrompt)(this.registry, {\n          ...options,\n          messages: async (input) => {\n            const response = await templateOrFn(input);\n            return response.messages;\n          }\n        });\n      }\n    }\n    return (0, import_ai.definePrompt)(this.registry, options);\n  }\n  /**\n   * Creates a retriever action for the provided {@link RetrieverFn} implementation.\n   */\n  defineRetriever(options, runner) {\n    return (0, import_retriever.defineRetriever)(this.registry, options, runner);\n  }\n  /**\n   * defineSimpleRetriever makes it easy to map existing data into documents that\n   * can be used for prompt augmentation.\n   *\n   * @param options Configuration options for the retriever.\n   * @param handler A function that queries a datastore and returns items from which to extract documents.\n   * @returns A Genkit retriever.\n   */\n  defineSimpleRetriever(options, handler) {\n    return (0, import_retriever.defineSimpleRetriever)(this.registry, options, handler);\n  }\n  /**\n   * Creates an indexer action for the provided {@link IndexerFn} implementation.\n   */\n  defineIndexer(options, runner) {\n    return (0, import_retriever.defineIndexer)(this.registry, options, runner);\n  }\n  /**\n   * Creates evaluator action for the provided {@link EvaluatorFn} implementation.\n   */\n  defineEvaluator(options, runner) {\n    return (0, import_evaluator.defineEvaluator)(this.registry, options, runner);\n  }\n  /**\n   * Creates embedder model for the provided {@link EmbedderFn} model implementation.\n   */\n  defineEmbedder(options, runner) {\n    return (0, import_embedder.defineEmbedder)(this.registry, options, runner);\n  }\n  /**\n   * create a handlebards helper (https://handlebarsjs.com/guide/block-helpers.html) to be used in dotpormpt templates.\n   */\n  defineHelper(name, fn) {\n    (0, import_ai.defineHelper)(this.registry, name, fn);\n  }\n  /**\n   * Creates a handlebars partial (https://handlebarsjs.com/guide/partials.html) to be used in dotpormpt templates.\n   */\n  definePartial(name, source) {\n    (0, import_ai.definePartial)(this.registry, name, source);\n  }\n  /**\n   *  Creates a reranker action for the provided {@link RerankerFn} implementation.\n   */\n  defineReranker(options, runner) {\n    return (0, import_reranker.defineReranker)(this.registry, options, runner);\n  }\n  /**\n   * Embeds the given `content` using the specified `embedder`.\n   */\n  embed(params) {\n    return (0, import_ai.embed)(this.registry, params);\n  }\n  /**\n   * A veneer for interacting with embedder models in bulk.\n   */\n  embedMany(params) {\n    return (0, import_embedder.embedMany)(this.registry, params);\n  }\n  /**\n   * Evaluates the given `dataset` using the specified `evaluator`.\n   */\n  evaluate(params) {\n    return (0, import_ai.evaluate)(this.registry, params);\n  }\n  /**\n   * Reranks documents from a {@link RerankerArgument} based on the provided query.\n   */\n  rerank(params) {\n    return (0, import_ai.rerank)(this.registry, params);\n  }\n  /**\n   * Indexes `documents` using the provided `indexer`.\n   */\n  index(params) {\n    return (0, import_retriever.index)(this.registry, params);\n  }\n  /**\n   * Retrieves documents from the `retriever` based on the provided `query`.\n   */\n  retrieve(params) {\n    return (0, import_ai.retrieve)(this.registry, params);\n  }\n  async generate(options) {\n    let resolvedOptions;\n    if (options instanceof Promise) {\n      resolvedOptions = await options;\n    } else if (typeof options === \"string\" || Array.isArray(options)) {\n      resolvedOptions = {\n        prompt: options\n      };\n    } else {\n      resolvedOptions = options;\n    }\n    return (0, import_ai.generate)(this.registry, resolvedOptions);\n  }\n  generateStream(options) {\n    if (typeof options === \"string\" || Array.isArray(options)) {\n      options = { prompt: options };\n    }\n    return (0, import_ai.generateStream)(this.registry, options);\n  }\n  run(name, funcOrInput, maybeFunc) {\n    if (maybeFunc) {\n      return (0, import_core.run)(name, funcOrInput, maybeFunc, this.registry);\n    }\n    return (0, import_core.run)(name, funcOrInput, this.registry);\n  }\n  /**\n   * Returns current action (or flow) invocation context. Can be used to access things like auth\n   * data set by HTTP server frameworks. If invoked outside of an action (e.g. flow or tool) will\n   * return `undefined`.\n   */\n  currentContext() {\n    return (0, import_core.getContext)(this);\n  }\n  /**\n   * Configures the Genkit instance.\n   */\n  configure() {\n    const activeRegistry = this.registry;\n    (0, import_model.defineGenerateAction)(activeRegistry);\n    (0, import_formats.configureFormats)(activeRegistry);\n    const plugins = [...this.options.plugins ?? []];\n    if (this.options.model) {\n      this.registry.registerValue(\n        \"defaultModel\",\n        \"defaultModel\",\n        this.options.model\n      );\n    }\n    if (this.options.promptDir !== null) {\n      (0, import_ai.loadPromptFolder)(\n        this.registry,\n        this.options.promptDir ?? \"./prompts\",\n        \"\"\n      );\n    }\n    plugins.forEach((plugin) => {\n      const loadedPlugin = plugin(this);\n      import_logging.logger.debug(`Registering plugin ${loadedPlugin.name}...`);\n      activeRegistry.registerPluginProvider(loadedPlugin.name, {\n        name: loadedPlugin.name,\n        async initializer() {\n          import_logging.logger.debug(`Initializing plugin ${loadedPlugin.name}:`);\n          await loadedPlugin.initializer();\n        }\n      });\n    });\n  }\n  /**\n   * Stops all servers.\n   */\n  async stopServers() {\n    await this.reflectionServer?.stop();\n    this.reflectionServer = null;\n  }\n}\nfunction genkit(options) {\n  return new Genkit(options);\n}\nconst shutdown = async () => {\n  import_logging.logger.info(\"Shutting down all Genkit servers...\");\n  await import_core.ReflectionServer.stopAll();\n  process.exit(0);\n};\nprocess.on(\"SIGTERM\", shutdown);\nprocess.on(\"SIGINT\", shutdown);\nlet disableReflectionApi = false;\nfunction __disableReflectionApi() {\n  disableReflectionApi = true;\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=genkit.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9nZW5raXQvbGliL2dlbmtpdC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixrQ0FBa0M7QUFDaEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2Qiw0RkFBNEY7QUFDekg7QUFDQTtBQUNBO0FBQ0Esb0RBQW9ELGtCQUFrQixhQUFhO0FBQ25GO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxnQkFBZ0IsbUJBQU8sQ0FBQyxpRkFBZTtBQUN2QyxzQkFBc0IsbUJBQU8sQ0FBQyw2RkFBd0I7QUFDdEQsdUJBQXVCLG1CQUFPLENBQUMsK0ZBQXlCO0FBQ3hELHFCQUFxQixtQkFBTyxDQUFDLGlHQUF1QjtBQUNwRCxtQkFBbUIsbUJBQU8sQ0FBQyx1RkFBcUI7QUFDaEQsc0JBQXNCLG1CQUFPLENBQUMsNkZBQXdCO0FBQ3RELHVCQUF1QixtQkFBTyxDQUFDLCtGQUF5QjtBQUN4RCxrQkFBa0IsbUJBQU8sQ0FBQyxxRkFBaUI7QUFDM0MscUJBQXFCLG1CQUFPLENBQUMsMkVBQWM7QUFDM0MsdUJBQXVCLG1CQUFPLENBQUMsNkVBQWU7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQ7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsc0JBQXNCO0FBQ2xFLGtDQUFrQyx1QkFBdUI7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLFVBQVU7QUFDVjtBQUNBO0FBQ0EsVUFBVTtBQUNWLFFBQVE7QUFDUjtBQUNBO0FBQ0EseUJBQXlCLDBCQUEwQixZQUFZLFdBQVcsR0FBRyxJQUFJO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBLGFBQWEsT0FBTyxhQUFhLGdCQUFnQjtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0RBQWtELG1CQUFtQjtBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCxpQkFBaUI7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdEQUFnRCxtQkFBbUI7QUFDbkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE4QyxrQkFBa0I7QUFDaEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCxrQkFBa0I7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQix3QkFBd0I7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3REFBd0Qsa0JBQWtCO0FBQzFFO0FBQ0E7QUFDQTtBQUNBLDZEQUE2RCxrQkFBa0I7QUFDL0U7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSxDQUlMO0FBQ0QiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcTmV3IGZvbGRlclxcVmljZVxcVmljZVxcbm9kZV9tb2R1bGVzXFxnZW5raXRcXGxpYlxcZ2Vua2l0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9fZGVmUHJvcCA9IE9iamVjdC5kZWZpbmVQcm9wZXJ0eTtcbnZhciBfX2dldE93blByb3BEZXNjID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcjtcbnZhciBfX2dldE93blByb3BOYW1lcyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eU5hbWVzO1xudmFyIF9faGFzT3duUHJvcCA9IE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHk7XG52YXIgX19leHBvcnQgPSAodGFyZ2V0LCBhbGwpID0+IHtcbiAgZm9yICh2YXIgbmFtZSBpbiBhbGwpXG4gICAgX19kZWZQcm9wKHRhcmdldCwgbmFtZSwgeyBnZXQ6IGFsbFtuYW1lXSwgZW51bWVyYWJsZTogdHJ1ZSB9KTtcbn07XG52YXIgX19jb3B5UHJvcHMgPSAodG8sIGZyb20sIGV4Y2VwdCwgZGVzYykgPT4ge1xuICBpZiAoZnJvbSAmJiB0eXBlb2YgZnJvbSA9PT0gXCJvYmplY3RcIiB8fCB0eXBlb2YgZnJvbSA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgZm9yIChsZXQga2V5IG9mIF9fZ2V0T3duUHJvcE5hbWVzKGZyb20pKVxuICAgICAgaWYgKCFfX2hhc093blByb3AuY2FsbCh0bywga2V5KSAmJiBrZXkgIT09IGV4Y2VwdClcbiAgICAgICAgX19kZWZQcm9wKHRvLCBrZXksIHsgZ2V0OiAoKSA9PiBmcm9tW2tleV0sIGVudW1lcmFibGU6ICEoZGVzYyA9IF9fZ2V0T3duUHJvcERlc2MoZnJvbSwga2V5KSkgfHwgZGVzYy5lbnVtZXJhYmxlIH0pO1xuICB9XG4gIHJldHVybiB0bztcbn07XG52YXIgX190b0NvbW1vbkpTID0gKG1vZCkgPT4gX19jb3B5UHJvcHMoX19kZWZQcm9wKHt9LCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KSwgbW9kKTtcbnZhciBnZW5raXRfZXhwb3J0cyA9IHt9O1xuX19leHBvcnQoZ2Vua2l0X2V4cG9ydHMsIHtcbiAgR2Vua2l0OiAoKSA9PiBHZW5raXQsXG4gIF9fZGlzYWJsZVJlZmxlY3Rpb25BcGk6ICgpID0+IF9fZGlzYWJsZVJlZmxlY3Rpb25BcGksXG4gIGdlbmtpdDogKCkgPT4gZ2Vua2l0XG59KTtcbm1vZHVsZS5leHBvcnRzID0gX190b0NvbW1vbkpTKGdlbmtpdF9leHBvcnRzKTtcbnZhciBpbXBvcnRfYWkgPSByZXF1aXJlKFwiQGdlbmtpdC1haS9haVwiKTtcbnZhciBpbXBvcnRfZW1iZWRkZXIgPSByZXF1aXJlKFwiQGdlbmtpdC1haS9haS9lbWJlZGRlclwiKTtcbnZhciBpbXBvcnRfZXZhbHVhdG9yID0gcmVxdWlyZShcIkBnZW5raXQtYWkvYWkvZXZhbHVhdG9yXCIpO1xudmFyIGltcG9ydF9mb3JtYXRzID0gcmVxdWlyZShcIkBnZW5raXQtYWkvYWkvZm9ybWF0c1wiKTtcbnZhciBpbXBvcnRfbW9kZWwgPSByZXF1aXJlKFwiQGdlbmtpdC1haS9haS9tb2RlbFwiKTtcbnZhciBpbXBvcnRfcmVyYW5rZXIgPSByZXF1aXJlKFwiQGdlbmtpdC1haS9haS9yZXJhbmtlclwiKTtcbnZhciBpbXBvcnRfcmV0cmlldmVyID0gcmVxdWlyZShcIkBnZW5raXQtYWkvYWkvcmV0cmlldmVyXCIpO1xudmFyIGltcG9ydF9jb3JlID0gcmVxdWlyZShcIkBnZW5raXQtYWkvY29yZVwiKTtcbnZhciBpbXBvcnRfbG9nZ2luZyA9IHJlcXVpcmUoXCIuL2xvZ2dpbmcuanNcIik7XG52YXIgaW1wb3J0X3JlZ2lzdHJ5MiA9IHJlcXVpcmUoXCIuL3JlZ2lzdHJ5LmpzXCIpO1xuY2xhc3MgR2Vua2l0IHtcbiAgLyoqIERldmVsb3Blci1jb25maWd1cmVkIG9wdGlvbnMuICovXG4gIG9wdGlvbnM7XG4gIC8qKiBSZWdpc3RyeSBpbnN0YW5jZSB0aGF0IGlzIGV4Y2x1c2l2ZWx5IG1vZGlmaWVkIGJ5IHRoaXMgR2Vua2l0IGluc3RhbmNlLiAqL1xuICByZWdpc3RyeTtcbiAgLyoqIFJlZmxlY3Rpb24gc2VydmVyIGZvciB0aGlzIHJlZ2lzdHJ5LiBNYXkgYmUgbnVsbCBpZiBub3Qgc3RhcnRlZC4gKi9cbiAgcmVmbGVjdGlvblNlcnZlciA9IG51bGw7XG4gIC8qKiBMaXN0IG9mIGZsb3dzIHRoYXQgaGF2ZSBiZWVuIHJlZ2lzdGVyZWQgaW4gdGhpcyBpbnN0YW5jZS4gKi9cbiAgZmxvd3MgPSBbXTtcbiAgZ2V0IGFwaVN0YWJpbGl0eSgpIHtcbiAgICByZXR1cm4gdGhpcy5yZWdpc3RyeS5hcGlTdGFiaWxpdHk7XG4gIH1cbiAgY29uc3RydWN0b3Iob3B0aW9ucykge1xuICAgIHRoaXMub3B0aW9ucyA9IG9wdGlvbnMgfHwge307XG4gICAgdGhpcy5yZWdpc3RyeSA9IG5ldyBpbXBvcnRfcmVnaXN0cnkyLlJlZ2lzdHJ5KCk7XG4gICAgdGhpcy5jb25maWd1cmUoKTtcbiAgICBpZiAoKDAsIGltcG9ydF9jb3JlLmlzRGV2RW52KSgpICYmICFkaXNhYmxlUmVmbGVjdGlvbkFwaSkge1xuICAgICAgdGhpcy5yZWZsZWN0aW9uU2VydmVyID0gbmV3IGltcG9ydF9jb3JlLlJlZmxlY3Rpb25TZXJ2ZXIodGhpcy5yZWdpc3RyeSwge1xuICAgICAgICBjb25maWd1cmVkRW52czogW1wiZGV2XCJdXG4gICAgICB9KTtcbiAgICAgIHRoaXMucmVmbGVjdGlvblNlcnZlci5zdGFydCgpLmNhdGNoKChlKSA9PiBpbXBvcnRfbG9nZ2luZy5sb2dnZXIuZXJyb3IpO1xuICAgIH1cbiAgfVxuICAvKipcbiAgICogRGVmaW5lcyBhbmQgcmVnaXN0ZXJzIGEgZmxvdyBmdW5jdGlvbi5cbiAgICovXG4gIGRlZmluZUZsb3coY29uZmlnLCBmbikge1xuICAgIGNvbnN0IGZsb3cgPSAoMCwgaW1wb3J0X2NvcmUuZGVmaW5lRmxvdykodGhpcy5yZWdpc3RyeSwgY29uZmlnLCBmbik7XG4gICAgdGhpcy5mbG93cy5wdXNoKGZsb3cpO1xuICAgIHJldHVybiBmbG93O1xuICB9XG4gIC8qKlxuICAgKiBEZWZpbmVzIGFuZCByZWdpc3RlcnMgYSB0b29sLlxuICAgKlxuICAgKiBUb29scyBjYW4gYmUgcGFzc2VkIHRvIG1vZGVscyBieSBuYW1lIG9yIHZhbHVlIGR1cmluZyBgZ2VuZXJhdGVgIGNhbGxzIHRvIGJlIGNhbGxlZCBhdXRvbWF0aWNhbGx5IGJhc2VkIG9uIHRoZSBwcm9tcHQgYW5kIHNpdHVhdGlvbi5cbiAgICovXG4gIGRlZmluZVRvb2woY29uZmlnLCBmbikge1xuICAgIHJldHVybiAoMCwgaW1wb3J0X2FpLmRlZmluZVRvb2wpKHRoaXMucmVnaXN0cnksIGNvbmZpZywgZm4pO1xuICB9XG4gIC8qKlxuICAgKiBEZWZpbmVzIGFuZCByZWdpc3RlcnMgYSBzY2hlbWEgZnJvbSBhIFpvZCBzY2hlbWEuXG4gICAqXG4gICAqIERlZmluZWQgc2NoZW1hcyBjYW4gYmUgcmVmZXJlbmNlZCBieSBgbmFtZWAgaW4gcHJvbXB0cyBpbiBwbGFjZSBvZiBpbmxpbmUgc2NoZW1hcy5cbiAgICovXG4gIGRlZmluZVNjaGVtYShuYW1lLCBzY2hlbWEpIHtcbiAgICByZXR1cm4gKDAsIGltcG9ydF9jb3JlLmRlZmluZVNjaGVtYSkodGhpcy5yZWdpc3RyeSwgbmFtZSwgc2NoZW1hKTtcbiAgfVxuICAvKipcbiAgICogRGVmaW5lcyBhbmQgcmVnaXN0ZXJzIGEgc2NoZW1hIGZyb20gYSBKU09OIHNjaGVtYS5cbiAgICpcbiAgICogRGVmaW5lZCBzY2hlbWFzIGNhbiBiZSByZWZlcmVuY2VkIGJ5IGBuYW1lYCBpbiBwcm9tcHRzIGluIHBsYWNlIG9mIGlubGluZSBzY2hlbWFzLlxuICAgKi9cbiAgZGVmaW5lSnNvblNjaGVtYShuYW1lLCBqc29uU2NoZW1hKSB7XG4gICAgcmV0dXJuICgwLCBpbXBvcnRfY29yZS5kZWZpbmVKc29uU2NoZW1hKSh0aGlzLnJlZ2lzdHJ5LCBuYW1lLCBqc29uU2NoZW1hKTtcbiAgfVxuICAvKipcbiAgICogRGVmaW5lcyBhIG5ldyBtb2RlbCBhbmQgYWRkcyBpdCB0byB0aGUgcmVnaXN0cnkuXG4gICAqL1xuICBkZWZpbmVNb2RlbChvcHRpb25zLCBydW5uZXIpIHtcbiAgICByZXR1cm4gKDAsIGltcG9ydF9tb2RlbC5kZWZpbmVNb2RlbCkodGhpcy5yZWdpc3RyeSwgb3B0aW9ucywgcnVubmVyKTtcbiAgfVxuICAvKipcbiAgICogTG9va3MgdXAgYSBwcm9tcHQgYnkgYG5hbWVgIChhbmQgb3B0aW9uYWxseSBgdmFyaWFudGApLiBDYW4gYmUgdXNlZCB0byBsb29rdXBcbiAgICogLnByb21wdCBmaWxlcyBvciBwcm9tcHRzIHByZXZpb3VzbHkgZGVmaW5lZCB3aXRoIHtAbGluayBHZW5raXQuZGVmaW5lUHJvbXB0fVxuICAgKi9cbiAgcHJvbXB0KG5hbWUsIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gdGhpcy53cmFwRXhlY3V0YWJsZVByb21wdFByb21pc2UoXG4gICAgICAoMCwgaW1wb3J0X2FpLnByb21wdCkodGhpcy5yZWdpc3RyeSwgbmFtZSwge1xuICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICBkaXI6IHRoaXMub3B0aW9ucy5wcm9tcHREaXIgPz8gXCIuL3Byb21wdHNcIlxuICAgICAgfSlcbiAgICApO1xuICB9XG4gIHdyYXBFeGVjdXRhYmxlUHJvbXB0UHJvbWlzZShwcm9taXNlKSB7XG4gICAgY29uc3QgZXhlY3V0YWJsZVByb21wdCA9IGFzeW5jIChpbnB1dCwgb3B0cykgPT4ge1xuICAgICAgcmV0dXJuIChhd2FpdCBwcm9taXNlKShpbnB1dCwgb3B0cyk7XG4gICAgfTtcbiAgICBleGVjdXRhYmxlUHJvbXB0LnJlbmRlciA9IGFzeW5jIChvcHQpID0+IHtcbiAgICAgIHJldHVybiAoYXdhaXQgcHJvbWlzZSkucmVuZGVyKG9wdC5pbnB1dCwgb3B0KTtcbiAgICB9O1xuICAgIGV4ZWN1dGFibGVQcm9tcHQuc3RyZWFtID0gKGlucHV0LCBvcHRzKSA9PiB7XG4gICAgICByZXR1cm4gdGhpcy5nZW5lcmF0ZVN0cmVhbShcbiAgICAgICAgcHJvbWlzZS50aGVuKFxuICAgICAgICAgIChhY3Rpb24pID0+IGFjdGlvbi5yZW5kZXIoaW5wdXQsIHtcbiAgICAgICAgICAgIC4uLm9wdHNcbiAgICAgICAgICB9KVxuICAgICAgICApXG4gICAgICApO1xuICAgIH07XG4gICAgZXhlY3V0YWJsZVByb21wdC5hc1Rvb2wgPSBhc3luYyAoKSA9PiB7XG4gICAgICByZXR1cm4gKGF3YWl0IHByb21pc2UpLmFzVG9vbCgpO1xuICAgIH07XG4gICAgcmV0dXJuIGV4ZWN1dGFibGVQcm9tcHQ7XG4gIH1cbiAgLyoqXG4gICAqIERlZmluZXMgYW5kIHJlZ2lzdGVycyBhIHByb21wdCBiYXNlZCBvbiBhIGZ1bmN0aW9uLlxuICAgKlxuICAgKiBUaGlzIGlzIGFuIGFsdGVybmF0aXZlIHRvIGRlZmluaW5nIGFuZCBpbXBvcnRpbmcgYSAucHJvbXB0IGZpbGUsIHByb3ZpZGluZ1xuICAgKiB0aGUgbW9zdCBhZHZhbmNlZCBjb250cm9sIG92ZXIgaG93IHRoZSBmaW5hbCByZXF1ZXN0IHRvIHRoZSBtb2RlbCBpcyBtYWRlLlxuICAgKlxuICAgKiBAcGFyYW0gb3B0aW9ucyAtIFByb21wdCBtZXRhZGF0YSBpbmNsdWRpbmcgbW9kZWwsIG1vZGVsIHBhcmFtcyxcbiAgICogaW5wdXQvb3V0cHV0IHNjaGVtYXMsIGV0Y1xuICAgKiBAcGFyYW0gZm4gLSBBIGZ1bmN0aW9uIHRoYXQgcmV0dXJucyBhIHtAbGluayBHZW5lcmF0ZVJlcXVlc3R9LiBBbnkgY29uZmlnXG4gICAqIHBhcmFtZXRlcnMgc3BlY2lmaWVkIGJ5IHRoZSB7QGxpbmsgR2VuZXJhdGVSZXF1ZXN0fSB3aWxsIHRha2UgcHJlY2VkZW5jZVxuICAgKiBvdmVyIGFueSBwYXJhbWV0ZXJzIHNwZWNpZmllZCBieSBgb3B0aW9uc2AuXG4gICAqXG4gICAqIGBgYHRzXG4gICAqIGNvbnN0IGhpID0gYWkuZGVmaW5lUHJvbXB0KFxuICAgKiAgIHtcbiAgICogICAgIG5hbWU6ICdoaScsXG4gICAqICAgICBpbnB1dDoge1xuICAgKiAgICAgICBzY2hlbWE6IHoub2JqZWN0KHtcbiAgICogICAgICAgICBuYW1lOiB6LnN0cmluZygpLFxuICAgKiAgICAgICB9KSxcbiAgICogICAgIH0sXG4gICAqICAgICBjb25maWc6IHtcbiAgICogICAgICAgdGVtcGVyYXR1cmU6IDEsXG4gICAqICAgICB9LFxuICAgKiAgIH0sXG4gICAqICAgYXN5bmMgKGlucHV0KSA9PiB7XG4gICAqICAgICByZXR1cm4ge1xuICAgKiAgICAgICBtZXNzYWdlczogWyB7IHJvbGU6ICd1c2VyJywgY29udGVudDogW3sgdGV4dDogYGhpICR7aW5wdXQubmFtZX1gIH1dIH0gXSxcbiAgICogICAgIH07XG4gICAqICAgfVxuICAgKiApO1xuICAgKiBjb25zdCB7IHRleHQgfSA9IGF3YWl0IGhpKHsgbmFtZTogJ0dlbmtpdCcgfSk7XG4gICAqIGBgYFxuICAgKi9cbiAgZGVmaW5lUHJvbXB0KG9wdGlvbnMsIHRlbXBsYXRlT3JGbikge1xuICAgIGlmICh0ZW1wbGF0ZU9yRm4pIHtcbiAgICAgIGlmIChvcHRpb25zLm1lc3NhZ2VzKSB7XG4gICAgICAgIHRocm93IG5ldyBpbXBvcnRfY29yZS5HZW5raXRFcnJvcih7XG4gICAgICAgICAgc3RhdHVzOiBcIklOVkFMSURfQVJHVU1FTlRcIixcbiAgICAgICAgICBtZXNzYWdlOiBcIkNhbm5vdCBzcGVjaWZ5IHRlbXBsYXRlL2Z1bmN0aW9uIGFyZ3VtZW50IGFuZCBgb3B0aW9ucy5tZXNzYWdlc2AgYXQgdGhlIHNhbWUgdGltZVwiXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgICAgaWYgKHR5cGVvZiB0ZW1wbGF0ZU9yRm4gPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgcmV0dXJuICgwLCBpbXBvcnRfYWkuZGVmaW5lUHJvbXB0KSh0aGlzLnJlZ2lzdHJ5LCB7XG4gICAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgICBtZXNzYWdlczogdGVtcGxhdGVPckZuXG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcmV0dXJuICgwLCBpbXBvcnRfYWkuZGVmaW5lUHJvbXB0KSh0aGlzLnJlZ2lzdHJ5LCB7XG4gICAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgICBtZXNzYWdlczogYXN5bmMgKGlucHV0KSA9PiB7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRlbXBsYXRlT3JGbihpbnB1dCk7XG4gICAgICAgICAgICByZXR1cm4gcmVzcG9uc2UubWVzc2FnZXM7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuICgwLCBpbXBvcnRfYWkuZGVmaW5lUHJvbXB0KSh0aGlzLnJlZ2lzdHJ5LCBvcHRpb25zKTtcbiAgfVxuICAvKipcbiAgICogQ3JlYXRlcyBhIHJldHJpZXZlciBhY3Rpb24gZm9yIHRoZSBwcm92aWRlZCB7QGxpbmsgUmV0cmlldmVyRm59IGltcGxlbWVudGF0aW9uLlxuICAgKi9cbiAgZGVmaW5lUmV0cmlldmVyKG9wdGlvbnMsIHJ1bm5lcikge1xuICAgIHJldHVybiAoMCwgaW1wb3J0X3JldHJpZXZlci5kZWZpbmVSZXRyaWV2ZXIpKHRoaXMucmVnaXN0cnksIG9wdGlvbnMsIHJ1bm5lcik7XG4gIH1cbiAgLyoqXG4gICAqIGRlZmluZVNpbXBsZVJldHJpZXZlciBtYWtlcyBpdCBlYXN5IHRvIG1hcCBleGlzdGluZyBkYXRhIGludG8gZG9jdW1lbnRzIHRoYXRcbiAgICogY2FuIGJlIHVzZWQgZm9yIHByb21wdCBhdWdtZW50YXRpb24uXG4gICAqXG4gICAqIEBwYXJhbSBvcHRpb25zIENvbmZpZ3VyYXRpb24gb3B0aW9ucyBmb3IgdGhlIHJldHJpZXZlci5cbiAgICogQHBhcmFtIGhhbmRsZXIgQSBmdW5jdGlvbiB0aGF0IHF1ZXJpZXMgYSBkYXRhc3RvcmUgYW5kIHJldHVybnMgaXRlbXMgZnJvbSB3aGljaCB0byBleHRyYWN0IGRvY3VtZW50cy5cbiAgICogQHJldHVybnMgQSBHZW5raXQgcmV0cmlldmVyLlxuICAgKi9cbiAgZGVmaW5lU2ltcGxlUmV0cmlldmVyKG9wdGlvbnMsIGhhbmRsZXIpIHtcbiAgICByZXR1cm4gKDAsIGltcG9ydF9yZXRyaWV2ZXIuZGVmaW5lU2ltcGxlUmV0cmlldmVyKSh0aGlzLnJlZ2lzdHJ5LCBvcHRpb25zLCBoYW5kbGVyKTtcbiAgfVxuICAvKipcbiAgICogQ3JlYXRlcyBhbiBpbmRleGVyIGFjdGlvbiBmb3IgdGhlIHByb3ZpZGVkIHtAbGluayBJbmRleGVyRm59IGltcGxlbWVudGF0aW9uLlxuICAgKi9cbiAgZGVmaW5lSW5kZXhlcihvcHRpb25zLCBydW5uZXIpIHtcbiAgICByZXR1cm4gKDAsIGltcG9ydF9yZXRyaWV2ZXIuZGVmaW5lSW5kZXhlcikodGhpcy5yZWdpc3RyeSwgb3B0aW9ucywgcnVubmVyKTtcbiAgfVxuICAvKipcbiAgICogQ3JlYXRlcyBldmFsdWF0b3IgYWN0aW9uIGZvciB0aGUgcHJvdmlkZWQge0BsaW5rIEV2YWx1YXRvckZufSBpbXBsZW1lbnRhdGlvbi5cbiAgICovXG4gIGRlZmluZUV2YWx1YXRvcihvcHRpb25zLCBydW5uZXIpIHtcbiAgICByZXR1cm4gKDAsIGltcG9ydF9ldmFsdWF0b3IuZGVmaW5lRXZhbHVhdG9yKSh0aGlzLnJlZ2lzdHJ5LCBvcHRpb25zLCBydW5uZXIpO1xuICB9XG4gIC8qKlxuICAgKiBDcmVhdGVzIGVtYmVkZGVyIG1vZGVsIGZvciB0aGUgcHJvdmlkZWQge0BsaW5rIEVtYmVkZGVyRm59IG1vZGVsIGltcGxlbWVudGF0aW9uLlxuICAgKi9cbiAgZGVmaW5lRW1iZWRkZXIob3B0aW9ucywgcnVubmVyKSB7XG4gICAgcmV0dXJuICgwLCBpbXBvcnRfZW1iZWRkZXIuZGVmaW5lRW1iZWRkZXIpKHRoaXMucmVnaXN0cnksIG9wdGlvbnMsIHJ1bm5lcik7XG4gIH1cbiAgLyoqXG4gICAqIGNyZWF0ZSBhIGhhbmRsZWJhcmRzIGhlbHBlciAoaHR0cHM6Ly9oYW5kbGViYXJzanMuY29tL2d1aWRlL2Jsb2NrLWhlbHBlcnMuaHRtbCkgdG8gYmUgdXNlZCBpbiBkb3Rwb3JtcHQgdGVtcGxhdGVzLlxuICAgKi9cbiAgZGVmaW5lSGVscGVyKG5hbWUsIGZuKSB7XG4gICAgKDAsIGltcG9ydF9haS5kZWZpbmVIZWxwZXIpKHRoaXMucmVnaXN0cnksIG5hbWUsIGZuKTtcbiAgfVxuICAvKipcbiAgICogQ3JlYXRlcyBhIGhhbmRsZWJhcnMgcGFydGlhbCAoaHR0cHM6Ly9oYW5kbGViYXJzanMuY29tL2d1aWRlL3BhcnRpYWxzLmh0bWwpIHRvIGJlIHVzZWQgaW4gZG90cG9ybXB0IHRlbXBsYXRlcy5cbiAgICovXG4gIGRlZmluZVBhcnRpYWwobmFtZSwgc291cmNlKSB7XG4gICAgKDAsIGltcG9ydF9haS5kZWZpbmVQYXJ0aWFsKSh0aGlzLnJlZ2lzdHJ5LCBuYW1lLCBzb3VyY2UpO1xuICB9XG4gIC8qKlxuICAgKiAgQ3JlYXRlcyBhIHJlcmFua2VyIGFjdGlvbiBmb3IgdGhlIHByb3ZpZGVkIHtAbGluayBSZXJhbmtlckZufSBpbXBsZW1lbnRhdGlvbi5cbiAgICovXG4gIGRlZmluZVJlcmFua2VyKG9wdGlvbnMsIHJ1bm5lcikge1xuICAgIHJldHVybiAoMCwgaW1wb3J0X3JlcmFua2VyLmRlZmluZVJlcmFua2VyKSh0aGlzLnJlZ2lzdHJ5LCBvcHRpb25zLCBydW5uZXIpO1xuICB9XG4gIC8qKlxuICAgKiBFbWJlZHMgdGhlIGdpdmVuIGBjb250ZW50YCB1c2luZyB0aGUgc3BlY2lmaWVkIGBlbWJlZGRlcmAuXG4gICAqL1xuICBlbWJlZChwYXJhbXMpIHtcbiAgICByZXR1cm4gKDAsIGltcG9ydF9haS5lbWJlZCkodGhpcy5yZWdpc3RyeSwgcGFyYW1zKTtcbiAgfVxuICAvKipcbiAgICogQSB2ZW5lZXIgZm9yIGludGVyYWN0aW5nIHdpdGggZW1iZWRkZXIgbW9kZWxzIGluIGJ1bGsuXG4gICAqL1xuICBlbWJlZE1hbnkocGFyYW1zKSB7XG4gICAgcmV0dXJuICgwLCBpbXBvcnRfZW1iZWRkZXIuZW1iZWRNYW55KSh0aGlzLnJlZ2lzdHJ5LCBwYXJhbXMpO1xuICB9XG4gIC8qKlxuICAgKiBFdmFsdWF0ZXMgdGhlIGdpdmVuIGBkYXRhc2V0YCB1c2luZyB0aGUgc3BlY2lmaWVkIGBldmFsdWF0b3JgLlxuICAgKi9cbiAgZXZhbHVhdGUocGFyYW1zKSB7XG4gICAgcmV0dXJuICgwLCBpbXBvcnRfYWkuZXZhbHVhdGUpKHRoaXMucmVnaXN0cnksIHBhcmFtcyk7XG4gIH1cbiAgLyoqXG4gICAqIFJlcmFua3MgZG9jdW1lbnRzIGZyb20gYSB7QGxpbmsgUmVyYW5rZXJBcmd1bWVudH0gYmFzZWQgb24gdGhlIHByb3ZpZGVkIHF1ZXJ5LlxuICAgKi9cbiAgcmVyYW5rKHBhcmFtcykge1xuICAgIHJldHVybiAoMCwgaW1wb3J0X2FpLnJlcmFuaykodGhpcy5yZWdpc3RyeSwgcGFyYW1zKTtcbiAgfVxuICAvKipcbiAgICogSW5kZXhlcyBgZG9jdW1lbnRzYCB1c2luZyB0aGUgcHJvdmlkZWQgYGluZGV4ZXJgLlxuICAgKi9cbiAgaW5kZXgocGFyYW1zKSB7XG4gICAgcmV0dXJuICgwLCBpbXBvcnRfcmV0cmlldmVyLmluZGV4KSh0aGlzLnJlZ2lzdHJ5LCBwYXJhbXMpO1xuICB9XG4gIC8qKlxuICAgKiBSZXRyaWV2ZXMgZG9jdW1lbnRzIGZyb20gdGhlIGByZXRyaWV2ZXJgIGJhc2VkIG9uIHRoZSBwcm92aWRlZCBgcXVlcnlgLlxuICAgKi9cbiAgcmV0cmlldmUocGFyYW1zKSB7XG4gICAgcmV0dXJuICgwLCBpbXBvcnRfYWkucmV0cmlldmUpKHRoaXMucmVnaXN0cnksIHBhcmFtcyk7XG4gIH1cbiAgYXN5bmMgZ2VuZXJhdGUob3B0aW9ucykge1xuICAgIGxldCByZXNvbHZlZE9wdGlvbnM7XG4gICAgaWYgKG9wdGlvbnMgaW5zdGFuY2VvZiBQcm9taXNlKSB7XG4gICAgICByZXNvbHZlZE9wdGlvbnMgPSBhd2FpdCBvcHRpb25zO1xuICAgIH0gZWxzZSBpZiAodHlwZW9mIG9wdGlvbnMgPT09IFwic3RyaW5nXCIgfHwgQXJyYXkuaXNBcnJheShvcHRpb25zKSkge1xuICAgICAgcmVzb2x2ZWRPcHRpb25zID0ge1xuICAgICAgICBwcm9tcHQ6IG9wdGlvbnNcbiAgICAgIH07XG4gICAgfSBlbHNlIHtcbiAgICAgIHJlc29sdmVkT3B0aW9ucyA9IG9wdGlvbnM7XG4gICAgfVxuICAgIHJldHVybiAoMCwgaW1wb3J0X2FpLmdlbmVyYXRlKSh0aGlzLnJlZ2lzdHJ5LCByZXNvbHZlZE9wdGlvbnMpO1xuICB9XG4gIGdlbmVyYXRlU3RyZWFtKG9wdGlvbnMpIHtcbiAgICBpZiAodHlwZW9mIG9wdGlvbnMgPT09IFwic3RyaW5nXCIgfHwgQXJyYXkuaXNBcnJheShvcHRpb25zKSkge1xuICAgICAgb3B0aW9ucyA9IHsgcHJvbXB0OiBvcHRpb25zIH07XG4gICAgfVxuICAgIHJldHVybiAoMCwgaW1wb3J0X2FpLmdlbmVyYXRlU3RyZWFtKSh0aGlzLnJlZ2lzdHJ5LCBvcHRpb25zKTtcbiAgfVxuICBydW4obmFtZSwgZnVuY09ySW5wdXQsIG1heWJlRnVuYykge1xuICAgIGlmIChtYXliZUZ1bmMpIHtcbiAgICAgIHJldHVybiAoMCwgaW1wb3J0X2NvcmUucnVuKShuYW1lLCBmdW5jT3JJbnB1dCwgbWF5YmVGdW5jLCB0aGlzLnJlZ2lzdHJ5KTtcbiAgICB9XG4gICAgcmV0dXJuICgwLCBpbXBvcnRfY29yZS5ydW4pKG5hbWUsIGZ1bmNPcklucHV0LCB0aGlzLnJlZ2lzdHJ5KTtcbiAgfVxuICAvKipcbiAgICogUmV0dXJucyBjdXJyZW50IGFjdGlvbiAob3IgZmxvdykgaW52b2NhdGlvbiBjb250ZXh0LiBDYW4gYmUgdXNlZCB0byBhY2Nlc3MgdGhpbmdzIGxpa2UgYXV0aFxuICAgKiBkYXRhIHNldCBieSBIVFRQIHNlcnZlciBmcmFtZXdvcmtzLiBJZiBpbnZva2VkIG91dHNpZGUgb2YgYW4gYWN0aW9uIChlLmcuIGZsb3cgb3IgdG9vbCkgd2lsbFxuICAgKiByZXR1cm4gYHVuZGVmaW5lZGAuXG4gICAqL1xuICBjdXJyZW50Q29udGV4dCgpIHtcbiAgICByZXR1cm4gKDAsIGltcG9ydF9jb3JlLmdldENvbnRleHQpKHRoaXMpO1xuICB9XG4gIC8qKlxuICAgKiBDb25maWd1cmVzIHRoZSBHZW5raXQgaW5zdGFuY2UuXG4gICAqL1xuICBjb25maWd1cmUoKSB7XG4gICAgY29uc3QgYWN0aXZlUmVnaXN0cnkgPSB0aGlzLnJlZ2lzdHJ5O1xuICAgICgwLCBpbXBvcnRfbW9kZWwuZGVmaW5lR2VuZXJhdGVBY3Rpb24pKGFjdGl2ZVJlZ2lzdHJ5KTtcbiAgICAoMCwgaW1wb3J0X2Zvcm1hdHMuY29uZmlndXJlRm9ybWF0cykoYWN0aXZlUmVnaXN0cnkpO1xuICAgIGNvbnN0IHBsdWdpbnMgPSBbLi4udGhpcy5vcHRpb25zLnBsdWdpbnMgPz8gW11dO1xuICAgIGlmICh0aGlzLm9wdGlvbnMubW9kZWwpIHtcbiAgICAgIHRoaXMucmVnaXN0cnkucmVnaXN0ZXJWYWx1ZShcbiAgICAgICAgXCJkZWZhdWx0TW9kZWxcIixcbiAgICAgICAgXCJkZWZhdWx0TW9kZWxcIixcbiAgICAgICAgdGhpcy5vcHRpb25zLm1vZGVsXG4gICAgICApO1xuICAgIH1cbiAgICBpZiAodGhpcy5vcHRpb25zLnByb21wdERpciAhPT0gbnVsbCkge1xuICAgICAgKDAsIGltcG9ydF9haS5sb2FkUHJvbXB0Rm9sZGVyKShcbiAgICAgICAgdGhpcy5yZWdpc3RyeSxcbiAgICAgICAgdGhpcy5vcHRpb25zLnByb21wdERpciA/PyBcIi4vcHJvbXB0c1wiLFxuICAgICAgICBcIlwiXG4gICAgICApO1xuICAgIH1cbiAgICBwbHVnaW5zLmZvckVhY2goKHBsdWdpbikgPT4ge1xuICAgICAgY29uc3QgbG9hZGVkUGx1Z2luID0gcGx1Z2luKHRoaXMpO1xuICAgICAgaW1wb3J0X2xvZ2dpbmcubG9nZ2VyLmRlYnVnKGBSZWdpc3RlcmluZyBwbHVnaW4gJHtsb2FkZWRQbHVnaW4ubmFtZX0uLi5gKTtcbiAgICAgIGFjdGl2ZVJlZ2lzdHJ5LnJlZ2lzdGVyUGx1Z2luUHJvdmlkZXIobG9hZGVkUGx1Z2luLm5hbWUsIHtcbiAgICAgICAgbmFtZTogbG9hZGVkUGx1Z2luLm5hbWUsXG4gICAgICAgIGFzeW5jIGluaXRpYWxpemVyKCkge1xuICAgICAgICAgIGltcG9ydF9sb2dnaW5nLmxvZ2dlci5kZWJ1ZyhgSW5pdGlhbGl6aW5nIHBsdWdpbiAke2xvYWRlZFBsdWdpbi5uYW1lfTpgKTtcbiAgICAgICAgICBhd2FpdCBsb2FkZWRQbHVnaW4uaW5pdGlhbGl6ZXIoKTtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfSk7XG4gIH1cbiAgLyoqXG4gICAqIFN0b3BzIGFsbCBzZXJ2ZXJzLlxuICAgKi9cbiAgYXN5bmMgc3RvcFNlcnZlcnMoKSB7XG4gICAgYXdhaXQgdGhpcy5yZWZsZWN0aW9uU2VydmVyPy5zdG9wKCk7XG4gICAgdGhpcy5yZWZsZWN0aW9uU2VydmVyID0gbnVsbDtcbiAgfVxufVxuZnVuY3Rpb24gZ2Vua2l0KG9wdGlvbnMpIHtcbiAgcmV0dXJuIG5ldyBHZW5raXQob3B0aW9ucyk7XG59XG5jb25zdCBzaHV0ZG93biA9IGFzeW5jICgpID0+IHtcbiAgaW1wb3J0X2xvZ2dpbmcubG9nZ2VyLmluZm8oXCJTaHV0dGluZyBkb3duIGFsbCBHZW5raXQgc2VydmVycy4uLlwiKTtcbiAgYXdhaXQgaW1wb3J0X2NvcmUuUmVmbGVjdGlvblNlcnZlci5zdG9wQWxsKCk7XG4gIHByb2Nlc3MuZXhpdCgwKTtcbn07XG5wcm9jZXNzLm9uKFwiU0lHVEVSTVwiLCBzaHV0ZG93bik7XG5wcm9jZXNzLm9uKFwiU0lHSU5UXCIsIHNodXRkb3duKTtcbmxldCBkaXNhYmxlUmVmbGVjdGlvbkFwaSA9IGZhbHNlO1xuZnVuY3Rpb24gX19kaXNhYmxlUmVmbGVjdGlvbkFwaSgpIHtcbiAgZGlzYWJsZVJlZmxlY3Rpb25BcGkgPSB0cnVlO1xufVxuLy8gQW5ub3RhdGUgdGhlIENvbW1vbkpTIGV4cG9ydCBuYW1lcyBmb3IgRVNNIGltcG9ydCBpbiBub2RlOlxuMCAmJiAobW9kdWxlLmV4cG9ydHMgPSB7XG4gIEdlbmtpdCxcbiAgX19kaXNhYmxlUmVmbGVjdGlvbkFwaSxcbiAgZ2Vua2l0XG59KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdlbmtpdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/genkit.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/index.js":
/*!******************************************!*\
  !*** ./node_modules/genkit/lib/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar src_exports = {};\n__export(src_exports, {\n  Genkit: () => import_genkit.Genkit,\n  genkit: () => import_genkit.genkit\n});\nmodule.exports = __toCommonJS(src_exports);\n__reExport(src_exports, __webpack_require__(/*! ./common.js */ \"(action-browser)/./node_modules/genkit/lib/common.js\"), module.exports);\nvar import_genkit = __webpack_require__(/*! ./genkit.js */ \"(action-browser)/./node_modules/genkit/lib/genkit.js\");\n/**\n * @license\n *\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/index.mjs":
/*!*******************************************!*\
  !*** ./node_modules/genkit/lib/index.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Genkit: () => (/* reexport safe */ _genkit_js__WEBPACK_IMPORTED_MODULE_1__.Genkit),\n/* harmony export */   genkit: () => (/* reexport safe */ _genkit_js__WEBPACK_IMPORTED_MODULE_1__.genkit)\n/* harmony export */ });\n/* harmony import */ var _common_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./common.js */ \"(action-browser)/./node_modules/genkit/lib/common.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _common_js__WEBPACK_IMPORTED_MODULE_0__) if([\"default\",\"Genkit\",\"genkit\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _common_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _genkit_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./genkit.js */ \"(action-browser)/./node_modules/genkit/lib/genkit.js\");\n/**\n * @license\n *\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9nZW5raXQvbGliL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUM0QjtBQUNpQjtBQUkzQztBQUNGIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXE5ldyBmb2xkZXJcXFZpY2VcXFZpY2VcXG5vZGVfbW9kdWxlc1xcZ2Vua2l0XFxsaWJcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKlxuICogQ29weXJpZ2h0IDIwMjUgR29vZ2xlIExMQ1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuZXhwb3J0ICogZnJvbSBcIi4vY29tbW9uLmpzXCI7XG5pbXBvcnQgeyBHZW5raXQsIGdlbmtpdCB9IGZyb20gXCIuL2dlbmtpdC5qc1wiO1xuZXhwb3J0IHtcbiAgR2Vua2l0LFxuICBnZW5raXRcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/logging.js":
/*!********************************************!*\
  !*** ./node_modules/genkit/lib/logging.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar logging_exports = {};\n__export(logging_exports, {\n  logger: () => import_logging.logger\n});\nmodule.exports = __toCommonJS(logging_exports);\nvar import_logging = __webpack_require__(/*! @genkit-ai/core/logging */ \"(action-browser)/./node_modules/@genkit-ai/core/lib/logging.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=logging.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/logging.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/middleware.js":
/*!***********************************************!*\
  !*** ./node_modules/genkit/lib/middleware.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar middleware_exports = {};\n__export(middleware_exports, {\n  augmentWithContext: () => import_middleware.augmentWithContext,\n  downloadRequestMedia: () => import_middleware.downloadRequestMedia,\n  simulateSystemPrompt: () => import_middleware.simulateSystemPrompt,\n  validateSupport: () => import_middleware.validateSupport\n});\nmodule.exports = __toCommonJS(middleware_exports);\nvar import_middleware = __webpack_require__(/*! @genkit-ai/ai/model/middleware */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/model/middleware.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=middleware.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/middleware.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/model.js":
/*!******************************************!*\
  !*** ./node_modules/genkit/lib/model.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar model_exports = {};\n__export(model_exports, {\n  CandidateErrorSchema: () => import_model.CandidateErrorSchema,\n  CandidateSchema: () => import_model.CandidateSchema,\n  CustomPartSchema: () => import_model.CustomPartSchema,\n  DataPartSchema: () => import_model.DataPartSchema,\n  GenerateRequestSchema: () => import_model.GenerateRequestSchema,\n  GenerateResponseChunkSchema: () => import_model.GenerateResponseChunkSchema,\n  GenerateResponseSchema: () => import_model.GenerateResponseSchema,\n  GenerationCommonConfigSchema: () => import_model.GenerationCommonConfigSchema,\n  GenerationUsageSchema: () => import_model.GenerationUsageSchema,\n  MediaPartSchema: () => import_model.MediaPartSchema,\n  MessageSchema: () => import_model.MessageSchema,\n  ModelInfoSchema: () => import_model.ModelInfoSchema,\n  ModelRequestSchema: () => import_model.ModelRequestSchema,\n  ModelResponseSchema: () => import_model.ModelResponseSchema,\n  PartSchema: () => import_model.PartSchema,\n  RoleSchema: () => import_model.RoleSchema,\n  TextPartSchema: () => import_model.TextPartSchema,\n  ToolDefinitionSchema: () => import_model.ToolDefinitionSchema,\n  ToolRequestPartSchema: () => import_model.ToolRequestPartSchema,\n  ToolResponsePartSchema: () => import_model.ToolResponsePartSchema,\n  getBasicUsageStats: () => import_model.getBasicUsageStats,\n  modelRef: () => import_model.modelRef,\n  simulateConstrainedGeneration: () => import_model.simulateConstrainedGeneration\n});\nmodule.exports = __toCommonJS(model_exports);\nvar import_model = __webpack_require__(/*! @genkit-ai/ai/model */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/model.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=model.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/model.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/plugin.mjs":
/*!********************************************!*\
  !*** ./node_modules/genkit/lib/plugin.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genkitPlugin: () => (/* binding */ genkitPlugin)\n/* harmony export */ });\nfunction genkitPlugin(pluginName, initFn) {\n  return (genkit) => ({\n    name: pluginName,\n    initializer: async () => {\n      await initFn(genkit);\n    }\n  });\n}\n\n//# sourceMappingURL=plugin.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9nZW5raXQvbGliL3BsdWdpbi5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcTmV3IGZvbGRlclxcVmljZVxcVmljZVxcbm9kZV9tb2R1bGVzXFxnZW5raXRcXGxpYlxccGx1Z2luLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBnZW5raXRQbHVnaW4ocGx1Z2luTmFtZSwgaW5pdEZuKSB7XG4gIHJldHVybiAoZ2Vua2l0KSA9PiAoe1xuICAgIG5hbWU6IHBsdWdpbk5hbWUsXG4gICAgaW5pdGlhbGl6ZXI6IGFzeW5jICgpID0+IHtcbiAgICAgIGF3YWl0IGluaXRGbihnZW5raXQpO1xuICAgIH1cbiAgfSk7XG59XG5leHBvcnQge1xuICBnZW5raXRQbHVnaW5cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wbHVnaW4ubWpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/plugin.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/registry.js":
/*!*********************************************!*\
  !*** ./node_modules/genkit/lib/registry.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar registry_exports = {};\n__export(registry_exports, {\n  Registry: () => import_registry.Registry\n});\nmodule.exports = __toCommonJS(registry_exports);\nvar import_registry = __webpack_require__(/*! @genkit-ai/core/registry */ \"(action-browser)/./node_modules/@genkit-ai/core/lib/registry.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/registry.js\n");

/***/ })

};
;