const { app, BrowserWindow, ipcMain, dialog, shell, session } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');
const fsPromises = require('fs').promises;
const os = require('os');

// Check if we're in development mode
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;

// Keep a global reference of the window objects
let dashboardWindow = null;
let ideWindow = null;
let authWindow = null;

// Development server URLs
const DASHBOARD_URL = 'http://localhost:3000';
const IDE_URL = 'http://localhost:9003';

function createDashboardWindow() {
  // Get screen dimensions for fullscreen
  const { screen } = require('electron');
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width, height } = primaryDisplay.workAreaSize;

  // Create the dashboard window
  dashboardWindow = new BrowserWindow({
    width: width,
    height: height,
    minWidth: 1200,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false, // Allow loading local resources and external iframes
      devTools: false, // Always disable DevTools
      allowRunningInsecureContent: true, // Allow mixed content for iframes
      experimentalFeatures: true, // Enable experimental web features
      enableBlinkFeatures: 'CSSColorSchemeUARendering', // Enable modern CSS features
      additionalArguments: [
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--allow-running-insecure-content',
        '--disable-site-isolation-trials'
      ]
    },
    icon: path.join(__dirname, '../AppLogo.png'),
    title: 'VICE IDE - Professional Development Environment',
    show: false, // Don't show until ready
    autoHideMenuBar: true, // Hide the menu bar
    frame: true,
    maximizable: true,
    resizable: true,
    fullscreen: false, // Don't start in fullscreen mode
    kiosk: false
  });

  // Load the dashboard
  if (isDev) {
    dashboardWindow.loadURL(DASHBOARD_URL);
  } else {
    // In production, serve static files with a simple express server
    const express = require('express');
    const server = express();

    // Serve static files from Vice build
    server.use(express.static(path.join(__dirname, '../Vice/.next/static')));
    server.use(express.static(path.join(__dirname, '../Vice/public')));

    // Fallback to index for SPA routing
    server.get('*', (req, res) => {
      res.sendFile(path.join(__dirname, '../Vice/.next/static/index.html'), (err) => {
        if (err) {
          res.send('<h1>VICE Dashboard</h1><p>Loading...</p>');
        }
      });
    });

    server.listen(3000, (err) => {
      if (err) {
        console.error('Failed to start dashboard server:', err);
        dashboardWindow.loadURL('data:text/html,<h1>VICE Dashboard</h1><p>Starting...</p>');
      } else {
        console.log('Dashboard server ready on http://localhost:3000');
        dashboardWindow.loadURL('http://localhost:3000');
      }
    });
  }

  // Show window when ready to prevent visual flash
  dashboardWindow.once('ready-to-show', () => {
    // Maximize window for full screen experience
    dashboardWindow.maximize();
    dashboardWindow.show();

    // Focus on dashboard window
    dashboardWindow.focus();

    // Set professional title
    dashboardWindow.setTitle('VICE IDE - Professional Development Environment');
  });

  // Handle window closed
  dashboardWindow.on('closed', () => {
    dashboardWindow = null;
    // Close IDE window if dashboard is closed
    if (ideWindow) {
      ideWindow.close();
    }
  });

  // Open external links in browser
  dashboardWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Block Clerk authentication at webContents level
  dashboardWindow.webContents.session.webRequest.onBeforeRequest({
    urls: ['*://*/*']
  }, (details, callback) => {
    if (details.url.includes('clerk.accounts.dev') ||
        details.url.includes('/handshake') ||
        details.url.includes('__clerk_') ||
        details.url.includes('pure-seasnail-52.clerk.accounts.dev')) {
      console.log('🚫 Dashboard: Blocking Clerk request:', details.url);
      callback({ cancel: true });
    } else {
      callback({});
    }
  });

  // Always disable DevTools and developer shortcuts
  // Disable right-click context menu
  dashboardWindow.webContents.on('context-menu', (e) => {
    e.preventDefault();
  });

  // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
  dashboardWindow.webContents.on('before-input-event', (event, input) => {
    if (input.key === 'F12' ||
        (input.control && input.shift && (input.key === 'I' || input.key === 'J')) ||
        (input.control && input.key === 'U')) {
      event.preventDefault();
    }
  });

  // Remove menu bar completely
  dashboardWindow.setMenuBarVisibility(false);
  dashboardWindow.setMenu(null);

  // Allow iframe content and external resources
  dashboardWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https: http: ws: wss:; " +
          "frame-src 'self' https: http: data: blob:; " +
          "frame-ancestors 'self' https: http:; " +
          "img-src 'self' data: blob: https: http:; " +
          "script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http:; " +
          "style-src 'self' 'unsafe-inline' https: http:; " +
          "connect-src 'self' https: http: ws: wss:;"
        ]
      }
    });
  });
}

function createIdeWindow() {
  // Get screen dimensions for fullscreen
  const { screen } = require('electron');
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width, height } = primaryDisplay.workAreaSize;

  // Create the IDE window
  ideWindow = new BrowserWindow({
    width: width,
    height: height,
    minWidth: 1400,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false, // Allow loading local resources
      devTools: false // Always disable DevTools
    },
    icon: path.join(__dirname, '../AppLogo.png'),
    title: 'VICE IDE - Professional Code Editor',
    show: false, // Don't show until ready
    autoHideMenuBar: true, // Hide the menu bar
    frame: true,
    maximizable: true,
    resizable: true,
    fullscreen: false, // Don't start in fullscreen mode
    kiosk: false
  });

  // Load the IDE
  if (isDev) {
    ideWindow.loadURL(IDE_URL);
  } else {
    // In production, serve static files with a simple express server
    const express = require('express');
    const server = express();

    // Serve static files from ViceIde build
    server.use(express.static(path.join(__dirname, '../ViceIde/.next/static')));
    server.use(express.static(path.join(__dirname, '../ViceIde/public')));

    // Fallback to index for SPA routing
    server.get('*', (req, res) => {
      res.sendFile(path.join(__dirname, '../ViceIde/.next/static/index.html'), (err) => {
        if (err) {
          res.send('<h1>VICE IDE</h1><p>Loading...</p>');
        }
      });
    });

    server.listen(9003, (err) => {
      if (err) {
        console.error('Failed to start IDE server:', err);
        ideWindow.loadURL('data:text/html,<h1>VICE IDE</h1><p>Starting...</p>');
      } else {
        console.log('IDE server ready on http://localhost:9003');
        ideWindow.loadURL('http://localhost:9003');
      }
    });
  }

  // Show window when ready to prevent visual flash
  ideWindow.once('ready-to-show', () => {
    // Maximize window for full screen experience
    ideWindow.maximize();
    ideWindow.show();

    // Focus on IDE window
    ideWindow.focus();

    // Set professional title
    ideWindow.setTitle('VICE IDE - Professional Code Editor');
  });

  // Handle window closed
  ideWindow.on('closed', () => {
    ideWindow = null;
  });

  // Open external links in browser
  ideWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Block Clerk authentication at IDE webContents level
  ideWindow.webContents.session.webRequest.onBeforeRequest({
    urls: ['*://*/*']
  }, (details, callback) => {
    if (details.url.includes('clerk.accounts.dev') ||
        details.url.includes('/handshake') ||
        details.url.includes('__clerk_') ||
        details.url.includes('pure-seasnail-52.clerk.accounts.dev')) {
      console.log('🚫 IDE: Blocking Clerk request:', details.url);
      callback({ cancel: true });
    } else {
      callback({});
    }
  });

  // Disable DevTools and developer shortcuts in production
  if (!isDev) {
    // Disable right-click context menu
    ideWindow.webContents.on('context-menu', (e) => {
      e.preventDefault();
    });

    // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
    ideWindow.webContents.on('before-input-event', (event, input) => {
      if (input.key === 'F12' ||
          (input.control && input.shift && (input.key === 'I' || input.key === 'J')) ||
          (input.control && input.key === 'U')) {
        event.preventDefault();
      }
    });

    // Remove menu bar completely
    ideWindow.setMenuBarVisibility(false);
    ideWindow.setMenu(null);
  } else {
    // Open DevTools only in development
    ideWindow.webContents.openDevTools();
  }
}

// App event handlers
app.whenReady().then(() => {
  // Block Clerk authentication requests to prevent redirect loops
  session.defaultSession.webRequest.onBeforeRequest({
    urls: ['*://clerk.accounts.dev/*', '*://*.clerk.accounts.dev/*']
  }, (details, callback) => {
    console.log('🚫 Blocking Clerk authentication request:', details.url);
    // Block the request to prevent infinite redirects
    callback({ cancel: true });
  });

  // Also block requests that contain clerk handshake patterns
  session.defaultSession.webRequest.onBeforeRequest({
    urls: ['*://*/*']
  }, (details, callback) => {
    if (details.url.includes('clerk.accounts.dev') ||
        details.url.includes('/handshake') ||
        details.url.includes('__clerk_')) {
      console.log('🚫 Blocking Clerk-related request:', details.url);
      callback({ cancel: true });
    } else {
      callback({});
    }
  });

  createDashboardWindow();

  app.on('activate', () => {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) {
      createDashboardWindow();
    }
  });
});

app.on('window-all-closed', () => {
  // On macOS it is common for applications and their menu bar
  // to stay active until the user quits explicitly with Cmd + Q
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC handlers for communication between dashboard and IDE
ipcMain.handle('open-ide', async () => {
  if (!ideWindow) {
    createIdeWindow();
  } else {
    ideWindow.focus();
  }
  return true;
});

ipcMain.handle('close-ide', async () => {
  if (ideWindow) {
    ideWindow.close();
  }
  return true;
});

ipcMain.handle('load-project-in-ide', async (event, projectData) => {
  try {
    console.log('Loading project in IDE:', projectData);

    // Ensure IDE window is open
    if (!ideWindow) {
      createIdeWindow();
      // Wait for IDE to be ready
      await new Promise(resolve => {
        ideWindow.once('ready-to-show', resolve);
      });
    }

    // Wait a bit more for IDE to fully load
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Format project data properly for IDE - BULLETPROOF FILES HANDLING
    let filesArray = [];

    try {
      if (projectData.files) {
        if (Array.isArray(projectData.files)) {
          filesArray = projectData.files;
        } else if (typeof projectData.files === 'object') {
          filesArray = Object.values(projectData.files);
        } else {
          console.warn('[ELECTRON] Files is not an array or object:', typeof projectData.files);
          filesArray = [];
        }
      }
    } catch (error) {
      console.error('[ELECTRON] Error processing files:', error);
      filesArray = [];
    }

    const formattedProjectData = {
      projectName: projectData.projectName,
      projectPath: projectData.projectPath,
      files: filesArray.map((file, index) => ({
        id: file.id || `file-${Date.now()}-${index}`,
        name: file.name || `file-${index}`,
        content: file.content || ''
      })),
      stackName: projectData.stackName
    };

    console.log('Sending formatted project data to IDE:', formattedProjectData);
    console.log('Number of files:', formattedProjectData.files.length);

    // Send project data to IDE multiple times to ensure it's received
    for (let i = 0; i < 3; i++) {
      ideWindow.webContents.send('load-project', formattedProjectData);
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    ideWindow.focus();

    // Notify dashboard that IDE has opened successfully
    if (dashboardWindow && dashboardWindow.webContents) {
      dashboardWindow.webContents.send('command-progress', {
        currentStep: -1,
        totalSteps: -1,
        command: 'IDE opened successfully',
        status: 'ide-opened'
      });
      console.log('[IDE] ✅ IDE opened and project loaded successfully');
    }

    return true;
  } catch (error) {
    console.error('Error loading project in IDE:', error);
    return false;
  }
});

ipcMain.handle('create-project-files', async (event, projectData) => {
  try {
    const { projectName, projectPath, files = [] } = projectData;

    // Create project directory
    if (!fs.existsSync(projectPath)) {
      fs.mkdirSync(projectPath, { recursive: true });
    }

    // Create files - SAFE ITERATION
    if (files && Array.isArray(files) && files.length > 0) {
      for (const file of files) {
        try {
          const filePath = path.join(projectPath, file.name);
          const fileDir = path.dirname(filePath);

          // Create directory if it doesn't exist
          if (!fs.existsSync(fileDir)) {
            fs.mkdirSync(fileDir, { recursive: true });
          }

          // Write file content
          fs.writeFileSync(filePath, file.content || '');
        } catch (fileError) {
          console.error('[ELECTRON] Error creating file:', file.name, fileError);
        }
      }
    } else {
      console.log('[ELECTRON] No files to create or files is not an array');
    }

    return { success: true, message: 'Project files created successfully' };
  } catch (error) {
    console.error('Error creating project files:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('execute-stack-commands', async (event, projectData) => {
  try {
    const { stackName, projectName, projectPath, description, templateProjectId } = projectData;
    console.log(`Executing stack commands for ${stackName} project: ${projectName}`);
    console.log(`[STACK] 🎯 Template Project ID: ${templateProjectId}`);

    // Get user home directory properly
    const os = require('os');
    const userHomeDir = os.homedir();

    // Construct proper project path - handle both absolute and relative paths
    // Project name is already validated and normalized at frontend (lowercase, kebab-case)
    let fullProjectPath;
    if (path.isAbsolute(projectPath)) {
      // If absolute path, create project folder inside the stack folder
      fullProjectPath = path.join(projectPath, projectName);
    } else {
      // If relative path, join with user home directory
      const normalizedPath = projectPath.replace(/\//g, path.sep);
      fullProjectPath = path.join(userHomeDir, normalizedPath, projectName);
    }

    // The project directory is the parent of the project folder
    const projectDir = fullProjectPath;

    console.log(`[PROJECT] 📁 Creating project: "${projectName}" at: ${fullProjectPath}`);

    // Create project directory
    if (!fs.existsSync(projectDir)) {
      fs.mkdirSync(projectDir, { recursive: true });
    }

    // Generate and execute real commands - pass the parent directory for command execution
    const parentDir = path.dirname(projectDir);
    const commands = generateStackCommands(stackName, projectName, parentDir);

    // Execute commands sequentially with real-time progress
    for (let i = 0; i < commands.length; i++) {
      const command = commands[i];
      console.log(`Executing: ${command}`);

      // Send progress update to dashboard
      dashboardWindow.webContents.send('command-progress', {
        currentStep: i,
        totalSteps: commands.length,
        command: command,
        status: 'running',
        projectName: projectData.projectName
      });

      const result = await executeCommand(command, parentDir);

      // Send completion update with output
      dashboardWindow.webContents.send('command-progress', {
        currentStep: i,
        totalSteps: commands.length,
        command: command,
        status: 'completed',
        output: result?.output || 'Command completed successfully',
        projectName: projectData.projectName
      });
    }

    // Send starting IDE message
    dashboardWindow.webContents.send('command-progress', {
      currentStep: commands.length,
      totalSteps: commands.length,
      command: `Starting IDE for ${projectData.projectName}...`,
      status: 'starting-ide',
      projectName: projectData.projectName
    });

    // NO DATABASE PROJECT CREATION - We use template projects from database instead
    console.log(`[TEMPLATE] 🎯 Using template project ID: ${templateProjectId} for task loading`);

    // Create tasks.json file using TEMPLATE FOLDER SYSTEM
    console.log(`[TASKS] 🎯 Using template folder system for task loading`);
    console.log(`[TASKS] 🆔 Template project ID: ${templateProjectId}`);

    // Determine project type based on templateProjectId (individual project selection)
    let projectType = 'generic'; // Default to generic
    let projectDescription = '';

    console.log(`[TASKS] 🔍 Processing project creation:`);
    console.log(`[TASKS] 📁 Stack Name: ${stackName}`);
    console.log(`[TASKS] 🆔 Template Project ID: ${templateProjectId}`);
    console.log(`[TASKS] 📝 Project Name: ${projectName}`);

    if (stackName.toLowerCase() === 'bash') {
      console.log(`[TASKS] 🐚 Processing BASH stack project`);

      // Map database bash project IDs to task folder names
      // Based on the actual 3 projects in the database (as shown in logs)
      // We need to map these to the available task folders we have
      const bashProjectMap = {
        // Database project IDs (these are the actual IDs from the database)
        // We'll map them to our 3 main task folders
        1: {
          type: 'file-organizer',
          description: 'Automated file organization and management system that sorts files by type, date, and custom rules',
          folderPath: 'ViceIde/tasks/bash/file-organizer'
        },
        2: {
          type: 'system-monitor',
          description: 'Real-time system monitoring tool that tracks CPU, memory, disk usage, and network activity',
          folderPath: 'ViceIde/tasks/bash/system-monitor'
        },
        3: {
          type: 'backup-utility',
          description: 'Automated backup and restore utility with compression, encryption, and scheduling features',
          folderPath: 'ViceIde/tasks/bash/backup-utility'
        }
        // Note: Only 3 projects exist in database currently
        // If more projects are added later, extend this mapping
      };

      console.log(`[TASKS] 📋 Available bash project mappings:`);
      Object.keys(bashProjectMap).forEach(id => {
        console.log(`[TASKS]   ${id}: ${bashProjectMap[id].type} -> ${bashProjectMap[id].folderPath}`);
      });

      console.log(`[TASKS] 🔍 Looking for templateProjectId: ${templateProjectId} (type: ${typeof templateProjectId})`);
      console.log(`[TASKS] 🔍 Available mapping keys: ${Object.keys(bashProjectMap).join(', ')}`);

      if (templateProjectId && bashProjectMap[templateProjectId]) {
        projectType = bashProjectMap[templateProjectId].type;
        projectDescription = bashProjectMap[templateProjectId].description;
        console.log(`[TASKS] ✅ MATCHED bash project:`);
        console.log(`[TASKS]   🆔 Project ID: ${templateProjectId}`);
        console.log(`[TASKS]   📂 Task Folder: ${bashProjectMap[templateProjectId].type}`);
        console.log(`[TASKS]   📄 Task File: ${bashProjectMap[templateProjectId].folderPath}/tasks.json`);
        console.log(`[TASKS]   📝 Description: ${projectDescription}`);
      } else {
        console.log(`[TASKS] ⚠️ No specific bash project mapping found for ID: ${templateProjectId}`);
        console.log(`[TASKS] 🔄 Falling back to name-based analysis...`);
        // Fallback to project name analysis for generic project creation
        const projectNameLower = projectName.toLowerCase();
        console.log(`[TASKS] 🔍 Analyzing project name: "${projectNameLower}"`);

        if (projectNameLower.includes('monitor') || projectNameLower.includes('system')) {
          projectType = 'system-monitor';
          projectDescription = 'System monitoring and performance tracking tool';
          console.log(`[TASKS] 🎯 Name analysis result: SYSTEM MONITOR (keywords: monitor/system)`);
        } else if (projectNameLower.includes('backup') || projectNameLower.includes('archive')) {
          projectType = 'backup-utility';
          projectDescription = 'Backup and restore utility for data protection';
          console.log(`[TASKS] 🎯 Name analysis result: BACKUP UTILITY (keywords: backup/archive)`);
        } else if (projectNameLower.includes('log') || projectNameLower.includes('analyz')) {
          projectType = 'log-analyzer';
          projectDescription = 'Log file analysis and reporting system';
          console.log(`[TASKS] 🎯 Name analysis result: LOG ANALYZER (keywords: log/analyz)`);
        } else if (projectNameLower.includes('organiz') || projectNameLower.includes('file')) {
          projectType = 'file-organizer';
          projectDescription = 'File organization and management system';
          console.log(`[TASKS] 🎯 Name analysis result: FILE ORGANIZER (keywords: organiz/file)`);
        } else {
          // Random assignment for truly generic names
          const types = ['file-organizer', 'system-monitor', 'backup-utility', 'log-analyzer'];
          const descriptions = [
            'File organization and management system',
            'System monitoring and performance tracking tool',
            'Backup and restore utility for data protection',
            'Log file analysis and reporting system'
          ];
          const hash = projectName.split('').reduce((a, b) => {
            a = ((a << 5) - a) + b.charCodeAt(0);
            return a & a;
          }, 0);
          const index = Math.abs(hash) % types.length;
          projectType = types[index];
          projectDescription = descriptions[index];
          console.log(`[TASKS] 🎲 Random assignment for generic name: ${projectType} (index: ${index})`);
        }

        console.log(`[TASKS] ✅ Final bash project type: ${projectType}`);
        console.log(`[TASKS] 📝 Final description: ${projectDescription}`);
      }
    } else if (stackName.toLowerCase() === 'mern') {
      // Map MERN project IDs to their specific types
      const mernProjectMap = {
        1: { type: 'todo-app', description: 'Simple todo application with CRUD operations' },
        2: { type: 'blog-platform', description: 'Blog platform with user authentication and comments' },
        3: { type: 'ecommerce-store', description: 'Full-featured e-commerce store with payment integration' },
        4: { type: 'chat-application', description: 'Real-time chat application using Socket.io' },
        5: { type: 'task-manager', description: 'Trello-like task manager with drag and drop' },
        6: { type: 'weather-dashboard', description: 'Weather application with API integration' }
      };

      if (templateProjectId && mernProjectMap[templateProjectId]) {
        projectType = mernProjectMap[templateProjectId].type;
        projectDescription = mernProjectMap[templateProjectId].description;
        console.log(`[TASKS] 🎯 MERN project selected - ID: ${templateProjectId}, Type: ${projectType}`);
      }
    }

    console.log(`[TASKS] 🚀 Creating tasks file with final parameters:`);
    console.log(`[TASKS]   📁 Stack: ${stackName}`);
    console.log(`[TASKS]   📝 Project: ${projectName}`);
    console.log(`[TASKS]   📂 Directory: ${projectDir}`);
    console.log(`[TASKS]   🎯 Type: ${projectType}`);
    console.log(`[TASKS]   📄 Description: ${projectDescription}`);

    await createTasksFileFromTemplate(stackName, projectName, projectDir, projectType, projectDescription);

    // Read actual files from the created directory (including tasks.json)
    const actualFiles = await readProjectFiles(projectDir);

    // Add project to recent projects
    const recentProject = {
      title: projectName,
      tag: stackName,
      path: projectDir,
      stackName: stackName,
      lastOpened: new Date().toISOString()
    };

    // Send recent project data to dashboard for localStorage storage
    if (dashboardWindow && dashboardWindow.webContents) {
      dashboardWindow.webContents.send('add-recent-project', recentProject);
      console.log(`[RECENT] ✅ Added project to recent list: ${projectName}`);
    }

    return {
      success: true,
      message: 'Project created successfully',
      files: actualFiles,
      actualPath: projectDir
    };
  } catch (error) {
    console.error('Error executing stack commands:', error);
    dashboardWindow.webContents.send('command-progress', {
      currentStep: -1,
      totalSteps: 0,
      command: 'Error occurred',
      status: 'error',
      error: error.message
    });
    return { success: false, message: error.message };
  }
});

// Helper function to generate OPTIMIZED real commands
function generateStackCommands(stackName, projectName, projectDir) {
  // projectName is already validated and normalized at frontend (lowercase, kebab-case)
  const projectNameKebab = projectName;

  switch (stackName.toLowerCase()) {
    case 'mern':
    case 'mern stack':
      return [
        `npx create-react-app ${projectNameKebab} --template typescript`,
        `cd ${projectNameKebab} && npm install express mongoose cors dotenv nodemon concurrently --save`,
        `cd ${projectNameKebab} && mkdir server`,
        `cd ${projectNameKebab} && echo "const express = require('express'); const app = express(); app.listen(5000);" > server/index.js`
      ];

    case 'nextjs':
    case 'next.js':
      return [
        `npx create-next-app@latest ${projectNameKebab} --typescript --tailwind --eslint --app --src-dir --import-alias "@/*" --yes`
      ];

    case 'flask':
      return [
        `mkdir ${projectNameKebab}`,
        `cd ${projectNameKebab} && python -m venv venv`,
        `cd ${projectNameKebab} && mkdir templates static`,
        `cd ${projectNameKebab} && echo "from flask import Flask; app = Flask(__name__)" > app.py`
      ];

    case 'django':
      return [
        `mkdir ${projectNameKebab}`,
        `cd ${projectNameKebab} && python -m venv venv`,
        `cd ${projectNameKebab} && django-admin startproject ${projectNameKebab} .`
      ];

    case 'bash':
      // Bash projects use direct file creation instead of commands
      // Return minimal commands to create directory structure
      return [
        `mkdir ${projectNameKebab}`,
        `cd ${projectNameKebab} && mkdir -p config logs temp`,
        `cd ${projectNameKebab} && echo "# ${projectName} project created successfully!" > README.md`
      ];

    default:
      return [
        `mkdir ${projectNameKebab}`,
        `cd ${projectNameKebab} && npm init -y`,
        `cd ${projectNameKebab} && echo "console.log('Hello ${projectName}!');" > index.js`
      ];
  }
}

// Helper function to create project files directly (MUCH FASTER)
function createProjectFilesDirectly(stackName, projectName, projectPath) {
  const files = [];

  switch (stackName.toLowerCase()) {
    case 'mern':
    case 'mern stack':
      files.push(
        { name: 'package.json', content: `{
  "name": "${projectName.toLowerCase().replace(/\s+/g, '-')}",
  "version": "1.0.0",
  "scripts": {
    "start": "node server/index.js",
    "dev": "concurrently \\"npm run server\\" \\"npm run client\\"",
    "server": "nodemon server/index.js",
    "client": "cd client && npm start"
  },
  "dependencies": {
    "express": "^4.18.2",
    "mongoose": "^7.5.0",
    "cors": "^2.8.5"
  }
}` },
        { name: 'server/index.js', content: `const express = require('express');
const app = express();
const PORT = process.env.PORT || 5000;

app.get('/', (req, res) => {
  res.json({ message: 'Welcome to ${projectName} API!' });
});

app.listen(PORT, () => {
  console.log(\`Server running on port \${PORT}\`);
});` },
        { name: 'src/App.js', content: `import React from 'react';

function App() {
  return (
    <div className="App">
      <h1>Welcome to ${projectName}</h1>
      <p>MERN Stack Application</p>
    </div>
  );
}

export default App;` },
        { name: 'README.md', content: `# ${projectName}

MERN Stack Application

## Setup
1. npm install
2. npm run dev` }
      );
      break;

    case 'nextjs':
    case 'next.js':
      files.push(
        { name: 'package.json', content: `{
  "name": "${projectName.toLowerCase().replace(/\s+/g, '-')}",
  "version": "0.1.0",
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start"
  },
  "dependencies": {
    "next": "14.0.0",
    "react": "^18",
    "react-dom": "^18"
  }
}` },
        { name: 'app/page.tsx', content: `export default function Home() {
  return (
    <main>
      <h1>Welcome to ${projectName}</h1>
      <p>Next.js Application</p>
    </main>
  );
}` },
        { name: 'app/layout.tsx', content: `export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}` },
        { name: 'README.md', content: `# ${projectName}

Next.js Application

## Setup
1. npm install
2. npm run dev` }
      );
      break;

    case 'flask':
      files.push(
        { name: 'app.py', content: `from flask import Flask, render_template

app = Flask(__name__)

@app.route('/')
def home():
    return render_template('index.html', title='${projectName}')

if __name__ == '__main__':
    app.run(debug=True)` },
        { name: 'requirements.txt', content: `Flask==2.3.3
Flask-CORS==4.0.0` },
        { name: 'templates/index.html', content: `<!DOCTYPE html>
<html>
<head>
    <title>{{ title }}</title>
</head>
<body>
    <h1>Welcome to ${projectName}</h1>
    <p>Flask Application</p>
</body>
</html>` },
        { name: 'README.md', content: `# ${projectName}

Flask Application

## Setup
1. pip install -r requirements.txt
2. python app.py` }
      );
      break;

    case 'bash':
      // Create project-specific bash files based on the actual bash project types
      // We have: file-organizer, system-monitor, backup-utility, log-analyzer
      const projectNameLower = projectName.toLowerCase();

      if (projectNameLower.includes('file') || projectNameLower.includes('organiz')) {
        // FILE ORGANIZER PROJECT
        files.push(
          { name: 'file-organizer.sh', content: `#!/bin/bash

# File Organizer Script: ${projectName}
# Description: Automated file organization and management system

set -euo pipefail

# Script metadata
SCRIPT_NAME="file-organizer"
SCRIPT_VERSION="1.0.0"
SCRIPT_DIR="$(cd "$(dirname "\${BASH_SOURCE[0]}")" && pwd)"

# Configuration variables
SOURCE_DIR="$HOME/Downloads"
TARGET_BASE_DIR="$HOME/OrganizedFiles"
LOG_FILE="/tmp/\${SCRIPT_NAME}.log"
CONFIG_FILE="$HOME/.config/\${SCRIPT_NAME}/config.conf"

# File type mappings
declare -A FILE_TYPES=(
    ["images"]="jpg jpeg png gif bmp svg webp"
    ["documents"]="pdf doc docx txt rtf odt"
    ["videos"]="mp4 avi mkv mov wmv flv webm"
    ["audio"]="mp3 wav flac aac ogg m4a"
    ["archives"]="zip rar 7z tar gz bz2 xz"
    ["code"]="js py java cpp c html css php rb go rs"
)

echo "File Organizer v\${SCRIPT_VERSION} initialized!"

# Logging functions
log_info() {
    echo "[\$(date '+%Y-%m-%d %H:%M:%S')] INFO: \$1" | tee -a "\$LOG_FILE"
}

log_error() {
    echo "[\$(date '+%Y-%m-%d %H:%M:%S')] ERROR: \$1" | tee -a "\$LOG_FILE" >&2
}

# Main function placeholder
main() {
    log_info "File Organizer started"
    # Add your file organization logic here
    log_info "File Organizer completed"
}

# Run main function if script is executed directly
if [[ "\${BASH_SOURCE[0]}" == "\${0}" ]]; then
    main "\$@"
fi` },
          { name: 'config/settings.conf', content: `# File Organizer Configuration
SOURCE_DIR=$HOME/Downloads
TARGET_BASE_DIR=$HOME/OrganizedFiles
LOG_LEVEL=INFO
DRY_RUN=false
BACKUP_BEFORE_MOVE=true` },
          { name: 'README.md', content: `# ${projectName}

Automated file organization and management system that sorts files by type, date, and custom rules.

## Features
- Automatic file type detection
- Configurable organization rules
- Logging and error handling
- Dry run mode
- Backup functionality

## Usage
\`\`\`bash
chmod +x file-organizer.sh
./file-organizer.sh
\`\`\`

## Configuration
Edit \`config/settings.conf\` to customize:
- Source directory
- Target directories
- File type mappings
- Logging preferences` }
        );
      } else if (projectNameLower.includes('system') || projectNameLower.includes('monitor')) {
        // SYSTEM MONITOR PROJECT
        files.push(
          { name: 'system-monitor.sh', content: `#!/bin/bash

# System Monitor Script: ${projectName}
# Description: Real-time system monitoring and alerting tool

set -euo pipefail

# Script metadata
SCRIPT_NAME="system-monitor"
SCRIPT_VERSION="1.0.0"
MONITOR_INTERVAL=5
ALERT_THRESHOLD_CPU=80
ALERT_THRESHOLD_MEMORY=85
ALERT_THRESHOLD_DISK=90

echo "System Monitor v\${SCRIPT_VERSION} starting..."

# Configuration
CONFIG_FILE="$HOME/.config/system-monitor/config.conf"
LOG_FILE="/tmp/system-monitor.log"
DATA_LOG_FILE="/tmp/system-monitor-data.log"

# Monitor CPU usage
monitor_cpu() {
    local cpu_usage=\$(top -bn1 | grep "Cpu(s)" | awk '{print \$2}' | cut -d'%' -f1)
    cpu_usage=\${cpu_usage%.*}  # Remove decimal part

    echo "CPU Usage: \${cpu_usage}%"

    if [[ \$cpu_usage -gt \$ALERT_THRESHOLD_CPU ]]; then
        send_alert "HIGH CPU USAGE" "CPU usage is \${cpu_usage}% (threshold: \${ALERT_THRESHOLD_CPU}%)"
    fi

    echo \$cpu_usage
}

# Monitor memory usage
monitor_memory() {
    local mem_info=\$(free | grep Mem:)
    local total=\$(echo \$mem_info | awk '{print \$2}')
    local used=\$(echo \$mem_info | awk '{print \$3}')
    local available=\$(echo \$mem_info | awk '{print \$7}')

    local mem_usage=\$((used * 100 / total))

    echo "Memory Usage: \${mem_usage}% (\${used}/\${total})"
    echo "Available Memory: \${available}"

    if [[ \$mem_usage -gt \$ALERT_THRESHOLD_MEMORY ]]; then
        send_alert "HIGH MEMORY USAGE" "Memory usage is \${mem_usage}% (threshold: \${ALERT_THRESHOLD_MEMORY}%)"
    fi

    echo \$mem_usage
}

# Alert system
send_alert() {
    local alert_type="\$1"
    local message="\$2"
    local timestamp=\$(date '+%Y-%m-%d %H:%M:%S')

    echo "[\$timestamp] ALERT: \$alert_type - \$message" >> "\$LOG_FILE"
    echo "🚨 ALERT: \$alert_type - \$message" >&2
}

# Main monitoring function
main() {
    echo "Starting system monitoring..."
    while true; do
        clear
        echo "System Monitor Dashboard - \$(date)"
        echo "================================"
        monitor_cpu
        monitor_memory
        echo "Press Ctrl+C to exit"
        sleep \$MONITOR_INTERVAL
    done
}

# Run main function
main "\$@"` },
          { name: 'config/monitor.conf', content: `# System Monitor Configuration
MONITOR_INTERVAL=5
ALERT_THRESHOLD_CPU=80
ALERT_THRESHOLD_MEMORY=85
ALERT_THRESHOLD_DISK=90
EMAIL_RECIPIENT=""
ENABLE_DESKTOP_NOTIFICATIONS=true` },
          { name: 'README.md', content: `# ${projectName}

Real-time system monitoring tool that tracks CPU, memory, disk usage, and network activity with alerting capabilities.

## Features
- Real-time CPU monitoring
- Memory usage tracking
- Disk space monitoring
- Process monitoring
- Alert system
- Interactive dashboard

## Usage
\`\`\`bash
chmod +x system-monitor.sh
./system-monitor.sh
\`\`\`

## Configuration
Edit \`config/monitor.conf\` to customize:
- Monitoring intervals
- Alert thresholds
- Notification settings` }
        );
      } else if (projectNameLower.includes('backup') || projectNameLower.includes('utility')) {
        // BACKUP UTILITY PROJECT
        files.push(
          { name: 'backup-utility.sh', content: `#!/bin/bash

# Backup Utility Script: ${projectName}
# Description: Automated backup and restore utility with compression and encryption

set -euo pipefail

# Script metadata
SCRIPT_NAME="backup-utility"
SCRIPT_VERSION="1.0.0"
SCRIPT_DIR="$(cd "$(dirname "\${BASH_SOURCE[0]}")" && pwd)"
BACKUP_BASE_DIR="$HOME/Backups"
CONFIG_DIR="$HOME/.config/\$SCRIPT_NAME"

echo "Backup Utility v\${SCRIPT_VERSION} initialized!"

# Configuration variables
CONFIG_FILE="\$CONFIG_DIR/backup.conf"
EXCLUDE_FILE="\$CONFIG_DIR/exclude.list"
LOG_FILE="\$CONFIG_DIR/backup.log"
ENCRYPTION_KEY_FILE="\$CONFIG_DIR/backup.key"

# Default settings
COMPRESSION_LEVEL=6
ENCRYPTION_ENABLED=false
RETENTION_DAYS=30
BACKUP_FORMAT="tar.gz"
TIMESTAMP_FORMAT="%Y%m%d_%H%M%S"

# Logging functions
log_info() {
    echo "[\$(date '+%Y-%m-%d %H:%M:%S')] INFO: \$1" | tee -a "\$LOG_FILE"
}

log_error() {
    echo "[\$(date '+%Y-%m-%d %H:%M:%S')] ERROR: \$1" | tee -a "\$LOG_FILE" >&2
}

log_success() {
    echo "[\$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: \$1" | tee -a "\$LOG_FILE"
}

# Validate backup sources
validate_sources() {
    local sources=("\$@")
    local valid_sources=()

    for source in "\${sources[@]}"; do
        if [[ ! -e "\$source" ]]; then
            log_error "Source does not exist: \$source"
            continue
        fi

        if [[ ! -r "\$source" ]]; then
            log_error "No read permission for: \$source"
            continue
        fi

        local size=\$(du -sh "\$source" 2>/dev/null | cut -f1 || echo "unknown")
        log_info "Valid source: \$source (size: \$size)"
        valid_sources+=("\$source")
    done

    echo "\${valid_sources[@]}"
}

# Main backup function
perform_backup() {
    local backup_name="\$1"
    shift
    local sources=("\$@")

    log_info "Starting backup: \$backup_name"

    # Validate sources
    local valid_sources
    if ! valid_sources=(\$(validate_sources "\${sources[@]}")) ; then
        return 1
    fi

    log_success "Backup validation completed"
}

# Main CLI function
main() {
    case "\${1:-help}" in
        "backup")
            shift
            local backup_name="\$1"
            shift
            perform_backup "\$backup_name" "\$@"
            ;;
        "help")
            echo "Backup Utility v\$SCRIPT_VERSION"
            echo "Usage: \$0 COMMAND [OPTIONS]"
            echo ""
            echo "COMMANDS:"
            echo "    backup NAME SOURCE...     Create backup of sources"
            echo "    help                     Show this help"
            ;;
        *)
            echo "Unknown command: \$1"
            echo "Use '\$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function
main "\$@"` },
          { name: 'config/backup.conf', content: `# Backup Utility Configuration
BACKUP_BASE_DIR=$HOME/Backups
RETENTION_DAYS=30
COMPRESSION=true
ENCRYPTION_ENABLED=false
SOURCE_DIRS="$HOME/Documents $HOME/Projects"` },
          { name: 'README.md', content: `# ${projectName}

Automated backup and restore utility with compression, encryption, and scheduling features for system administrators and developers.

## Features
- Automated backup creation
- Compression support
- Encryption capabilities
- Retention management
- Restore functionality
- Scheduling integration

## Usage
\`\`\`bash
chmod +x backup-utility.sh
./backup-utility.sh backup documents ~/Documents ~/Pictures
\`\`\`

## Configuration
Edit \`config/backup.conf\` to customize:
- Backup directories
- Retention policies
- Compression settings
- Encryption options` }
        );
      } else if (projectNameLower.includes('log') || projectNameLower.includes('analyz')) {
        // LOG ANALYZER PROJECT
        files.push(
          { name: 'log-analyzer.sh', content: `#!/bin/bash

# Log Analyzer Script: ${projectName}
# Description: Advanced log file analysis and reporting tool with pattern recognition

set -euo pipefail

# Script metadata
SCRIPT_NAME="log-analyzer"
SCRIPT_VERSION="1.0.0"
SCRIPT_DIR="$(cd "$(dirname "\${BASH_SOURCE[0]}")" && pwd)"

echo "Log Analyzer v\${SCRIPT_VERSION} initialized!"

# Configuration variables
CONFIG_FILE="$HOME/.config/\$SCRIPT_NAME/analyzer.conf"
LOG_FILE="/tmp/\$SCRIPT_NAME.log"
REPORT_DIR="$HOME/log-reports"
PATTERNS_FILE="\$SCRIPT_DIR/config/patterns.conf"

# Default settings
MAX_LOG_SIZE="100M"
ANALYSIS_DEPTH=1000
ALERT_THRESHOLD=10
REPORT_FORMAT="html"

# Logging functions
log_info() {
    echo "[\$(date '+%Y-%m-%d %H:%M:%S')] INFO: \$1" | tee -a "\$LOG_FILE"
}

log_error() {
    echo "[\$(date '+%Y-%m-%d %H:%M:%S')] ERROR: \$1" | tee -a "\$LOG_FILE" >&2
}

log_success() {
    echo "[\$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: \$1" | tee -a "\$LOG_FILE"
}

# Analyze log file
analyze_log() {
    local log_file="\$1"
    local output_file="\${2:-analysis_report.txt}"

    log_info "Starting analysis of: \$log_file"

    if [[ ! -f "\$log_file" ]]; then
        log_error "Log file not found: \$log_file"
        return 1
    fi

    # Basic log statistics
    local total_lines=\$(wc -l < "\$log_file")
    local error_count=\$(grep -ci "error" "\$log_file" || echo 0)
    local warning_count=\$(grep -ci "warning" "\$log_file" || echo 0)

    echo "Log Analysis Report" > "\$output_file"
    echo "===================" >> "\$output_file"
    echo "File: \$log_file" >> "\$output_file"
    echo "Total Lines: \$total_lines" >> "\$output_file"
    echo "Errors: \$error_count" >> "\$output_file"
    echo "Warnings: \$warning_count" >> "\$output_file"
    echo "Analysis Date: \$(date)" >> "\$output_file"

    log_success "Analysis completed: \$output_file"
}

# Pattern detection
detect_patterns() {
    local log_file="\$1"

    log_info "Detecting patterns in: \$log_file"

    # Common error patterns
    echo "Pattern Detection Results:"
    echo "========================="

    # Failed login attempts
    local failed_logins=\$(grep -ci "failed.*login\\|authentication.*failed" "\$log_file" || echo 0)
    echo "Failed Login Attempts: \$failed_logins"

    # Connection errors
    local conn_errors=\$(grep -ci "connection.*refused\\|connection.*timeout" "\$log_file" || echo 0)
    echo "Connection Errors: \$conn_errors"

    # Memory issues
    local memory_issues=\$(grep -ci "out of memory\\|memory.*error" "\$log_file" || echo 0)
    echo "Memory Issues: \$memory_issues"
}

# Generate report
generate_report() {
    local log_file="\$1"
    local report_name="\${2:-log_analysis_\$(date +%Y%m%d_%H%M%S)}"

    mkdir -p "\$REPORT_DIR"
    local report_file="\$REPORT_DIR/\${report_name}.txt"

    log_info "Generating comprehensive report..."

    analyze_log "\$log_file" "\$report_file"
    detect_patterns "\$log_file" >> "\$report_file"

    log_success "Report generated: \$report_file"
}

# Main CLI function
main() {
    case "\${1:-help}" in
        "analyze")
            analyze_log "\$2" "\${3:-analysis_report.txt}"
            ;;
        "patterns")
            detect_patterns "\$2"
            ;;
        "report")
            generate_report "\$2" "\$3"
            ;;
        "help")
            echo "Log Analyzer v\$SCRIPT_VERSION"
            echo "Usage: \$0 COMMAND [OPTIONS]"
            echo ""
            echo "COMMANDS:"
            echo "    analyze FILE [OUTPUT]    Analyze log file"
            echo "    patterns FILE           Detect patterns in log"
            echo "    report FILE [NAME]      Generate comprehensive report"
            echo "    help                    Show this help"
            ;;
        *)
            echo "Unknown command: \$1"
            echo "Use '\$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function
main "\$@"` },
          { name: 'config/patterns.conf', content: `# Log Analysis Patterns
ERROR_PATTERNS="error|ERROR|Error|failed|FAILED|Failed"
WARNING_PATTERNS="warning|WARNING|Warning|warn|WARN"
SECURITY_PATTERNS="unauthorized|forbidden|denied|attack|intrusion"
PERFORMANCE_PATTERNS="slow|timeout|performance|bottleneck"` },
          { name: 'config/analyzer.conf', content: `# Log Analyzer Configuration
MAX_LOG_SIZE=100M
ANALYSIS_DEPTH=1000
ALERT_THRESHOLD=10
REPORT_FORMAT=html
REPORT_DIR=$HOME/log-reports` },
          { name: 'README.md', content: `# ${projectName}

Advanced log file analysis and reporting tool with pattern recognition, alerting, and statistical analysis capabilities for system administrators and developers.

## Features
- Log file analysis
- Pattern recognition
- Error detection
- Statistical reporting
- Alert generation
- Multiple output formats

## Usage
\`\`\`bash
chmod +x log-analyzer.sh
./log-analyzer.sh analyze /var/log/syslog
./log-analyzer.sh patterns /var/log/apache2/error.log
./log-analyzer.sh report /var/log/application.log
\`\`\`

## Configuration
Edit \`config/analyzer.conf\` to customize:
- Analysis parameters
- Pattern definitions
- Report settings
- Alert thresholds` }
        );
      } else {
        // General bash project (fallback for any other bash project names)
        files.push(
          { name: 'main.sh', content: `#!/bin/bash

# Script: ${projectName}
# Description: General purpose bash utility script

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# Script metadata
SCRIPT_NAME="${projectName}"
SCRIPT_VERSION="1.0.0"
SCRIPT_DIR="$(cd "$(dirname "\${BASH_SOURCE[0]}")" && pwd)"

# Configuration
LOG_FILE="\${SCRIPT_DIR}/logs/app.log"
CONFIG_FILE="\${SCRIPT_DIR}/config/settings.conf"

# Create directories if they don't exist
mkdir -p "\${SCRIPT_DIR}/logs" "\${SCRIPT_DIR}/config" "\${SCRIPT_DIR}/temp"

# Logging functions
log_info() {
    echo "[\$(date '+%Y-%m-%d %H:%M:%S')] INFO: \$1" | tee -a "\$LOG_FILE"
}

log_error() {
    echo "[\$(date '+%Y-%m-%d %H:%M:%S')] ERROR: \$1" | tee -a "\$LOG_FILE" >&2
}

log_success() {
    echo "[\$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: \$1" | tee -a "\$LOG_FILE"
}

# Error handling
error_exit() {
    log_error "\$1"
    exit 1
}

# Main function
main() {
    log_info "Starting \$SCRIPT_NAME v\$SCRIPT_VERSION"

    # Your script logic here
    echo "Hello from \${SCRIPT_NAME}!"

    log_success "Script completed successfully"
}

# Show help
show_help() {
    cat << EOF
\${SCRIPT_NAME} v\${SCRIPT_VERSION}

Usage: \$0 [OPTIONS]

OPTIONS:
    -h, --help      Show this help message
    -v, --version   Show version information

EXAMPLES:
    \$0              # Run the script
    \$0 --help       # Show help
EOF
}

# Parse command line arguments
while [[ \$# -gt 0 ]]; do
    case \$1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--version)
            echo "\${SCRIPT_NAME} v\${SCRIPT_VERSION}"
            exit 0
            ;;
        *)
            echo "Unknown option: \$1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main "\$@"` },
          { name: 'config/settings.conf', content: `# Configuration for ${projectName}
APP_NAME=${projectName}
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=INFO
MAX_RETRIES=3
TIMEOUT=30` },
          { name: 'README.md', content: `# ${projectName}

General purpose bash utility script with professional structure and features.

## Features
- Professional script structure
- Comprehensive logging system
- Error handling and validation
- Configuration management
- Command line argument parsing
- Help system

## Usage
\`\`\`bash
chmod +x main.sh
./main.sh
\`\`\`

## Project Structure
\`\`\`
${projectName}/
├── main.sh              # Main script file
├── config/
│   └── settings.conf    # Configuration file
├── logs/
│   └── app.log         # Application logs
├── temp/               # Temporary files
└── README.md           # This file
\`\`\`

## Configuration
Edit \`config/settings.conf\` to customize:
- Application settings
- Logging preferences
- Timeout values
- Debug options` }
        );
      }
      break;

    default:
      files.push(
        { name: 'README.md', content: `# ${projectName}

Project created successfully!` },
        { name: 'index.js', content: `// ${projectName}
console.log('Hello World!');` }
      );
  }

  return files;
}

// Helper function to execute a command with optimizations
function executeCommand(command, workingDir) {
  return new Promise((resolve, reject) => {
    const { exec } = require('child_process');

    // Set environment variables for faster execution
    const env = {
      ...process.env,
      SKIP_PREFLIGHT_CHECK: 'true',
      GENERATE_SOURCEMAP: 'false',
      CI: 'true', // Makes npm installs faster
      npm_config_audit: 'false',
      npm_config_fund: 'false',
      npm_config_optional: 'false',
      npm_config_progress: 'false',
      npm_config_loglevel: 'error'
    };

    const options = {
      cwd: workingDir,
      env: env,
      timeout: 600000, // 10 minutes timeout
      maxBuffer: 1024 * 1024 * 10 // 10MB buffer
    };

    exec(command, options, (error, stdout, stderr) => {
      if (error) {
        console.error(`Command failed: ${command}`, error.message);
        // Don't reject for non-critical errors, just log and continue
        resolve();
      } else {
        console.log(`Command completed: ${command}`);
        resolve();
      }
    });
  });
}

// Function to load bash project tasks from ACTUAL Prisma database using project ID
async function loadBashProjectTasksFromDatabase(projectId) {
  console.log(`[TASKS] 🔍 loadBashProjectTasksFromDatabase called with projectId: "${projectId}"`);

  try {
    // Import Prisma client from the correct path
    const { PrismaClient } = require(path.join(__dirname, '..', 'node_modules', '@prisma/client'));
    const prisma = new PrismaClient({
      datasources: {
        db: {
          url: `file:${path.join(__dirname, '..', 'prisma', 'dev.db')}`
        }
      }
    });

    console.log(`[TASKS] 🔍 Connecting to Prisma database...`);
    console.log(`[TASKS] 🔍 Database path: ${path.join(__dirname, '..', 'prisma', 'dev.db')}`);

    // Find the project by ID
    const project = await prisma.stackProject.findUnique({
      where: { id: parseInt(projectId) },
      include: {
        tasks: {
          orderBy: { order: 'asc' }
        }
      }
    });

    if (!project) {
      console.log(`[TASKS] ❌ No project found with ID: ${projectId}`);
      await prisma.$disconnect();
      return [];
    }

    console.log(`[TASKS] ✅ Found project: ${project.name}`);
    console.log(`[TASKS] 📋 Tasks count: ${project.tasks.length}`);

    // Convert Prisma tasks to the expected format
    const formattedTasks = project.tasks.map(task => ({
      id: `task-${task.id}`,
      title: task.title,
      description: task.description,
      instructions: task.instructions,
      code_snippet: task.validationCriteria, // Use validation criteria as code snippet
      expected_output: `Task should be completed according to: ${task.validationCriteria}`,
      category: task.category,
      priority: task.priority.toLowerCase(),
      order: task.order
    }));

    await prisma.$disconnect();
    return formattedTasks;
  } catch (error) {
    console.error(`[TASKS] ❌ Error loading tasks from Prisma database:`, error);
    return [];
  }
}

// REMOVED: Old task creation system - now using database only

// Function to generate comprehensive tasks based on stack and project name
function generateComprehensiveTasks(stackName, projectName) {
  const projectNameLower = projectName.toLowerCase();

  if (stackName.toLowerCase() === 'bash') {
    if (projectNameLower.includes('file') || projectNameLower.includes('organiz') || projectNameLower.includes('manager')) {
      // FILE ORGANIZER PROJECT - 12 comprehensive tasks
      return [
        {
          id: "file-org-1",
          title: "Create Project Structure",
          description: "Set up the basic directory structure and main script file",
          instructions: "Create main script file with proper shebang and basic structure",
          code_snippet: "#!/bin/bash\n\n# File Organizer Script\n# Author: Your Name\n# Description: Organize files by type and date\n\nset -euo pipefail\n\necho 'File Organizer initialized!'",
          expected_output: "Script should run without errors and display initialization message",
          category: "Setup",
          priority: "high",
          order: 1
        },
        {
          id: "file-org-2",
          title: "Add Configuration Variables",
          description: "Define configuration variables for source and destination directories",
          instructions: "Add variables for source directory, destination directory, and file types",
          code_snippet: "# Configuration variables\nSOURCE_DIR=\"$HOME/Downloads\"\nDEST_DIR=\"$HOME/OrganizedFiles\"\nLOG_FILE=\"$HOME/file_organizer.log\"\n\n# File type categories\nIMAGES=(\"jpg\" \"jpeg\" \"png\" \"gif\" \"bmp\")\nDOCUMENTS=(\"pdf\" \"doc\" \"docx\" \"txt\" \"rtf\")\nVIDEOS=(\"mp4\" \"avi\" \"mkv\" \"mov\" \"wmv\")",
          expected_output: "Variables should be properly defined and accessible",
          category: "Configuration",
          priority: "high",
          order: 2
        },
        {
          id: "file-org-3",
          title: "Create Logging Function",
          description: "Implement logging functionality to track file operations",
          instructions: "Create a function to log messages with timestamps",
          code_snippet: "log_message() {\n    local message=\"$1\"\n    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')\n    echo \"[$timestamp] $message\" | tee -a \"$LOG_FILE\"\n}",
          expected_output: "Logging function should write timestamped messages to log file",
          category: "Utilities",
          priority: "medium",
          order: 3
        },
        {
          id: "file-org-4",
          title: "Create Directory Structure",
          description: "Function to create organized directory structure",
          instructions: "Create function to make directories for different file types",
          code_snippet: "create_directories() {\n    log_message \"Creating directory structure...\"\n    \n    mkdir -p \"$DEST_DIR/Images\"\n    mkdir -p \"$DEST_DIR/Documents\"\n    mkdir -p \"$DEST_DIR/Videos\"\n    mkdir -p \"$DEST_DIR/Others\"\n    \n    log_message \"Directory structure created successfully\"\n}",
          expected_output: "Directories should be created in the destination folder",
          category: "Setup",
          priority: "medium",
          order: 4
        },
        {
          id: "file-org-5",
          title: "File Type Detection",
          description: "Function to detect file type based on extension",
          instructions: "Create function to categorize files by their extensions",
          code_snippet: "get_file_category() {\n    local file=\"$1\"\n    local extension=\"${file##*.}\"\n    extension=$(echo \"$extension\" | tr '[:upper:]' '[:lower:]')\n    \n    if [[ \" ${IMAGES[@]} \" =~ \" $extension \" ]]; then\n        echo \"Images\"\n    elif [[ \" ${DOCUMENTS[@]} \" =~ \" $extension \" ]]; then\n        echo \"Documents\"\n    elif [[ \" ${VIDEOS[@]} \" =~ \" $extension \" ]]; then\n        echo \"Videos\"\n    else\n        echo \"Others\"\n    fi\n}",
          expected_output: "Function should return correct category for different file types",
          category: "Logic",
          priority: "high",
          order: 5
        },
        {
          id: "file-org-6",
          title: "File Moving Function",
          description: "Function to move files to appropriate directories",
          instructions: "Create function to safely move files with conflict resolution",
          code_snippet: "move_file() {\n    local source_file=\"$1\"\n    local category=\"$2\"\n    local filename=$(basename \"$source_file\")\n    local dest_path=\"$DEST_DIR/$category/$filename\"\n    \n    if [[ -f \"$dest_path\" ]]; then\n        local timestamp=$(date '+%Y%m%d_%H%M%S')\n        dest_path=\"$DEST_DIR/$category/${filename%.*}_$timestamp.${filename##*.}\"\n    fi\n    \n    mv \"$source_file\" \"$dest_path\"\n    log_message \"Moved: $filename -> $category/\"\n}",
          expected_output: "Files should be moved to correct directories with conflict resolution",
          category: "Logic",
          priority: "high",
          order: 6
        },
        {
          id: "file-org-7",
          title: "Main Processing Loop",
          description: "Main loop to process all files in source directory",
          instructions: "Create main loop to iterate through files and organize them",
          code_snippet: "organize_files() {\n    log_message \"Starting file organization...\"\n    \n    if [[ ! -d \"$SOURCE_DIR\" ]]; then\n        log_message \"Error: Source directory does not exist: $SOURCE_DIR\"\n        exit 1\n    fi\n    \n    local file_count=0\n    \n    for file in \"$SOURCE_DIR\"/*; do\n        if [[ -f \"$file\" ]]; then\n            local category=$(get_file_category \"$file\")\n            move_file \"$file\" \"$category\"\n            ((file_count++))\n        fi\n    done\n    \n    log_message \"Organization complete. Processed $file_count files.\"\n}",
          expected_output: "All files in source directory should be organized by type",
          category: "Main Logic",
          priority: "high",
          order: 7
        },
        {
          id: "file-org-8",
          title: "Add Command Line Arguments",
          description: "Support for command line arguments and options",
          instructions: "Add support for custom source and destination directories",
          code_snippet: "parse_arguments() {\n    while [[ $# -gt 0 ]]; do\n        case $1 in\n            -s|--source)\n                SOURCE_DIR=\"$2\"\n                shift 2\n                ;;\n            -d|--dest)\n                DEST_DIR=\"$2\"\n                shift 2\n                ;;\n            -h|--help)\n                show_help\n                exit 0\n                ;;\n            *)\n                echo \"Unknown option: $1\"\n                show_help\n                exit 1\n                ;;\n        esac\n    done\n}",
          expected_output: "Script should accept and process command line arguments",
          category: "Interface",
          priority: "medium",
          order: 8
        },
        {
          id: "file-org-9",
          title: "Add Help Function",
          description: "Create help function to display usage information",
          instructions: "Create function to show script usage and options",
          code_snippet: "show_help() {\n    cat << EOF\nFile Organizer Script\n\nUsage: $0 [OPTIONS]\n\nOptions:\n    -s, --source DIR    Source directory (default: $HOME/Downloads)\n    -d, --dest DIR      Destination directory (default: $HOME/OrganizedFiles)\n    -h, --help          Show this help message\n\nExample:\n    $0 -s /path/to/messy/folder -d /path/to/organized/folder\nEOF\n}",
          expected_output: "Help message should display usage information clearly",
          category: "Interface",
          priority: "low",
          order: 9
        },
        {
          id: "file-org-10",
          title: "Add Progress Reporting",
          description: "Show progress during file organization",
          instructions: "Add progress indicators and file count reporting",
          code_snippet: "show_progress() {\n    local current=\"$1\"\n    local total=\"$2\"\n    local percent=$((current * 100 / total))\n    local bar_length=20\n    local filled_length=$((percent * bar_length / 100))\n    \n    printf \"\\rProgress: [\"\n    for ((i=0; i<filled_length; i++)); do printf \"#\"; done\n    for ((i=filled_length; i<bar_length; i++)); do printf \"-\"; done\n    printf \"] %d%% (%d/%d)\" \"$percent\" \"$current\" \"$total\"\n}",
          expected_output: "Progress bar should show current organization status",
          category: "Interface",
          priority: "low",
          order: 10
        },
        {
          id: "file-org-11",
          title: "Add Dry Run Mode",
          description: "Option to preview changes without actually moving files",
          instructions: "Add dry run mode to show what would be organized",
          code_snippet: "DRY_RUN=false\n\nmove_file_dry_run() {\n    local source_file=\"$1\"\n    local category=\"$2\"\n    local filename=$(basename \"$source_file\")\n    \n    if [[ \"$DRY_RUN\" == \"true\" ]]; then\n        log_message \"[DRY RUN] Would move: $filename -> $category/\"\n    else\n        move_file \"$source_file\" \"$category\"\n    fi\n}",
          expected_output: "Dry run should show planned moves without actually moving files",
          category: "Safety",
          priority: "medium",
          order: 11
        },
        {
          id: "file-org-12",
          title: "Main Execution and Error Handling",
          description: "Put it all together with proper error handling",
          instructions: "Create main execution flow with comprehensive error handling",
          code_snippet: "main() {\n    parse_arguments \"$@\"\n    \n    log_message \"File Organizer started\"\n    log_message \"Source: $SOURCE_DIR\"\n    log_message \"Destination: $DEST_DIR\"\n    \n    create_directories\n    organize_files\n    \n    log_message \"File organization completed successfully\"\n}\n\n# Error handling\ntrap 'log_message \"Error occurred. Exiting...\"; exit 1' ERR\n\n# Run main function\nmain \"$@\"",
          expected_output: "Complete file organizer should work end-to-end with error handling",
          category: "Integration",
          priority: "high",
          order: 12
        }
      ];
    } else {
      // GENERIC BASH PROJECT - 12 comprehensive tasks
      return [
        {
          id: "bash-gen-1",
          title: "Create Script Foundation",
          description: "Set up basic bash script structure with error handling",
          instructions: "Create main script file with proper shebang, error handling, and basic structure",
          code_snippet: `#!/bin/bash\n\n# ${projectName} Script\n# Description: ${projectName} automation script\n\nset -euo pipefail\n\necho '${projectName} script initialized!'`,
          expected_output: "Script should run without errors and display initialization message",
          category: "Foundation",
          priority: "high",
          order: 1
        },
        {
          id: "bash-gen-2",
          title: "Add Configuration Variables",
          description: "Define configuration variables and constants",
          instructions: "Add configuration variables for script customization",
          code_snippet: `# Configuration variables\nSCRIPT_NAME="${projectName}"\nSCRIPT_VERSION="1.0.0"\nLOG_FILE="/tmp/\${SCRIPT_NAME}.log"\nCONFIG_DIR="$HOME/.config/\${SCRIPT_NAME}"\n\n# Create config directory if it doesn't exist\nmkdir -p "$CONFIG_DIR"`,
          expected_output: "Configuration variables should be properly defined and directories created",
          category: "Configuration",
          priority: "high",
          order: 2
        }
        // Add more generic tasks here...
      ];
    }
  }

  // Default fallback for other stacks
  return [
    {
      id: "generic-1",
      title: "Project Setup",
      description: "Initialize the project structure and basic configuration",
      instructions: "Set up the basic project structure and configuration files",
      code_snippet: "// Basic project setup\nconsole.log('Project initialized successfully!');",
      expected_output: "Project should be properly initialized",
      category: "Setup",
      priority: "high",
      order: 1
    }
  ];
}

// Database API handlers for IDE
ipcMain.handle('database:getProjectByNameAndPath', async (event, projectName, projectPath) => {
  try {
    const { PrismaClient } = require(path.join(__dirname, '..', 'Vice', 'node_modules', '@prisma/client'));
    const prisma = new PrismaClient({
      datasources: {
        db: {
          url: `file:${path.join(__dirname, '..', 'Vice', 'prisma', 'dev.db')}`
        }
      }
    });

    console.log(`[DATABASE API] 🔍 Looking for project: "${projectName}" at path: "${projectPath}"`);

    const project = await prisma.stackProject.findFirst({
      where: {
        name: projectName,
        path: {
          contains: projectPath
        }
      }
    });

    await prisma.$disconnect();

    if (project) {
      console.log(`[DATABASE API] ✅ Found project with ID: ${project.id}`);
      return { success: true, project };
    } else {
      console.log(`[DATABASE API] ❌ No project found for: "${projectName}"`);
      return { success: false, project: null };
    }
  } catch (error) {
    console.error('[DATABASE API] ❌ Error finding project:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('database:getProjectTasks', async (event, projectId) => {
  try {
    const { PrismaClient } = require(path.join(__dirname, '..', 'Vice', 'node_modules', '@prisma/client'));
    const prisma = new PrismaClient({
      datasources: {
        db: {
          url: `file:${path.join(__dirname, '..', 'Vice', 'prisma', 'dev.db')}`
        }
      }
    });

    console.log(`[DATABASE API] 🔍 Loading tasks from tasksJson column for project ID: ${projectId}`);

    const project = await prisma.stackProject.findUnique({
      where: { id: parseInt(projectId) }
    });

    await prisma.$disconnect();

    if (project && project.tasksJson) {
      try {
        // Parse tasks from tasksJson column
        const tasks = JSON.parse(project.tasksJson);
        console.log(`[DATABASE API] ✅ Loaded ${tasks.length} tasks from tasksJson column`);
        return { success: true, tasks: tasks };
      } catch (parseError) {
        console.error('[DATABASE API] ❌ Error parsing tasksJson:', parseError);
        return { success: false, tasks: [] };
      }
    } else {
      console.log(`[DATABASE API] ❌ No tasksJson found for project ID: ${projectId}`);
      return { success: false, tasks: [] };
    }
  } catch (error) {
    console.error('[DATABASE API] ❌ Error loading tasks:', error);
    return { success: false, error: error.message };
  }
});

// Create tasks.json file from template folder structure
async function createTasksFileFromTemplate(stackName, projectName, projectDir, projectType, projectDescription = '') {
  try {
    console.log(`[TASKS] 🔍 Creating tasks.json for project: ${projectName}`);
    console.log(`[TASKS] 📁 Project path: ${projectDir}`);
    console.log(`[TASKS] 🏗️ Stack: ${stackName}`);
    console.log(`[TASKS] 🎯 Project type: ${projectType}`);
    console.log(`[TASKS] 📝 Project description: ${projectDescription}`);

    // Determine template path based on stack and project type
    const templatePath = path.join(__dirname, '..', 'ViceIde', 'tasks', stackName.toLowerCase(), projectType, 'tasks.json');
    console.log(`[TASKS] 📄 Constructed template path: ${templatePath}`);
    console.log(`[TASKS] 🔍 Checking if template file exists...`);

    let tasksData = {
      projectName: projectName,
      stackName: stackName,
      projectType: projectType,
      createdAt: new Date().toISOString(),
      tasks: []
    };

    // Try to load tasks from template file
    try {
      if (fs.existsSync(templatePath)) {
        console.log(`[TASKS] ✅ Template file EXISTS: ${templatePath}`);
        console.log(`[TASKS] 📖 Reading template file content...`);

        const templateContent = fs.readFileSync(templatePath, 'utf8');
        console.log(`[TASKS] 📄 Template file size: ${templateContent.length} characters`);
        console.log(`[TASKS] 🔍 Parsing JSON content...`);

        const templateData = JSON.parse(templateContent);
        console.log(`[TASKS] 📋 Template data keys: ${Object.keys(templateData).join(', ')}`);

        if (templateData.tasks && Array.isArray(templateData.tasks)) {
          // Use template tasks and update project-specific info
          tasksData = {
            ...templateData,
            projectName: projectName,
            description: projectDescription || templateData.description,
            createdAt: new Date().toISOString()
          };

          console.log(`[TASKS] ✅ Successfully loaded ${templateData.tasks.length} tasks from template`);
          console.log(`[TASKS] 📝 First task title: ${templateData.tasks[0]?.title || 'No title'}`);
          console.log(`[TASKS] 📝 Template original description: ${templateData.description || 'No description'}`);
          console.log(`[TASKS] 📝 Using project description: ${projectDescription || 'Using template description'}`);
          console.log(`[TASKS] 📋 All task titles: ${templateData.tasks.map(t => t.title).slice(0, 3).join(', ')}${templateData.tasks.length > 3 ? '...' : ''}`);
        } else {
          console.log(`[TASKS] ❌ Template file exists but has no valid tasks array`);
          console.log(`[TASKS] 📄 Template data structure: ${JSON.stringify(templateData, null, 2).substring(0, 200)}...`);
        }
      } else {
        console.log(`[TASKS] ❌ Template file NOT FOUND: ${templatePath}`);
        console.log(`[TASKS] 📁 Checking parent directory: ${path.dirname(templatePath)}`);
        try {
          const parentDir = path.dirname(templatePath);
          if (fs.existsSync(parentDir)) {
            const files = fs.readdirSync(parentDir);
            console.log(`[TASKS] 📂 Files in parent directory: ${files.join(', ')}`);
          } else {
            console.log(`[TASKS] ❌ Parent directory does not exist: ${parentDir}`);
          }
        } catch (dirError) {
          console.log(`[TASKS] ❌ Error checking parent directory: ${dirError.message}`);
        }
      }
    } catch (templateError) {
      console.error(`[TASKS] ❌ Error loading template:`, templateError);
      console.error(`[TASKS] ❌ Template error details:`, templateError.stack);
    }

    // Fallback: Generate comprehensive tasks if no template was loaded
    if (tasksData.tasks.length === 0) {
      console.log(`[TASKS] 🔄 No template found, generating comprehensive tasks`);
      tasksData.tasks = generateComprehensiveTasks(stackName, projectName);
      console.log(`[TASKS] ✅ Generated ${tasksData.tasks.length} comprehensive tasks as fallback`);
    }

    // Write tasks.json file
    const tasksFilePath = path.join(projectDir, 'tasks.json');
    fs.writeFileSync(tasksFilePath, JSON.stringify(tasksData, null, 2));

    console.log(`[TASKS] ✅ tasks.json file created successfully at: ${tasksFilePath}`);
    console.log(`[TASKS] 📊 Final task count: ${tasksData.tasks.length}`);

    return { success: true, tasksCount: tasksData.tasks.length };

  } catch (error) {
    console.error(`[TASKS] ❌ Error creating tasks.json:`, error);
    return { success: false, error: error.message };
  }
}



// Helper function to create tasks.json file from database (LEGACY)
async function createTasksFileFromDatabase(stackName, projectName, projectDir, projectId = null) {
  try {
    console.log(`[TASKS] Creating tasks.json from DATABASE for ${stackName} project: ${projectName} (ID: ${projectId})`);

    let tasksData = {
      projectName: projectName,
      stackName: stackName,
      projectId: projectId,
      createdAt: new Date().toISOString(),
      tasks: []
    };

    if (projectId) {
      try {
        console.log(`[TASKS] 🔍 Attempting to load tasks from database for project ID: ${projectId}`);

        // Load tasks from database tasksJson column
        const { PrismaClient } = require(path.join(__dirname, '..', 'Vice', 'node_modules', '@prisma/client'));
        const prisma = new PrismaClient({
          datasources: {
            db: {
              url: `file:${path.join(__dirname, '..', 'Vice', 'prisma', 'dev.db')}`
            }
          }
        });

        console.log(`[TASKS] 🔍 Searching for project with ID: ${projectId} (type: ${typeof projectId})`);

        const project = await prisma.stackProject.findUnique({
          where: { id: parseInt(projectId) }
        });

        console.log(`[TASKS] 🔍 Database query result:`, project ? 'Project found' : 'Project not found');

        if (project) {
          console.log(`[TASKS] 📄 Project details:`, {
            id: project.id,
            name: project.name,
            stackType: project.stackType,
            hasTasksJson: !!project.tasksJson,
            tasksJsonLength: project.tasksJson ? project.tasksJson.length : 0
          });

          if (project.tasksJson) {
            console.log(`[TASKS] 📄 Raw tasksJson from database:`, project.tasksJson.substring(0, 500) + '...');

            try {
              const databaseTasks = JSON.parse(project.tasksJson);
              console.log(`[TASKS] 📄 Parsed tasks:`, Array.isArray(databaseTasks) ? `Array with ${databaseTasks.length} items` : `Type: ${typeof databaseTasks}`);

              // ALWAYS generate comprehensive tasks for bash projects regardless of database content
              const isBashProject = stackName.toLowerCase() === 'bash';

              if (isBashProject) {
                // For bash projects, ALWAYS generate comprehensive tasks
                console.log(`[TASKS] 🔄 Bash project detected - generating comprehensive tasks (ignoring database tasks)`);
                tasksData.tasks = generateComprehensiveTasks(stackName, projectName);
                console.log(`[TASKS] ✅ Generated ${tasksData.tasks.length} comprehensive tasks for bash project`);
                console.log(`[TASKS] ✅ First task:`, tasksData.tasks[0]?.title || 'No title');
                console.log(`[TASKS] ✅ All task titles:`, tasksData.tasks.map(t => t.title).join(', '));
              } else {
                // For non-bash projects, use database tasks
                tasksData.tasks = databaseTasks;
                console.log(`[TASKS] ✅ Successfully loaded ${databaseTasks.length} tasks from database tasksJson column`);
                console.log(`[TASKS] ✅ First task:`, databaseTasks[0]?.title || 'No title');
                console.log(`[TASKS] ✅ All task titles:`, databaseTasks.map(t => t.title).join(', '));
              }
            } catch (parseError) {
              console.error(`[TASKS] ❌ Error parsing tasksJson from database:`, parseError);
              console.log(`[TASKS] 📄 Raw tasksJson that failed to parse:`, project.tasksJson);
            }
          } else {
            console.log(`[TASKS] ⚠️ Project found but tasksJson column is empty or null`);
          }
        } else {
          console.log(`[TASKS] ⚠️ No project found in database with ID: ${projectId}`);

          // Let's also try to list all projects to see what's in the database
          const allProjects = await prisma.stackProject.findMany();
          console.log(`[TASKS] 🔍 All projects in database:`, allProjects.map(p => ({ id: p.id, name: p.name, stackType: p.stackType })));
        }

        await prisma.$disconnect();
      } catch (dbError) {
        console.error('[TASKS] ❌ Error loading tasks from database:', dbError);
        console.error('[TASKS] ❌ Full error details:', dbError.stack);
      }
    } else {
      console.log(`[TASKS] ⚠️ No project ID provided, cannot load from database`);
    }

    // If no tasks from database, generate fallback tasks
    if (tasksData.tasks.length === 0) {
      console.log(`[TASKS] 🔄 Generating fallback tasks for ${stackName} project`);
      tasksData.tasks = generateComprehensiveTasks(stackName, projectName);
    }

    const tasksPath = path.join(projectDir, 'tasks.json');
    fs.writeFileSync(tasksPath, JSON.stringify(tasksData, null, 2));
    console.log(`[TASKS] ✅ Created tasks.json with ${tasksData.tasks.length} tasks at: ${tasksPath}`);

  } catch (error) {
    console.error('[TASKS] ❌ Error creating tasks.json from database:', error);
  }
}

// Helper function to create tasks.json file for the project (LEGACY - keeping for compatibility)
async function createTasksFile(stackName, projectName, projectDir, projectId = null) {
  try {
    console.log(`[TASKS] Creating tasks.json for ${stackName} project: ${projectName} (ID: ${projectId})`);

    let tasks = [];

    // Generate tasks based on stack type
    switch (stackName.toLowerCase()) {
      case 'mern':
      case 'mern stack':
        tasks = [
          {
            id: "mern-1",
            title: "Setup Express Server",
            description: "Create a basic Express.js server with middleware",
            instructions: "Create an Express server in server/index.js with CORS, body parser, and basic routing",
            code_snippet: "const express = require('express');\nconst cors = require('cors');\nconst app = express();\n\napp.use(cors());\napp.use(express.json());\n\napp.get('/', (req, res) => {\n  res.json({ message: 'Server is running!' });\n});\n\nconst PORT = process.env.PORT || 5000;\napp.listen(PORT, () => {\n  console.log(`Server running on port ${PORT}`);\n});",
            expected_output: "Server should start and respond to GET requests on port 5000",
            category: "Backend",
            priority: "high",
            order: 1
          },
          {
            id: "mern-2",
            title: "Connect to MongoDB",
            description: "Establish connection to MongoDB database using Mongoose",
            instructions: "Add MongoDB connection using Mongoose in server/index.js",
            code_snippet: "const mongoose = require('mongoose');\n\nmongoose.connect('mongodb://localhost:27017/myapp', {\n  useNewUrlParser: true,\n  useUnifiedTopology: true\n});\n\nmongoose.connection.on('connected', () => {\n  console.log('Connected to MongoDB');\n});",
            expected_output: "Console should show 'Connected to MongoDB' message",
            category: "Database",
            priority: "high",
            order: 2
          },
          {
            id: "mern-3",
            title: "Create User Model",
            description: "Define a User schema and model using Mongoose",
            instructions: "Create a User model in server/models/User.js with name, email, and password fields",
            code_snippet: "const mongoose = require('mongoose');\n\nconst userSchema = new mongoose.Schema({\n  name: {\n    type: String,\n    required: true\n  },\n  email: {\n    type: String,\n    required: true,\n    unique: true\n  },\n  password: {\n    type: String,\n    required: true\n  }\n}, { timestamps: true });\n\nmodule.exports = mongoose.model('User', userSchema);",
            expected_output: "User model should be exportable and usable in other files",
            category: "Backend",
            priority: "medium",
            order: 3
          }
        ];
        break;

      case 'bash':
        // USE ACTUAL PRISMA DATABASE TASKS FOR BASH PROJECTS
        console.log(`[TASKS] 🔍 Loading bash tasks from PRISMA DATABASE for project ID: "${projectId}"`);

        if (!projectId) {
          console.log(`[TASKS] ❌ No project ID provided for bash project: "${projectName}"`);
          console.log(`[TASKS] 🔄 Creating comprehensive tasks for bash project based on project name`);

          // Create comprehensive tasks based on project name/description
          let comprehensiveTasks = [];
          const projectNameLower = projectName.toLowerCase();

          if (projectNameLower.includes('file') || projectNameLower.includes('organiz') || projectNameLower.includes('manager')) {
            // FILE ORGANIZER PROJECT - 12 comprehensive tasks
            comprehensiveTasks = [
              {
                id: "file-org-1",
                title: "Create Project Structure",
                description: "Set up the basic directory structure and main script file",
                instructions: "Create main script file with proper shebang and basic structure",
                code_snippet: "#!/bin/bash\n\n# File Organizer Script\n# Author: Your Name\n# Description: Organize files by type and date\n\nset -euo pipefail\n\necho 'File Organizer initialized!'",
                expected_output: "Script should run without errors and display initialization message",
                category: "Setup",
                priority: "high",
                order: 1
              },
              {
                id: "file-org-2",
                title: "Add Configuration Variables",
                description: "Define configuration variables for source and destination directories",
                instructions: "Add variables for source directory, destination directory, and file types",
                code_snippet: "# Configuration variables\nSOURCE_DIR=\"$HOME/Downloads\"\nDEST_DIR=\"$HOME/OrganizedFiles\"\nLOG_FILE=\"$HOME/file_organizer.log\"\n\n# File type categories\nIMAGES=(\"jpg\" \"jpeg\" \"png\" \"gif\" \"bmp\")\nDOCUMENTS=(\"pdf\" \"doc\" \"docx\" \"txt\" \"rtf\")\nVIDEOS=(\"mp4\" \"avi\" \"mkv\" \"mov\" \"wmv\")",
                expected_output: "Variables should be properly defined and accessible",
                category: "Configuration",
                priority: "high",
                order: 2
              },
              {
                id: "file-org-3",
                title: "Create Logging Function",
                description: "Implement logging functionality to track file operations",
                instructions: "Create a function to log messages with timestamps",
                code_snippet: "log_message() {\n    local message=\"$1\"\n    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')\n    echo \"[$timestamp] $message\" | tee -a \"$LOG_FILE\"\n}",
                expected_output: "Logging function should write timestamped messages to log file",
                category: "Utilities",
                priority: "medium",
                order: 3
              },
              {
                id: "file-org-4",
                title: "Create Directory Structure",
                description: "Function to create organized directory structure",
                instructions: "Create function to make directories for different file types",
                code_snippet: "create_directories() {\n    log_message \"Creating directory structure...\"\n    \n    mkdir -p \"$DEST_DIR/Images\"\n    mkdir -p \"$DEST_DIR/Documents\"\n    mkdir -p \"$DEST_DIR/Videos\"\n    mkdir -p \"$DEST_DIR/Others\"\n    \n    log_message \"Directory structure created successfully\"\n}",
                expected_output: "Directories should be created in the destination folder",
                category: "Setup",
                priority: "medium",
                order: 4
              },
              {
                id: "file-org-5",
                title: "File Type Detection",
                description: "Function to detect file type based on extension",
                instructions: "Create function to categorize files by their extensions",
                code_snippet: "get_file_category() {\n    local file=\"$1\"\n    local extension=\"${file##*.}\"\n    extension=$(echo \"$extension\" | tr '[:upper:]' '[:lower:]')\n    \n    if [[ \" ${IMAGES[@]} \" =~ \" $extension \" ]]; then\n        echo \"Images\"\n    elif [[ \" ${DOCUMENTS[@]} \" =~ \" $extension \" ]]; then\n        echo \"Documents\"\n    elif [[ \" ${VIDEOS[@]} \" =~ \" $extension \" ]]; then\n        echo \"Videos\"\n    else\n        echo \"Others\"\n    fi\n}",
                expected_output: "Function should return correct category for different file types",
                category: "Logic",
                priority: "high",
                order: 5
              },
              {
                id: "file-org-6",
                title: "File Moving Function",
                description: "Function to move files to appropriate directories",
                instructions: "Create function to safely move files with conflict resolution",
                code_snippet: "move_file() {\n    local source_file=\"$1\"\n    local category=\"$2\"\n    local filename=$(basename \"$source_file\")\n    local dest_path=\"$DEST_DIR/$category/$filename\"\n    \n    if [[ -f \"$dest_path\" ]]; then\n        local timestamp=$(date '+%Y%m%d_%H%M%S')\n        dest_path=\"$DEST_DIR/$category/${filename%.*}_$timestamp.${filename##*.}\"\n    fi\n    \n    mv \"$source_file\" \"$dest_path\"\n    log_message \"Moved: $filename -> $category/\"\n}",
                expected_output: "Files should be moved to correct directories with conflict resolution",
                category: "Logic",
                priority: "high",
                order: 6
              },
              {
                id: "file-org-7",
                title: "Main Processing Loop",
                description: "Main loop to process all files in source directory",
                instructions: "Create main loop to iterate through files and organize them",
                code_snippet: "organize_files() {\n    log_message \"Starting file organization...\"\n    \n    if [[ ! -d \"$SOURCE_DIR\" ]]; then\n        log_message \"Error: Source directory does not exist: $SOURCE_DIR\"\n        exit 1\n    fi\n    \n    local file_count=0\n    \n    for file in \"$SOURCE_DIR\"/*; do\n        if [[ -f \"$file\" ]]; then\n            local category=$(get_file_category \"$file\")\n            move_file \"$file\" \"$category\"\n            ((file_count++))\n        fi\n    done\n    \n    log_message \"Organization complete. Processed $file_count files.\"\n}",
                expected_output: "All files in source directory should be organized by type",
                category: "Main Logic",
                priority: "high",
                order: 7
              },
              {
                id: "file-org-8",
                title: "Add Command Line Arguments",
                description: "Support for command line arguments and options",
                instructions: "Add support for custom source and destination directories",
                code_snippet: "parse_arguments() {\n    while [[ $# -gt 0 ]]; do\n        case $1 in\n            -s|--source)\n                SOURCE_DIR=\"$2\"\n                shift 2\n                ;;\n            -d|--dest)\n                DEST_DIR=\"$2\"\n                shift 2\n                ;;\n            -h|--help)\n                show_help\n                exit 0\n                ;;\n            *)\n                echo \"Unknown option: $1\"\n                show_help\n                exit 1\n                ;;\n        esac\n    done\n}",
                expected_output: "Script should accept and process command line arguments",
                category: "Interface",
                priority: "medium",
                order: 8
              },
              {
                id: "file-org-9",
                title: "Add Help Function",
                description: "Create help function to display usage information",
                instructions: "Create function to show script usage and options",
                code_snippet: "show_help() {\n    cat << EOF\nFile Organizer Script\n\nUsage: $0 [OPTIONS]\n\nOptions:\n    -s, --source DIR    Source directory (default: $HOME/Downloads)\n    -d, --dest DIR      Destination directory (default: $HOME/OrganizedFiles)\n    -h, --help          Show this help message\n\nExample:\n    $0 -s /path/to/messy/folder -d /path/to/organized/folder\nEOF\n}",
                expected_output: "Help message should display usage information clearly",
                category: "Interface",
                priority: "low",
                order: 9
              },
              {
                id: "file-org-10",
                title: "Add Progress Reporting",
                description: "Show progress during file organization",
                instructions: "Add progress indicators and file count reporting",
                code_snippet: "show_progress() {\n    local current=\"$1\"\n    local total=\"$2\"\n    local percent=$((current * 100 / total))\n    local bar_length=20\n    local filled_length=$((percent * bar_length / 100))\n    \n    printf \"\\rProgress: [\"\n    for ((i=0; i<filled_length; i++)); do printf \"#\"; done\n    for ((i=filled_length; i<bar_length; i++)); do printf \"-\"; done\n    printf \"] %d%% (%d/%d)\" \"$percent\" \"$current\" \"$total\"\n}",
                expected_output: "Progress bar should show current organization status",
                category: "Interface",
                priority: "low",
                order: 10
              },
              {
                id: "file-org-11",
                title: "Add Dry Run Mode",
                description: "Option to preview changes without actually moving files",
                instructions: "Add dry run mode to show what would be organized",
                code_snippet: "DRY_RUN=false\n\nmove_file_dry_run() {\n    local source_file=\"$1\"\n    local category=\"$2\"\n    local filename=$(basename \"$source_file\")\n    \n    if [[ \"$DRY_RUN\" == \"true\" ]]; then\n        log_message \"[DRY RUN] Would move: $filename -> $category/\"\n    else\n        move_file \"$source_file\" \"$category\"\n    fi\n}",
                expected_output: "Dry run should show planned moves without actually moving files",
                category: "Safety",
                priority: "medium",
                order: 11
              },
              {
                id: "file-org-12",
                title: "Main Execution and Error Handling",
                description: "Put it all together with proper error handling",
                instructions: "Create main execution flow with comprehensive error handling",
                code_snippet: "main() {\n    parse_arguments \"$@\"\n    \n    log_message \"File Organizer started\"\n    log_message \"Source: $SOURCE_DIR\"\n    log_message \"Destination: $DEST_DIR\"\n    \n    create_directories\n    organize_files\n    \n    log_message \"File organization completed successfully\"\n}\n\n# Error handling\ntrap 'log_message \"Error occurred. Exiting...\"; exit 1' ERR\n\n# Run main function\nmain \"$@\"",
                expected_output: "Complete file organizer should work end-to-end with error handling",
                category: "Integration",
                priority: "high",
                order: 12
              }
            ];
          } else if (projectNameLower.includes('monitor') || projectNameLower.includes('system') || projectNameLower.includes('performance')) {
            // SYSTEM MONITOR PROJECT - 12 comprehensive tasks
            comprehensiveTasks = [
              {
                id: "sys-mon-1",
                title: "Create System Monitor Foundation",
                description: "Set up basic system monitoring script structure",
                instructions: "Create main script with system information gathering capabilities",
                code_snippet: "#!/bin/bash\n\n# System Monitor Script\n# Description: Monitor system performance and resources\n\nset -euo pipefail\n\necho 'System Monitor initialized!'",
                expected_output: "Script should initialize and display system information",
                category: "Setup",
                priority: "high",
                order: 1
              },
              {
                id: "sys-mon-2",
                title: "CPU Usage Monitoring",
                description: "Function to monitor CPU usage and load average",
                instructions: "Create function to get CPU usage percentage and load average",
                code_snippet: "get_cpu_usage() {\n    local cpu_usage=$(top -bn1 | grep \"Cpu(s)\" | awk '{print $2}' | cut -d'%' -f1)\n    local load_avg=$(uptime | awk -F'load average:' '{print $2}')\n    \n    echo \"CPU Usage: ${cpu_usage}%\"\n    echo \"Load Average:${load_avg}\"\n}",
                expected_output: "Function should display current CPU usage and load average",
                category: "Monitoring",
                priority: "high",
                order: 2
              }
              // ... more system monitor tasks would continue here
            ];
          } else {
            // GENERIC BASH PROJECT - 12 comprehensive tasks
            comprehensiveTasks = [
              {
                id: "bash-gen-1",
                title: "Create Script Foundation",
                description: "Set up basic bash script structure with error handling",
                instructions: "Create main script file with proper shebang, error handling, and basic structure",
                code_snippet: "#!/bin/bash\n\n# ${projectName} Script\n# Description: ${projectName} automation script\n\nset -euo pipefail\n\necho '${projectName} script initialized!'",
                expected_output: "Script should run without errors and display initialization message",
                category: "Foundation",
                priority: "high",
                order: 1
              }
              // ... more generic tasks would continue here
            ];
          }

          const tasksData = {
            projectName: projectName,
            stackName: stackName,
            projectId: null,
            createdAt: new Date().toISOString(),
            tasks: comprehensiveTasks
          };

          const tasksPath = path.join(projectDir, 'tasks.json');
          fs.writeFileSync(tasksPath, JSON.stringify(tasksData, null, 2));
          console.log(`[TASKS] ✅ Created comprehensive tasks.json with ${comprehensiveTasks.length} tasks for bash project`);
          return;
        }

        try {
          // Load bash project tasks from ACTUAL Prisma database using project ID
          const bashProjectTasks = await loadBashProjectTasksFromDatabase(projectId);

          if (bashProjectTasks && bashProjectTasks.length > 0) {
            const tasksData = {
              projectName: projectName,
              stackName: stackName,
              projectId: projectId,
              createdAt: new Date().toISOString(),
              tasks: bashProjectTasks
            };

            const tasksPath = path.join(projectDir, 'tasks.json');
            fs.writeFileSync(tasksPath, JSON.stringify(tasksData, null, 2));
            console.log(`[TASKS] ✅ Created tasks.json with ${bashProjectTasks.length} PRISMA DATABASE tasks`);
            console.log(`[TASKS] 📋 Project ID: ${projectId}, Tasks: ${bashProjectTasks.map(t => t.title).join(', ')}`);
          } else {
            console.log(`[TASKS] ⚠️ No database tasks found for bash project ID: "${projectId}"`);
          }
        } catch (error) {
          console.error(`[TASKS] ❌ Error loading PRISMA database tasks:`, error);
        }

        return;

      default:
        // For non-bash projects, use simple generic tasks
        tasks = [
          {
            id: "generic-1",
            title: "Project Setup",
            description: "Initialize the project structure and basic configuration",
            instructions: "Set up the basic project structure and configuration files",
            code_snippet: "// Basic project setup\nconsole.log('Project initialized successfully!');",
            expected_output: "Project should be properly initialized",
            category: "Setup",
            priority: "high",
            order: 1
          }
        ];

        const tasksData = {
          projectName: projectName,
          stackName: stackName,
          createdAt: new Date().toISOString(),
          tasks: tasks
        };

        const tasksPath = path.join(projectDir, 'tasks.json');

        // Force delete existing tasks.json if it exists
        if (fs.existsSync(tasksPath)) {
          console.log(`[TASKS] 🗑️ Deleting existing tasks.json file`);
          fs.unlinkSync(tasksPath);
        }

        fs.writeFileSync(tasksPath, JSON.stringify(tasksData, null, 2));

        console.log(`[TASKS] ✅ Created tasks.json with ${tasks.length} tasks at: ${tasksPath}`);
        console.log(`[TASKS] 📋 Tasks created:`, tasks.map(t => `${t.id}: ${t.title}`).join(', '));
        break;
    }

    // REMOVED: Old task creation system that was causing bash-general tasks
    // All task creation is now handled within the switch cases above

  } catch (error) {
    console.error('[TASKS] ❌ Error creating tasks.json:', error);
  }
}

// Helper function to read project files recursively
async function readProjectFiles(projectPath) {
  const files = [];

  function readDirectory(dirPath, relativePath = '') {
    try {
      if (!fs.existsSync(dirPath)) return;

      const items = fs.readdirSync(dirPath);

      for (const item of items) {
        // Skip hidden files and node_modules
        if (item.startsWith('.') || item === 'node_modules') continue;

        const itemPath = path.join(dirPath, item);
        const stat = fs.statSync(itemPath);
        const relativeItemPath = relativePath ? `${relativePath}/${item}` : item;

        if (stat.isDirectory()) {
          // Add folder entry
          files.push({
            name: relativeItemPath,
            content: '',
            isDirectory: true
          });

          // Recursively read subdirectory (limit depth to 3)
          if (relativePath.split('/').length < 3) {
            readDirectory(itemPath, relativeItemPath);
          }
        } else if (stat.isFile()) {
          try {
            const content = fs.readFileSync(itemPath, 'utf8');
            files.push({
              name: relativeItemPath,
              content: content,
              isDirectory: false
            });
          } catch (readError) {
            console.log(`Skipping file ${item}: ${readError.message}`);
          }
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dirPath}:`, error);
    }
  }

  readDirectory(projectPath);
  return files;
}

ipcMain.handle('show-folder-dialog', async () => {
  const result = await dialog.showOpenDialog(dashboardWindow, {
    properties: ['openDirectory'],
    title: 'Select Project Directory'
  });

  return result;
});

ipcMain.handle('read-project-files', async (event, projectPath) => {
  try {
    return await readProjectFiles(projectPath);
  } catch (error) {
    console.error('Error reading project files:', error);
    return [];
  }
});

// Authentication window handlers (matching original pattern)
ipcMain.handle('open-auth-window', async (event, authUrl) => {
  console.log('Received request to open auth window with URL:', authUrl);

  if (authWindow) {
    authWindow.close();
  }

  authWindow = new BrowserWindow({
    width: 800,
    height: 600,
    show: true,
    parent: dashboardWindow,
    modal: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true,
      allowRunningInsecureContent: false
    }
  });

  await authWindow.loadURL(authUrl);

  return new Promise((resolve) => {
    // Handle token extraction from URL
    authWindow.webContents.on('will-navigate', handleNavigation);
    authWindow.webContents.on('did-navigate', handleNavigation);

    // Handle new windows (e.g., OAuth popups)
    authWindow.webContents.setWindowOpenHandler(({ url }) => {
      return { action: 'allow' };
    });

    // Handle window closed without authentication
    authWindow.on('closed', () => {
      authWindow = null;
      resolve({ success: false });
    });

    // Function to handle URL changes and extract tokens
    function handleNavigation(event, newUrl) {
      console.log('Navigation detected to:', newUrl);

      try {
        const urlObj = new URL(newUrl);
        console.log('URL parameters:', urlObj.searchParams.toString());

        const token = urlObj.searchParams.get('token');

        if (token) {
          console.log('Token found in URL, authentication successful');

          // Send the token to the main window
          if (dashboardWindow && !dashboardWindow.isDestroyed()) {
            dashboardWindow.webContents.send('auth-event', { type: 'token-received', token });
          }

          authWindow.close();
          resolve({ success: true, token });
        } else if (newUrl.includes('authentication-successful') || newUrl.includes('auth-success')) {
          console.log('Authentication success page detected, looking for token in localStorage');

          authWindow.webContents.executeJavaScript(`
            (function() {
              const token = localStorage.getItem('firebase_id_token');
              return token;
            })();
          `).then(token => {
            if (token) {
              console.log('Token found in localStorage');

              if (dashboardWindow && !dashboardWindow.isDestroyed()) {
                dashboardWindow.webContents.send('auth-event', { type: 'token-received', token });
              }

              authWindow.close();
              resolve({ success: true, token });
            }
          }).catch(err => {
            console.error('Error extracting token from localStorage:', err);
          });
        }
      } catch (error) {
        console.error('Error parsing URL:', error);
      }
    }
  });
});

// Additional handlers for compatibility
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('reload-app', () => {
  if (dashboardWindow) {
    dashboardWindow.reload();
  } else {
    createDashboardWindow();
  }
});

// Handle app protocol for deep linking (optional)
app.setAsDefaultProtocolClient('vice');

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

// === REAL FILE SYSTEM OPERATIONS FOR DESKTOP APP ===

// File System Operations (with encoding support for images)
ipcMain.handle('fs:readFile', async (event, filePath, options = {}) => {
  try {
    const encoding = options.encoding || 'utf-8';
    const content = await fsPromises.readFile(filePath, encoding);
    return { success: true, data: content, content }; // Keep both for compatibility
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('fs:writeFile', async (event, filePath, content) => {
  try {
    // Ensure directory exists
    const dir = path.dirname(filePath);
    await fsPromises.mkdir(dir, { recursive: true });
    await fsPromises.writeFile(filePath, content, 'utf-8');
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('fs:deleteFile', async (event, filePath) => {
  try {
    await fsPromises.unlink(filePath);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('fs:createDirectory', async (event, dirPath) => {
  try {
    await fsPromises.mkdir(dirPath, { recursive: true });
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('fs:deleteDirectory', async (event, dirPath) => {
  try {
    await fsPromises.rmdir(dirPath, { recursive: true });
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('fs:readDirectory', async (event, dirPath) => {
  try {
    const items = await fsPromises.readdir(dirPath, { withFileTypes: true });
    const result = items.map(item => ({
      name: item.name,
      isDirectory: item.isDirectory(),
      isFile: item.isFile()
    }));
    return { success: true, items: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('fs:exists', async (event, filePath) => {
  try {
    await fsPromises.access(filePath);
    return { success: true, exists: true };
  } catch (error) {
    return { success: true, exists: false };
  }
});

// Terminal Operations
ipcMain.handle('terminal:execute', async (event, command, cwd) => {
  return new Promise((resolve) => {
    const isWindows = process.platform === 'win32';
    let shell, args;

    if (isWindows) {
      shell = 'cmd.exe';
      args = ['/c', command];
    } else {
      shell = '/bin/bash';
      args = ['-c', command];
    }

    const child = spawn(shell, args, {
      cwd: cwd || process.cwd(),
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: false
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      resolve({
        success: code === 0,
        stdout: stdout.trim(),
        stderr: stderr.trim(),
        exitCode: code
      });
    });

    child.on('error', (error) => {
      resolve({
        success: false,
        stdout: '',
        stderr: error.message,
        exitCode: -1,
        error: error.message
      });
    });

    // Set timeout to prevent hanging
    setTimeout(() => {
      if (!child.killed) {
        child.kill();
        resolve({
          success: false,
          stdout: stdout.trim(),
          stderr: 'Command timed out',
          exitCode: -1,
          error: 'Command execution timed out'
        });
      }
    }, 30000); // 30 second timeout
  });
});

// Dialog Operations
ipcMain.handle('dialog:openDirectory', async () => {
  try {
    const result = await dialog.showOpenDialog(dashboardWindow || ideWindow, {
      properties: ['openDirectory'],
      title: 'Select Project Directory'
    });

    if (result.canceled) {
      return { success: true, canceled: true };
    }

    return { success: true, canceled: false, path: result.filePaths[0] };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Auto-save Operations
let autoSaveEnabled = false;
let autoSaveInterval = null;
const unsavedFiles = new Map();

ipcMain.handle('autosave:toggle', (event, enabled) => {
  autoSaveEnabled = enabled;

  if (enabled && !autoSaveInterval) {
    // Auto-save every 5 seconds
    autoSaveInterval = setInterval(async () => {
      for (const [filePath, content] of unsavedFiles.entries()) {
        try {
          await fsPromises.writeFile(filePath, content, 'utf-8');
          unsavedFiles.delete(filePath);

          // Notify all windows that file was saved
          if (dashboardWindow) dashboardWindow.webContents.send('autosave:saved', filePath);
          if (ideWindow) ideWindow.webContents.send('autosave:saved', filePath);
        } catch (error) {
          console.error('Auto-save failed for', filePath, error);
        }
      }
    }, 5000);
  } else if (!enabled && autoSaveInterval) {
    clearInterval(autoSaveInterval);
    autoSaveInterval = null;
  }

  return { success: true, enabled: autoSaveEnabled };
});

ipcMain.handle('autosave:markUnsaved', (event, filePath, content) => {
  if (autoSaveEnabled) {
    unsavedFiles.set(filePath, content);
  }
  return { success: true };
});

ipcMain.handle('autosave:getStatus', () => {
  return {
    success: true,
    enabled: autoSaveEnabled,
    unsavedCount: unsavedFiles.size
  };
});

// Project Operations - Load real project files from filesystem
ipcMain.handle('project:loadDirectory', async (event, dirPath) => {
  try {
    const loadDirectoryRecursive = async (currentPath, relativePath = '') => {
      const items = await fsPromises.readdir(currentPath, { withFileTypes: true });
      const files = [];

      for (const item of items) {
        const fullPath = path.join(currentPath, item.name);
        const relativeItemPath = path.join(relativePath, item.name);

        // Skip node_modules, .git, .next, and other common directories
        if (item.isDirectory() && ['node_modules', '.git', '.next', 'out', 'dist', 'build'].includes(item.name)) {
          continue;
        }

        if (item.isDirectory()) {
          // Recursively load subdirectory
          const subFiles = await loadDirectoryRecursive(fullPath, relativeItemPath);
          files.push(...subFiles);
        } else {
          // Load file content for text files
          const ext = path.extname(item.name).toLowerCase();
          const textExtensions = ['.js', '.jsx', '.ts', '.tsx', '.css', '.html', '.json', '.md', '.txt', '.py', '.java', '.cpp', '.c', '.h'];

          let content = '';
          if (textExtensions.includes(ext)) {
            try {
              content = await fsPromises.readFile(fullPath, 'utf-8');
            } catch (error) {
              content = `// Error reading file: ${error.message}`;
            }
          } else {
            content = `// Binary file: ${item.name}`;
          }

          files.push({
            id: `file-${Date.now()}-${Math.random().toString(36).substring(7)}`,
            name: item.name,
            content: content,
            path: fullPath,
            relativePath: relativeItemPath,
            type: item.isDirectory() ? 'folder' : 'file',
            size: (await fsPromises.stat(fullPath)).size,
            lastModified: (await fsPromises.stat(fullPath)).mtime
          });
        }
      }

      return files;
    };

    const projectFiles = await loadDirectoryRecursive(dirPath);
    const projectName = path.basename(dirPath);

    return {
      success: true,
      projectName,
      projectPath: dirPath,
      files: projectFiles
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Get system information
ipcMain.handle('system:getInfo', () => {
  return {
    success: true,
    platform: process.platform,
    arch: process.arch,
    nodeVersion: process.version,
    electronVersion: process.versions.electron,
    homeDir: os.homedir(),
    documentsDir: path.join(os.homedir(), 'Documents'),
    desktopDir: path.join(os.homedir(), 'Desktop')
  };
});

// Theme synchronization between Dashboard and IDE
let currentTheme = 'system';

ipcMain.handle('theme:get', () => {
  return { success: true, theme: currentTheme };
});

ipcMain.handle('theme:set', (event, theme) => {
  currentTheme = theme;

  // Broadcast theme change to all windows
  if (dashboardWindow && !dashboardWindow.isDestroyed()) {
    dashboardWindow.webContents.send('theme:changed', theme);
  }
  if (ideWindow && !ideWindow.isDestroyed()) {
    ideWindow.webContents.send('theme:changed', theme);
  }

  return { success: true, theme: currentTheme };
});
