@echo off
title TEST FUNCTIONAL PROJECT CREATION
color 0A

echo ========================================
echo   TEST FUNCTIONAL PROJECT CREATION
echo ========================================
echo.
echo 🚀 TESTING RESTORED FUNCTIONAL PROJECT CREATION
echo.
echo **What's Restored:**
echo ✅ **Real project creation** → Runs actual commands (npm, mkdir, etc.)
echo ✅ **Text field constraints** → No spaces, no caps, hyphens for multi-word
echo ✅ **Command execution** → Shows real-time command progress
echo ✅ **IDE integration** → Opens created project in Vice IDE
echo ✅ **Task.json setup** → Creates matching task files for each stack
echo ✅ **File organizer** → Bash projects run file organizer setup
echo ✅ **Loading animation** → Enhanced terminal-style progress display
echo ✅ **Dashboard return** → Returns to dashboard after IDE opens
echo.
pause

echo **Step 1: Start Vice services...**
echo **Starting Dashboard...**
cd Vice
start "Vice Dashboard" cmd /k "title Vice Dashboard && npm run dev"
cd ..

echo **Starting IDE...**
cd ViceIde
start "Vice IDE" cmd /k "title Vice IDE && npm run dev"
cd ..

echo **Waiting for services to start...**
timeout /t 15 /nobreak >nul

echo.
echo **Step 2: Launch VICE with functional project creation...**
echo **Testing real project creation with commands...**

npm run electron

echo.
echo ========================================
echo ✅ FUNCTIONAL PROJECT CREATION READY!
echo ========================================
echo.

echo **🎯 FUNCTIONAL TESTING WORKFLOW:**
echo.
echo **1. Test Real Project Creation:**
echo    ✅ **Create React project** → Should run 'npx create-react-app'
echo    ✅ **Create Next.js project** → Should run 'npx create-next-app'
echo    ✅ **Create Django project** → Should run 'python -m venv' and 'django-admin'
echo    ✅ **Create Flask project** → Should run 'python -m venv' and 'pip install flask'
echo    ✅ **Create Bash project** → Should run file organizer setup
echo    ✅ **Create MERN project** → Should run React + Express setup
echo.
echo **2. Test Text Field Constraints:**
echo    ✅ **Type "My Test Project"** → Should show "Will be: my-test-project"
echo    ✅ **Type "React App 2024"** → Should show "Will be: react-app-2024"
echo    ✅ **Type "CAPS PROJECT"** → Should show "Will be: caps-project"
echo    ✅ **Type "special@chars"** → Should show "Will be: special-chars"
echo    ✅ **See constraint message** → "No spaces or caps allowed"
echo.
echo **3. Test Command Execution:**
echo    ✅ **Watch loading screen** → Should show real commands being executed
echo    ✅ **See command progress** → Terminal-style output with colors
echo    ✅ **Real-time updates** → Commands appear as they run
echo    ✅ **Completion status** → Green checkmarks when commands finish
echo    ✅ **Project name display** → Shows "Creating 'project-name'"
echo.
echo **4. Test IDE Integration:**
echo    ✅ **Project opens in IDE** → Should load created files
echo    ✅ **File explorer populated** → Shows actual project structure
echo    ✅ **Monaco editor ready** → Can edit created files
echo    ✅ **Terminal available** → Can run commands in project directory
echo    ✅ **Task.json created** → Matching tasks for the stack type
echo.
echo **5. Test Stack-Specific Features:**
echo    ✅ **Bash projects** → File organizer setup runs automatically
echo    ✅ **React projects** → package.json and src folder created
echo    ✅ **Django projects** → Virtual environment and Django setup
echo    ✅ **Flask projects** → Virtual environment and Flask installation
echo    ✅ **Next.js projects** → Next.js app with TypeScript support
echo.
echo ========================================
echo EXPECTED FUNCTIONAL RESULTS
echo ========================================
echo.

echo **✅ REAL PROJECT CREATION:**
echo.
echo **Text Field Constraints:**
echo 1. **"My Test Project"** → becomes **"my-test-project"**
echo 2. **"React App 2024"** → becomes **"react-app-2024"**
echo 3. **"CAPS PROJECT"** → becomes **"caps-project"**
echo 4. **"special@chars"** → becomes **"special-chars"**
echo 5. **Real-time preview** → Shows formatted name while typing
echo.
echo **Command Execution:**
echo 1. **Real commands run** → npm, mkdir, touch, python, django-admin
echo 2. **Terminal-style output** → Black background with colored text
echo 3. **Progress indicators** → Spinning loaders and completion checkmarks
echo 4. **Command streaming** → See commands as they execute
echo 5. **Error handling** → Shows errors if commands fail
echo.
echo **Project Structure Created:**
echo 1. **React projects** → src/, public/, package.json, node_modules/
echo 2. **Django projects** → venv/, manage.py, settings.py, urls.py
echo 3. **Flask projects** → venv/, app/, requirements.txt
echo 4. **Bash projects** → scripts/, file organizer structure
echo 5. **Next.js projects** → pages/, components/, next.config.js
echo.
echo **IDE Integration:**
echo 1. **Project loads** → All created files appear in file explorer
echo 2. **Monaco editor** → Can edit files immediately
echo 3. **Terminal integration** → Commands run in project directory
echo 4. **Task.json setup** → Stack-specific tasks available
echo 5. **File organizer** → Bash projects have organizer functionality
echo.
echo ========================================
echo STACK-SPECIFIC TESTING
echo ========================================
echo.

echo **🧪 Test Each Stack Type:**
echo.
echo **React Stack:**
echo    • Project name: "my-react-app"
echo    • Should run: npx create-react-app my-react-app
echo    • Should create: src/, public/, package.json
echo    • Should open in IDE with React file structure
echo.
echo **Next.js Stack:**
echo    • Project name: "my-nextjs-app"
echo    • Should run: npx create-next-app@latest my-nextjs-app
echo    • Should create: pages/, components/, next.config.js
echo    • Should open in IDE with Next.js structure
echo.
echo **Django Stack:**
echo    • Project name: "my-django-api"
echo    • Should run: python -m venv venv, django-admin startproject
echo    • Should create: venv/, manage.py, settings.py
echo    • Should open in IDE with Django structure
echo.
echo **Flask Stack:**
echo    • Project name: "my-flask-api"
echo    • Should run: python -m venv venv, pip install flask
echo    • Should create: venv/, app/, requirements.txt
echo    • Should open in IDE with Flask structure
echo.
echo **Bash Stack (File Organizer):**
echo    • Project name: "my-file-organizer"
echo    • Should run: file organizer setup commands
echo    • Should create: scripts/, organizer structure
echo    • Should setup matching task.json for file organization
echo    • Should open in IDE with organizer functionality
echo.
echo **MERN Stack:**
echo    • Project name: "my-mern-app"
echo    • Should run: React setup + Express installation
echo    • Should create: client/, server/, package.json files
echo    • Should open in IDE with full-stack structure
echo.
echo ========================================
echo TROUBLESHOOTING
echo ========================================
echo.

echo **If project creation fails:**
echo 1. **Check Electron API** → Should be available in desktop app
echo 2. **Check file permissions** → Write access to ViceProjects folder
echo 3. **Check dependencies** → npm, python should be installed
echo 4. **Check network** → Internet access for package downloads
echo.
echo **If commands don't run:**
echo 1. **Check command execution** → Should see real commands in loading
echo 2. **Check project path** → Should be valid Windows path
echo 3. **Check stack commands** → Each stack has specific commands
echo 4. **Check console logs** → F12 for detailed error messages
echo.
echo **If IDE doesn't open project:**
echo 1. **Check IDE service** → Should be running on port 9003
echo 2. **Check project files** → Should be created in ViceProjects folder
echo 3. **Check file discovery** → IDE should find files automatically
echo 4. **Manual navigation** → Go to IDE and open project manually
echo.
echo **🚀 Functional project creation with real commands!**
echo.
echo **Key Features Working:**
echo 1. **Real command execution** → Actual npm, python, django commands
echo 2. **Text field constraints** → Proper name formatting and validation
echo 3. **Stack-specific setup** → Each stack creates appropriate structure
echo 4. **Task.json creation** → Matching tasks for each project type
echo 5. **File organizer** → Bash projects get organizer functionality
echo 6. **IDE integration** → Projects open with full file structure
echo 7. **Loading animations** → Real-time command progress display
echo.
echo **🎉 Vice IDE with fully functional project creation!**
echo.
pause
