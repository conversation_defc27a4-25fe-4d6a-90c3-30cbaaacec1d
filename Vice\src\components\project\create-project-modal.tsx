'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { ArrowLeft, ChevronRight, Terminal } from 'lucide-react';

interface CreateProjectModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  stackName: string;
  projectTitle?: string;
}

export function CreateProjectModal({
  open,
  onOpenChange,
  stackName,
  projectTitle = '',
}: CreateProjectModalProps) {
  const { toast } = useToast();
  const [projectName, setProjectName] = useState(projectTitle);
  const [projectPath, setProjectPath] = useState(`Documents/ViceProjects/${stackName.toLowerCase()}`);
  const [projectDescription, setProjectDescription] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [showCommands, setShowCommands] = useState(false);

  // Starting commands for different stacks
  const startingCommands = {
    'Mern': [
      'npx create-react-app my-app',
      'cd my-app',
      'npm install express mongoose cors dotenv',
      'mkdir server',
      'touch server/index.js server/models/User.js server/routes/api.js'
    ],
    'Pern': [
      'npx create-react-app my-app',
      'cd my-app',
      'npm install express pg cors dotenv',
      'mkdir server',
      'touch server/index.js server/db.js server/routes/api.js'
    ],
    'NextJS': [
      'npx create-next-app@latest my-app',
      'cd my-app',
      'npm install'
    ],
    'Django': [
      'python -m venv venv',
      'venv\\Scripts\\activate',
      'pip install django djangorestframework',
      'django-admin startproject myproject .',
      'python manage.py startapp myapp'
    ],
    'Flask': [
      'python -m venv venv',
      'venv\\Scripts\\activate',
      'pip install flask flask-sqlalchemy flask-cors',
      'mkdir app',
      'touch app/__init__.py app/routes.py app/models.py'
    ],
    'Bash': [
      'mkdir my-script',
      'cd my-script',
      'touch script.sh',
      'chmod +x script.sh',
      'echo "#!/bin/bash" > script.sh'
    ]
  };

  const handleCreate = async () => {
    if (!projectName.trim()) {
      toast({
        title: 'Project name required',
        description: 'Please enter a name for your project.',
        variant: 'destructive',
      });
      return;
    }

    setIsCreating(true);

    try {
      // In a real app, this would create the project
      // For now, we'll just simulate the creation process
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Show success message
      toast({
        title: 'Project created',
        description: `${projectName} has been created successfully.`,
      });

      // Close the modal
      onOpenChange(false);
    } catch (error) {
      toast({
        title: 'Failed to create project',
        description: 'An error occurred while creating the project.',
        variant: 'destructive',
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px] md:max-w-[600px] rounded-xl overflow-hidden">
        {/* Logo at the top center */}
        <div className="flex justify-center mb-2">
          <div className="relative h-20 w-40">
            <Image
              src="/logo.png"
              alt="VICE Logo"
              fill
              style={{ objectFit: 'contain' }}
              priority
            />
          </div>
        </div>

        {/* Content container with animation */}
        <div className="relative overflow-hidden">
          {/* Project Form */}
          <div
            className={`transition-transform duration-300 ease-in-out ${
              showCommands ? 'translate-x-[-100%]' : 'translate-x-0'
            }`}
          >
            <DialogHeader className="mb-[5] pb-2">
              <DialogTitle className='text-center'>Create New Project</DialogTitle>
              <DialogDescription className='text-center text-sm text-muted-foreground'>
                Create a new {stackName} project. Fill in the details below to get started.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4 px-2">
              <div className="flex flex-col gap-1.5">
                <Label htmlFor="name" className="text-sm font-medium">
                  Name
                </Label>
                <Input
                  id="name"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  className="w-full"
                  placeholder="My Awesome Project"
                  autoFocus
                />
              </div>
              <div className="flex flex-col gap-1.5">
                <Label htmlFor="path" className="text-sm font-medium">
                  Path
                </Label>
                <Input
                  id="path"
                  value={projectPath}
                  onChange={(e) => setProjectPath(e.target.value)}
                  className="w-full"
                  placeholder="Documents/ViceProjects/"
                />
              </div>
              <div className="flex flex-col gap-1.5">
                <Label htmlFor="description" className="text-sm font-medium">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                  className="w-full"
                  placeholder="A brief description of your project"
                  rows={3}
                />
              </div>
            </div>
            <div className="flex flex-col">
              <div className="flex items-center mt-2 mb-4 ml-3">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs text-muted-foreground flex items-center gap-1 p-0 h-auto"
                  onClick={() => setShowCommands(true)}
                >
                  <Terminal className="h-3.5 w-3.5 mr-1" />
                  See Starting Commands
                  <ChevronRight className="h-3.5 w-3.5 ml-1" />
                </Button>
              </div>

              <DialogFooter className="flex justify-end items-center">
                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => onOpenChange(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreate} disabled={isCreating}>
                    {isCreating ? 'Creating...' : 'Create Project'}
                  </Button>
                </div>
              </DialogFooter>
            </div>
          </div>

          {/* Starting Commands */}
          <div
            className={`absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out ${
              showCommands ? 'translate-x-0' : 'translate-x-[100%]'
            }`}
          >
            <div className="mb-4 ml-3">
              <Button
                variant="ghost"
                size="sm"
                className="pl-0 flex items-center gap-1"
                onClick={() => setShowCommands(false)}
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Project Form
              </Button>
            </div>

            <DialogHeader>
              <DialogTitle>Starting Commands for {stackName}</DialogTitle>
              <DialogDescription>
                Use these commands to set up your {stackName} project manually.
              </DialogDescription>
            </DialogHeader>

            <div className="py-4 px-3">
              <div className="bg-muted p-4 rounded-md font-mono text-xs sm:text-sm overflow-auto max-h-[250px]">
                {startingCommands[stackName as keyof typeof startingCommands]?.map((command, index) => (
                  <div key={index} className="flex items-start mb-2">
                    <span className="text-muted-foreground mr-2 flex-shrink-0">$</span>
                    <span className="break-all">{command}</span>
                  </div>
                )) || (
                  <div className="text-muted-foreground">No commands available for {stackName}.</div>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Close
              </Button>
            </DialogFooter>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
