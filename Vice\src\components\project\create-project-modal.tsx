'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { ArrowLeft, ChevronRight, Terminal } from 'lucide-react';
import ProjectCreationLoading from './project-creation-loading';

interface CreateProjectModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  stackName: string;
  projectTitle?: string;
}

export function CreateProjectModal({
  open,
  onOpenChange,
  stackName,
  projectTitle = '',
}: CreateProjectModalProps) {
  const { toast } = useToast();
  const [projectName, setProjectName] = useState(projectTitle);
  const [projectPath, setProjectPath] = useState(`Documents/ViceProjects/${stackName.toLowerCase()}`);
  const [projectDescription, setProjectDescription] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [showCommands, setShowCommands] = useState(false);
  const [showLoading, setShowLoading] = useState(false);
  const [currentCommand, setCurrentCommand] = useState('');
  const [commandProgress, setCommandProgress] = useState<string[]>([]);
  const [isCommandRunning, setIsCommandRunning] = useState(false);

  // Starting commands for different stacks
  const startingCommands = {
    'Mern': [
      'npx create-react-app my-app',
      'cd my-app',
      'npm install express mongoose cors dotenv',
      'mkdir server',
      'touch server/index.js server/models/User.js server/routes/api.js'
    ],
    'Pern': [
      'npx create-react-app my-app',
      'cd my-app',
      'npm install express pg cors dotenv',
      'mkdir server',
      'touch server/index.js server/db.js server/routes/api.js'
    ],
    'NextJS': [
      'npx create-next-app@latest my-app',
      'cd my-app',
      'npm install'
    ],
    'Django': [
      'python -m venv venv',
      'venv\\Scripts\\activate',
      'pip install django djangorestframework',
      'django-admin startproject myproject .',
      'python manage.py startapp myapp'
    ],
    'Flask': [
      'python -m venv venv',
      'venv\\Scripts\\activate',
      'pip install flask flask-sqlalchemy flask-cors',
      'mkdir app',
      'touch app/__init__.py app/routes.py app/models.py'
    ],
    'Bash': [
      'mkdir my-script',
      'cd my-script',
      'touch script.sh',
      'chmod +x script.sh',
      'echo "#!/bin/bash" > script.sh'
    ]
  };

  const getDefaultPath = (stack: string) => {
    return `C:\\Users\\<USER>\\Documents\\ViceProjects\\${stack.toLowerCase()}`;
  };

  // Update path when stackName changes
  useEffect(() => {
    setProjectPath(getDefaultPath(stackName));
  }, [stackName]);

  const handleLoadingComplete = useCallback(() => {
    setShowLoading(false);
    setIsCreating(false);
    setCurrentCommand('');
    setCommandProgress([]);
    setIsCommandRunning(false);

    // Ensure we're back on the dashboard
    console.log('[PROJECT] ✅ Project creation completed, returning to dashboard');

    toast({
      title: 'Project Ready',
      description: `${projectName} has been created and opened in the IDE. You can continue using the dashboard.`,
    });
  }, [projectName, toast]);

  // Listen for command progress events from Electron
  useEffect(() => {
    if (typeof window !== 'undefined' && window.electronAPI) {
      const handleCommandProgress = (data: any) => {
        console.log('[COMMAND PROGRESS] 📨 Received:', data);

        if (data.status === 'running') {
          setCurrentCommand(data.command);
          setIsCommandRunning(true);
          setCommandProgress(prev => [...prev, `▶️ Running: ${data.command}`]);
        } else if (data.status === 'completed') {
          setCommandProgress(prev => [
            ...prev,
            `✅ Completed: ${data.command}`,
            ...(data.output ? [`   ${data.output}`] : [])
          ]);
          setIsCommandRunning(false);
        } else if (data.status === 'error') {
          setCommandProgress(prev => [...prev, `❌ Error: ${data.command} - ${data.error || 'Unknown error'}`]);
          setIsCommandRunning(false);
        } else if (data.status === 'starting-ide') {
          setCurrentCommand(`Starting IDE for ${data.projectName || 'project'}...`);
          setIsCommandRunning(true);
          setCommandProgress(prev => [...prev, `🚀 Starting IDE and returning to dashboard...`]);

          // Close loading screen and return to dashboard
          setTimeout(() => {
            handleLoadingComplete();
          }, 3000);
        } else if (data.status === 'ide-opened') {
          // IDE has opened, we can fully close the loading
          setCommandProgress(prev => [...prev, `✅ IDE opened successfully! Returning to dashboard...`]);
          setTimeout(() => {
            handleLoadingComplete();
          }, 1000);
        }
      };

      // Listen for the IPC event
      if (window.electronAPI.onCommandProgress) {
        window.electronAPI.onCommandProgress(handleCommandProgress);
      }

      // Cleanup function (if the API supports it)
      return () => {
        // Note: Cleanup will happen when component unmounts
        // The Electron API should handle cleanup automatically
        console.log('[PROJECT] Cleaning up command progress listener');
      };
    }
  }, [handleLoadingComplete]);

  // Validate and format project name
  const validateProjectName = (name: string) => {
    // Remove spaces and convert to lowercase with hyphens
    const formatted = name.trim().toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
    return formatted;
  };

  // Handle project name input with real-time validation
  const handleProjectNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Allow typing but show preview of formatted name
    setProjectName(value);

    // Show warning if name contains invalid characters
    if (value && value !== validateProjectName(value)) {
      // Could add a warning state here if needed
    }
  };

  const handleCreate = async () => {
    if (!projectName.trim()) {
      toast({
        title: 'Project name required',
        description: 'Please enter a name for your project.',
        variant: 'destructive',
      });
      return;
    }

    // Validate project name constraints
    const formattedProjectName = validateProjectName(projectName);
    if (!formattedProjectName) {
      toast({
        title: 'Invalid project name',
        description: 'Project name must contain at least one letter or number.',
        variant: 'destructive',
      });
      return;
    }

    if (formattedProjectName !== projectName.trim().toLowerCase().replace(/\s+/g, '-')) {
      toast({
        title: 'Project name updated',
        description: `Project name changed to: ${formattedProjectName}`,
      });
      setProjectName(formattedProjectName);
    }

    setIsCreating(true);
    setShowLoading(true);

    try {
      // Create project using Electron API
      if (window.electronAPI && window.electronAPI.createProjectFiles) {
        const result = await window.electronAPI.createProjectFiles({
          projectName: formattedProjectName,
          projectPath,
          description: projectDescription,
          stackName
        } as any);

        if (result && result.success) {
          const finalProjectPath = (result as any).actualPath || projectPath;
          const projectFiles = Array.isArray((result as any).files) ? (result as any).files : [];

          // After commands complete, load project in IDE (but don't wait for it)
          if (window.electronAPI && window.electronAPI.loadProjectInIde) {
            console.log('[PROJECT] Loading project in IDE via Electron API');
            // Don't await this - let it happen in background while dashboard returns to normal
            window.electronAPI.loadProjectInIde({
              projectName: formattedProjectName,
              projectPath: finalProjectPath,
              files: projectFiles,
              stackName
            }).catch(error => {
              console.error('[PROJECT] Error loading project in IDE:', error);
              // Fallback: direct navigation to IDE
              const ideUrl = `http://localhost:9003?project=${encodeURIComponent(formattedProjectName)}&path=${encodeURIComponent(finalProjectPath)}`;
              window.open(ideUrl, '_blank');
            });
          } else {
            console.log('[PROJECT] Electron API not available, opening IDE in new window');
            // Fallback: open IDE in new window
            const ideUrl = `http://localhost:9003?project=${encodeURIComponent(formattedProjectName)}&path=${encodeURIComponent(finalProjectPath)}`;
            window.open(ideUrl, '_blank');
          }

          // Success! Project created and IDE is opening
          console.log('[PROJECT] ✅ Project created successfully, IDE opening in background');

          toast({
            title: 'Project Created Successfully',
            description: `${formattedProjectName} has been created and is opening in the IDE.`,
          });

          // Reset form
          setProjectName('');
          setProjectDescription('');
          
          // Note: Recent projects are automatically added via IPC events
          // Note: Loading screen will auto-close when IDE opens
        } else {
          throw new Error(result.message || 'Failed to create project');
        }
      } else {
        throw new Error('Electron API not available');
      }
    } catch (error) {
      console.error('[PROJECT] Error creating project:', error);
      setIsCreating(false);
      setShowLoading(false);
      
      toast({
        title: 'Failed to create project',
        description: error instanceof Error ? error.message : 'An error occurred while creating the project.',
        variant: 'destructive',
      });
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[550px] md:max-w-[600px] rounded-xl overflow-hidden">
          {/* Logo at the top center */}
          <div className="flex justify-center mb-2">
            <div className="relative h-20 w-40">
              <Image
                src="/logo.png"
                alt="VICE Logo"
                fill
                style={{ objectFit: 'contain' }}
                priority
              />
            </div>
          </div>

          {/* Content container with animation */}
          <div className="relative overflow-hidden">
            {/* Project Form */}
            <div
              className={`transition-transform duration-300 ease-in-out ${
                showCommands ? 'translate-x-[-100%]' : 'translate-x-0'
              }`}
            >
              <DialogHeader className="mb-[5] pb-2">
                <DialogTitle className='text-center'>Create New Project</DialogTitle>
                <DialogDescription className='text-center text-sm text-muted-foreground'>
                  Create a new {stackName} project. Fill in the details below to get started.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4 px-2">
                <div className="flex flex-col gap-1.5">
                  <Label htmlFor="name" className="text-sm font-medium">
                    Name
                  </Label>
                  <Input
                    id="name"
                    value={projectName}
                    onChange={handleProjectNameChange}
                    className="w-full"
                    placeholder="my-awesome-project"
                    autoFocus
                  />
                  <p className="text-xs text-muted-foreground">
                    No spaces or caps allowed. Use hyphens for multi-word projects.
                    {projectName && projectName !== validateProjectName(projectName) && (
                      <span className="text-orange-600 ml-1">
                        → Will be: {validateProjectName(projectName)}
                      </span>
                    )}
                  </p>
                </div>
                <div className="flex flex-col gap-1.5">
                  <Label htmlFor="path" className="text-sm font-medium">
                    Path
                  </Label>
                  <Input
                    id="path"
                    value={projectPath}
                    onChange={(e) => setProjectPath(e.target.value)}
                    className="w-full"
                    placeholder="Documents/ViceProjects/"
                  />
                </div>
                <div className="flex flex-col gap-1.5">
                  <Label htmlFor="description" className="text-sm font-medium">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    value={projectDescription}
                    onChange={(e) => setProjectDescription(e.target.value)}
                    className="w-full"
                    placeholder="A brief description of your project"
                    rows={3}
                  />
                </div>
              </div>
              <div className="flex flex-col">
                <div className="flex items-center mt-2 mb-4 ml-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs text-muted-foreground flex items-center gap-1 p-0 h-auto"
                    onClick={() => setShowCommands(true)}
                  >
                    <Terminal className="h-3.5 w-3.5 mr-1" />
                    See Starting Commands
                    <ChevronRight className="h-3.5 w-3.5 ml-1" />
                  </Button>
                </div>

                <DialogFooter className="flex justify-end items-center">
                  <div className="flex gap-2">
                    <Button variant="outline" onClick={() => onOpenChange(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreate} disabled={isCreating}>
                      {isCreating ? 'Creating...' : 'Create Project'}
                    </Button>
                  </div>
                </DialogFooter>
              </div>
            </div>

            {/* Starting Commands */}
            <div
              className={`absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out ${
                showCommands ? 'translate-x-0' : 'translate-x-[100%]'
              }`}
            >
              <div className="mb-4 ml-3">
                <Button
                  variant="ghost"
                  size="sm"
                  className="pl-0 flex items-center gap-1"
                  onClick={() => setShowCommands(false)}
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back to Project Form
                </Button>
              </div>

              <DialogHeader>
                <DialogTitle>Starting Commands for {stackName}</DialogTitle>
                <DialogDescription>
                  Use these commands to set up your {stackName} project manually.
                </DialogDescription>
              </DialogHeader>

              <div className="py-4 px-3">
                <div className="bg-muted p-4 rounded-md font-mono text-xs sm:text-sm overflow-auto max-h-[250px]">
                  {startingCommands[stackName as keyof typeof startingCommands]?.map((command, index) => (
                    <div key={index} className="flex items-start mb-2">
                      <span className="text-muted-foreground mr-2 flex-shrink-0">$</span>
                      <span className="break-all">{command}</span>
                    </div>
                  )) || (
                    <div className="text-muted-foreground">No commands available for {stackName}.</div>
                  )}
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => onOpenChange(false)}>
                  Close
                </Button>
              </DialogFooter>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <ProjectCreationLoading
        isVisible={showLoading}
        stackName={stackName}
        projectName={projectName}
        onComplete={handleLoadingComplete}
        currentCommand={currentCommand}
        commandProgress={commandProgress}
        isCommandRunning={isCommandRunning}
      />
    </>
  );
}
