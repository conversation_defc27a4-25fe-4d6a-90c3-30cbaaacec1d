'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { ArrowLeft, ChevronRight, Terminal } from 'lucide-react';
import ProjectCreationLoading from './project-creation-loading';

// Project file generation function
function generateProjectFiles(stackName: string, projectName: string, description: string) {
  const files: Array<{ name: string; content: string }> = [];

  switch (stackName.toLowerCase()) {
    case 'mern':
      files.push(
        {
          name: 'package.json',
          content: `{
  "name": "${projectName.toLowerCase().replace(/\s+/g, '-')}",
  "version": "1.0.0",
  "description": "${description}",
  "main": "server/index.js",
  "scripts": {
    "start": "node server/index.js",
    "dev": "concurrently \\"npm run server\\" \\"npm run client\\"",
    "server": "nodemon server/index.js",
    "client": "cd client && npm start",
    "build": "cd client && npm run build"
  },
  "dependencies": {
    "express": "^4.18.2",
    "mongoose": "^7.5.0",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1"
  },
  "devDependencies": {
    "nodemon": "^3.0.1",
    "concurrently": "^8.2.0"
  }
}`
        },
        {
          name: 'server/index.js',
          content: `const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// MongoDB connection
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/${projectName.toLowerCase()}', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to ${projectName} API!' });
});

app.listen(PORT, () => {
  console.log(\`Server running on port \${PORT}\`);
});`
        },
        {
          name: '.env',
          content: `PORT=5000
MONGODB_URI=mongodb://localhost:27017/${projectName.toLowerCase()}
NODE_ENV=development`
        },
        {
          name: 'README.md',
          content: `# ${projectName}

${description}

## MERN Stack Application

### Setup Instructions

1. Install dependencies:
   \`\`\`bash
   npm install
   \`\`\`

2. Create React client:
   \`\`\`bash
   npx create-react-app client
   cd client
   npm install axios
   cd ..
   \`\`\`

3. Start development servers:
   \`\`\`bash
   npm run dev
   \`\`\`

### Project Structure
- \`server/\` - Express.js backend
- \`client/\` - React.js frontend
- \`.env\` - Environment variables

### Technologies Used
- MongoDB
- Express.js
- React.js
- Node.js`
        }
      );
      break;

    case 'nextjs':
      files.push(
        {
          name: 'package.json',
          content: `{
  "name": "${projectName.toLowerCase().replace(/\s+/g, '-')}",
  "version": "0.1.0",
  "description": "${description}",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "next": "14.0.0",
    "react": "^18",
    "react-dom": "^18"
  },
  "devDependencies": {
    "typescript": "^5",
    "@types/node": "^20",
    "@types/react": "^18",
    "@types/react-dom": "^18",
    "eslint": "^8",
    "eslint-config-next": "14.0.0"
  }
}`
        },
        {
          name: 'app/page.tsx',
          content: `export default function Home() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-24">
      <div className="z-10 max-w-5xl w-full items-center justify-between font-mono text-sm">
        <h1 className="text-4xl font-bold text-center mb-8">
          Welcome to ${projectName}
        </h1>
        <p className="text-center text-lg">
          ${description}
        </p>
      </div>
    </main>
  );
}`
        },
        {
          name: 'app/layout.tsx',
          content: `import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: '${projectName}',
  description: '${description}',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>{children}</body>
    </html>
  )
}`
        },
        {
          name: 'app/globals.css',
          content: `@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}`
        },
        {
          name: 'README.md',
          content: `# ${projectName}

${description}

## Next.js Application

### Getting Started

1. Install dependencies:
   \`\`\`bash
   npm install
   \`\`\`

2. Run the development server:
   \`\`\`bash
   npm run dev
   \`\`\`

3. Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### Technologies Used
- Next.js 14
- React 18
- TypeScript
- Tailwind CSS`
        }
      );
      break;

    case 'flask':
      files.push(
        {
          name: 'app.py',
          content: `from flask import Flask, jsonify, request
from flask_cors import CORS
import os

app = Flask(__name__)
CORS(app)

@app.route('/')
def home():
    return jsonify({
        'message': 'Welcome to ${projectName} API!',
        'description': '${description}'
    })

@app.route('/api/health')
def health():
    return jsonify({'status': 'healthy'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))`
        },
        {
          name: 'requirements.txt',
          content: `Flask==2.3.3
Flask-CORS==4.0.0
python-dotenv==1.0.0`
        },
        {
          name: '.env',
          content: `FLASK_APP=app.py
FLASK_ENV=development
PORT=5000`
        },
        {
          name: 'README.md',
          content: `# ${projectName}

${description}

## Flask Application

### Setup Instructions

1. Create virtual environment:
   \`\`\`bash
   python -m venv venv
   \`\`\`

2. Activate virtual environment:
   \`\`\`bash
   # Windows
   venv\\Scripts\\activate
   # macOS/Linux
   source venv/bin/activate
   \`\`\`

3. Install dependencies:
   \`\`\`bash
   pip install -r requirements.txt
   \`\`\`

4. Run the application:
   \`\`\`bash
   python app.py
   \`\`\`

### Technologies Used
- Flask
- Python
- Flask-CORS`
        }
      );
      break;

    default:
      // Generic project
      files.push(
        {
          name: 'README.md',
          content: `# ${projectName}

${description}

## Getting Started

This is a ${stackName} project. Add your setup instructions here.

### Technologies Used
- ${stackName}`
        },
        {
          name: 'index.js',
          content: `// ${projectName}
// ${description}

console.log('Welcome to ${projectName}!');`
        }
      );
  }

  return files;
}

interface CreateProjectModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  stackName: string;
  projectTitle?: string;
  templateProjectId?: number;
}

export function CreateProjectModal({
  open,
  onOpenChange,
  stackName,
  projectTitle = '',
  templateProjectId,
}: CreateProjectModalProps) {
  const { toast } = useToast();
  const [projectName, setProjectName] = useState(projectTitle);

  // Set default path based on stack name
  const getDefaultPath = (stack: string) => {
    const stackFolder = stack.toLowerCase().replace(/\s+/g, '').replace('stack', '');
    return `C:\\Users\\<USER>\\Documents\\ViceProjects\\${stackFolder}`;
  };

  const [projectPath, setProjectPath] = useState(getDefaultPath(stackName));
  const [projectDescription, setProjectDescription] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [showCommands, setShowCommands] = useState(false);
  const [showLoading, setShowLoading] = useState(false);
  const [currentCommand, setCurrentCommand] = useState('');
  const [commandProgress, setCommandProgress] = useState<string[]>([]);
  const [isCommandRunning, setIsCommandRunning] = useState(false);

  // Update path when stackName changes
  React.useEffect(() => {
    setProjectPath(getDefaultPath(stackName));
  }, [stackName]);

  // Listen for command progress events from Electron
  React.useEffect(() => {
    if (typeof window !== 'undefined' && window.electronAPI) {
      const handleCommandProgress = (event: any, data: any) => {
        console.log('[COMMAND PROGRESS] 📨 Received:', data);

        if (data.status === 'running') {
          setCurrentCommand(data.command);
          setIsCommandRunning(true);
          setCommandProgress(prev => [...prev, `▶️ Starting: ${data.command}`]);
        } else if (data.status === 'completed') {
          setCommandProgress(prev => [...prev, `✅ Completed: ${data.command}`]);
          setIsCommandRunning(false);
        } else if (data.status === 'error') {
          setCommandProgress(prev => [...prev, `❌ Error: ${data.command} - ${data.error || 'Unknown error'}`]);
          setIsCommandRunning(false);
        } else if (data.status === 'starting-ide') {
          setCurrentCommand('Starting IDE...');
          setIsCommandRunning(true);
          setCommandProgress(prev => [...prev, `🚀 Starting IDE and returning to dashboard...`]);

          // Close loading screen and return to dashboard
          setTimeout(() => {
            handleLoadingComplete();
          }, 3000);
        } else if (data.status === 'ide-opened') {
          // IDE has opened, we can fully close the loading
          setCommandProgress(prev => [...prev, `✅ IDE opened successfully! Returning to dashboard...`]);
          setTimeout(() => {
            handleLoadingComplete();
          }, 1000);
        }
      };

      // Listen for the IPC event
      if (window.electronAPI.onCommandProgress) {
        window.electronAPI.onCommandProgress(handleCommandProgress);
      }

      // Cleanup listener on unmount
      return () => {
        if (window.electronAPI.removeCommandProgressListener) {
          window.electronAPI.removeCommandProgressListener(handleCommandProgress);
        }
      };
    }
  }, []);

  // Starting commands for different stacks
  const startingCommands = {
    'Mern': [
      'npx create-react-app my-project',
      'cd my-project',
      'npm install express mongoose cors dotenv',
      'mkdir server',
      'touch server/index.js server/models/User.js server/routes/api.js'
    ],
    'Pern': [
      'npx create-react-app my-project',
      'cd my-project',
      'npm install express pg cors dotenv',
      'mkdir server',
      'touch server/index.js server/db.js server/routes/api.js'
    ],
    'NextJS': [
      'npx create-next-app@latest my-project',
      'cd my-project',
      'npm install'
    ],
    'Django': [
      'python -m venv venv',
      'venv\\Scripts\\activate',
      'pip install django djangorestframework',
      'django-admin startproject my-project .',
      'python manage.py startapp my-app'
    ],
    'Flask': [
      'python -m venv venv',
      'venv\\Scripts\\activate',
      'pip install flask flask-sqlalchemy flask-cors',
      'mkdir app',
      'touch app/__init__.py app/routes.py app/models.py'
    ],
    'Bash': [
      'mkdir my-project',
      'cd my-project',
      'mkdir -p config logs temp',
      'touch main.sh config/settings.conf logs/app.log',
      'touch README.md .gitignore',
      'chmod +x main.sh',
      'echo "#!/bin/bash" > main.sh',
      'echo "# Project: my-project" >> main.sh',
      'echo "# Description: Bash utility script" >> main.sh',
      'echo "" >> main.sh',
      'echo "set -euo pipefail" >> main.sh'
    ]
  };

  const handleCreate = async () => {
    if (!projectName.trim()) {
      toast({
        title: 'Project name required',
        description: 'Please enter a name for your project.',
        variant: 'destructive',
      });
      return;
    }

    // Validate project name format
    const nameRegex = /^[a-z0-9-]+$/;
    if (!nameRegex.test(projectName)) {
      toast({
        title: 'Invalid project name',
        description: 'Project name can only contain lowercase letters, numbers, and hyphens.',
        variant: 'destructive',
      });
      return;
    }

    // Prevent names starting or ending with hyphen
    if (projectName.startsWith('-') || projectName.endsWith('-')) {
      toast({
        title: 'Invalid project name',
        description: 'Project name cannot start or end with a hyphen.',
        variant: 'destructive',
      });
      return;
    }

    // Prevent consecutive hyphens
    if (projectName.includes('--')) {
      toast({
        title: 'Invalid project name',
        description: 'Project name cannot contain consecutive hyphens.',
        variant: 'destructive',
      });
      return;
    }

    if (!projectPath.trim()) {
      toast({
        title: 'Project path required',
        description: 'Please enter a path for your project.',
        variant: 'destructive',
      });
      return;
    }

    setIsCreating(true);

    try {
      // Check if electron API is available
      if (typeof window !== 'undefined' && window.electronAPI) {
        // Close the modal and show loading screen
        onOpenChange(false);
        setShowLoading(true);

        // Execute actual stack commands and create project
        const result = await window.electronAPI.executeStackCommands({
          stackName,
          projectName,
          projectPath: projectPath, // Let electron handle path construction
          description: projectDescription,
          templateProjectId // Pass the template project ID for task loading
        });

        if (result.success) {
          const finalProjectPath = result.actualPath || projectPath;

          // After commands complete, load project in IDE (but don't wait for it)
          if (window.electronAPI && window.electronAPI.loadProjectInIde) {
            console.log('[PROJECT] Loading project in IDE via Electron API');
            // Don't await this - let it happen in background while dashboard returns to normal
            window.electronAPI.loadProjectInIde({
              projectName,
              projectPath: finalProjectPath,
              files: result.files || [],
              stackName
            }).catch(error => {
              console.error('[PROJECT] Error loading project in IDE:', error);
              // Fallback: direct navigation to IDE
              const ideUrl = `http://localhost:9003?project=${encodeURIComponent(projectName)}&path=${encodeURIComponent(finalProjectPath)}`;
              window.open(ideUrl, '_blank');
            });
          } else {
            console.log('[PROJECT] Electron API not available, opening IDE in new window');
            // Fallback: open IDE in new window
            const ideUrl = `http://localhost:9003?project=${encodeURIComponent(projectName)}&path=${encodeURIComponent(finalProjectPath)}`;
            window.open(ideUrl, '_blank');
          }

          // Add to recent projects
          if (typeof window !== 'undefined' && (window as any).addRecentProject) {
            console.log('[RECENT] 📝 Adding project to recent projects:', {
              title: projectName,
              path: finalProjectPath,
              stackName: stackName
            });

            (window as any).addRecentProject({
              title: projectName,
              tag: `@${stackName.toLowerCase()}`,
              path: finalProjectPath,
              stackName: stackName
            });

            console.log('[RECENT] ✅ Project added to recent projects successfully');
          } else {
            console.log('[RECENT] ⚠️ addRecentProject function not available');
          }

          // Success! Project created and IDE is opening
          console.log('[PROJECT] ✅ Project created successfully, IDE opening in background');

          toast({
            title: 'Project Created Successfully',
            description: `${projectName} has been created and is opening in the IDE.`,
          });

          // Reset form
          setProjectName('');
          setProjectDescription('');

          // Note: Recent projects are automatically added via IPC events
          // Note: Loading screen will auto-close when IDE opens
        } else {
          throw new Error(result.message || 'Failed to create project');
        }
      } else {
        // Fallback for web version - show loading simulation
        onOpenChange(false);
        setShowLoading(true);
      }
    } catch (error) {
      console.error('Error creating project:', error);
      setShowLoading(false);
      toast({
        title: 'Failed to create project',
        description: error instanceof Error ? error.message : 'An error occurred while creating the project.',
        variant: 'destructive',
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleLoadingComplete = () => {
    setShowLoading(false);
    setIsCreating(false);
    setCurrentCommand('');
    setCommandProgress([]);
    setIsCommandRunning(false);

    // Ensure we're back on the dashboard
    console.log('[PROJECT] ✅ Project creation completed, returning to dashboard');

    toast({
      title: 'Project Ready',
      description: `${projectName} has been created and opened in the IDE. You can continue using the dashboard.`,
    });
  };

  return (
    <>
      {/* Loading Screen */}
      <ProjectCreationLoading
        isVisible={showLoading}
        stackName={stackName}
        projectName={projectName}
        onComplete={handleLoadingComplete}
        currentCommand={currentCommand}
        commandProgress={commandProgress}
        isCommandRunning={isCommandRunning}
      />

      {/* Project Creation Modal */}
      <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] rounded-xl overflow-hidden">
        {/* Logo at the top center */}
        <div className="flex justify-center mb-4">
          <div className="relative h-20 w-20">
            <Image
              src="/logo.png"
              alt="VICE Logo"
              fill
              style={{ objectFit: 'contain' }}
              priority
            />
          </div>
        </div>

        {/* Content container with animation */}
        <div className="relative overflow-hidden">
          {/* Project Form */}
          <div
            className={`transition-transform duration-300 ease-in-out ${
              showCommands ? 'translate-x-[-100%]' : 'translate-x-0'
            }`}
          >
            <DialogHeader>
              <DialogTitle>Create New Project</DialogTitle>
              <DialogDescription>
                Create a new {stackName} project. Fill in the details below to get started.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-5 items-center gap-4">
                <Label htmlFor="name" className="text-left col-span-1">
                  Name
                </Label>
                <div className="col-span-4 space-y-1">
                  <Input
                    id="name"
                    value={projectName}
                    onChange={(e) => {
                      // Only allow lowercase letters, numbers, and hyphens
                      const value = e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '');
                      setProjectName(value);
                    }}
                    placeholder="my-awesome-project"
                    autoFocus
                    pattern="[a-z0-9-]+"
                    title="Only lowercase letters, numbers, and hyphens are allowed"
                  />
                  <p className="text-xs text-muted-foreground">
                    Use lowercase letters, numbers, and hyphens only. No spaces or special characters.
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-5 items-center gap-4">
                <Label htmlFor="path" className="text-left col-span-1">
                  Path
                </Label>
                <Input
                  id="path"
                  value={projectPath}
                  onChange={(e) => setProjectPath(e.target.value)}
                  className="col-span-4"
                  placeholder={getDefaultPath(stackName)}
                />
              </div>
              <div className="grid grid-cols-5 items-center gap-4">
                <Label htmlFor="description" className="text-left col-span-1">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                  className="col-span-4"
                  placeholder="A brief description of your project"
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter className="flex justify-between items-center">
              <Button
                variant="ghost"
                size="sm"
                className="text-xs text-muted-foreground flex items-center gap-1"
                onClick={() => setShowCommands(true)}
              >
                <Terminal className="h-3.5 w-3.5" />
                See Starting Commands
                <ChevronRight className="h-3.5 w-3.5" />
              </Button>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => onOpenChange(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreate} disabled={isCreating}>
                  {isCreating ? 'Creating...' : 'Create Project'}
                </Button>
              </div>
            </DialogFooter>
          </div>

          {/* Starting Commands */}
          <div
            className={`absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out ${
              showCommands ? 'translate-x-0' : 'translate-x-[100%]'
            }`}
          >
            <div className="mb-4">
              <Button
                variant="ghost"
                size="sm"
                className="pl-0 flex items-center gap-1"
                onClick={() => setShowCommands(false)}
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Project Form
              </Button>
            </div>

            <DialogHeader>
              <DialogTitle>Starting Commands for {stackName}</DialogTitle>
              <DialogDescription>
                Use these commands to set up your {stackName} project manually.
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <div className="bg-muted p-4 rounded-md font-mono text-sm overflow-auto max-h-[300px]">
                {startingCommands[stackName as keyof typeof startingCommands]?.map((command, index) => (
                  <div key={index} className="flex items-start mb-2">
                    <span className="text-muted-foreground mr-2">$</span>
                    <span>{command}</span>
                  </div>
                )) || (
                  <div className="text-muted-foreground">No commands available for {stackName}.</div>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Close
              </Button>
            </DialogFooter>
          </div>
        </div>
      </DialogContent>
    </Dialog>
    </>
  );
}
