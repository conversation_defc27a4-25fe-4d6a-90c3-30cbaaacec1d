'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, Terminal, CheckCircle, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProjectCreationLoadingProps {
  isVisible: boolean;
  stackName: string;
  projectName: string;
  onComplete: () => void;
  currentCommand?: string;
  commandProgress?: string[];
  isCommandRunning?: boolean;
}

interface CommandStep {
  command: string;
  status: 'pending' | 'running' | 'completed' | 'error' | 'starting-ide';
  output?: string;
}

const ProjectCreationLoading: React.FC<ProjectCreationLoadingProps> = ({
  isVisible,
  stackName,
  projectName,
  onComplete,
  currentCommand = '',
  commandProgress = [],
  isCommandRunning = false
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [steps, setSteps] = useState<CommandStep[]>([]);
  const [isStartingIde, setIsStartingIde] = useState(false);

  // Generate commands based on stack
  const generateCommands = (stack: string, name: string): CommandStep[] => {
    const projectNameKebab = name.toLowerCase().replace(/\s+/g, '-');

    switch (stack.toLowerCase()) {
      case 'mern':
      case 'mern stack':
        return [
          { command: `npx create-react-app ${projectNameKebab}`, status: 'pending' },
          { command: `cd ${projectNameKebab}`, status: 'pending' },
          { command: 'npm install express mongoose cors dotenv', status: 'pending' },
          { command: 'mkdir server', status: 'pending' },
          { command: 'Creating server files...', status: 'pending' }
        ];

      case 'nextjs':
      case 'next.js':
        return [
          { command: `npx create-next-app@latest ${projectNameKebab} --typescript --tailwind --eslint`, status: 'pending' },
          { command: `cd ${projectNameKebab}`, status: 'pending' },
          { command: 'npm install', status: 'pending' }
        ];

      case 'flask':
        return [
          { command: `mkdir ${projectNameKebab}`, status: 'pending' },
          { command: `cd ${projectNameKebab}`, status: 'pending' },
          { command: 'python -m venv venv', status: 'pending' },
          { command: 'venv\\Scripts\\activate', status: 'pending' },
          { command: 'pip install flask flask-cors python-dotenv', status: 'pending' },
          { command: 'Creating Flask application files...', status: 'pending' }
        ];

      case 'django':
        return [
          { command: `mkdir ${projectNameKebab}`, status: 'pending' },
          { command: `cd ${projectNameKebab}`, status: 'pending' },
          { command: 'python -m venv venv', status: 'pending' },
          { command: 'venv\\Scripts\\activate', status: 'pending' },
          { command: 'pip install django djangorestframework', status: 'pending' },
          { command: `django-admin startproject ${projectNameKebab} .`, status: 'pending' },
          { command: 'python manage.py startapp main', status: 'pending' }
        ];

      default:
        return [
          { command: `mkdir ${projectNameKebab}`, status: 'pending' },
          { command: `cd ${projectNameKebab}`, status: 'pending' },
          { command: 'Initializing project...', status: 'pending' }
        ];
    }
  };

  useEffect(() => {
    if (isVisible) {
      const commands = generateCommands(stackName, projectName);
      setSteps(commands);
      setCurrentStep(0);
      setIsStartingIde(false);

      // Listen for real command progress if electron API is available
      if (typeof window !== 'undefined' && window.electronAPI) {
        const handleCommandProgress = (progressData: {
          currentStep: number;
          totalSteps: number;
          command: string;
          status: 'running' | 'completed' | 'starting-ide' | 'error';
          error?: string;
        }) => {
          if (progressData.status === 'starting-ide') {
            setIsStartingIde(true);
            setTimeout(() => {
              onComplete();
            }, 3000);
          } else if (progressData.status === 'error') {
            console.error('Command execution error:', progressData.error);
            onComplete();
          } else {
            setCurrentStep(progressData.currentStep);
            setSteps(prev => prev.map((step, index) => {
              if (index === progressData.currentStep) {
                return { ...step, status: progressData.status, command: progressData.command };
              } else if (index < progressData.currentStep) {
                return { ...step, status: 'completed' };
              }
              return step;
            }));
          }
        };

        window.electronAPI.onCommandProgress(handleCommandProgress);

        // Cleanup function
        return () => {
          if (window.electronAPI) {
            window.electronAPI.removeAllListeners('command-progress');
          }
        };
      } else {
        // Fallback simulation for web version
        simulateCommandExecution(commands);
      }
    }
  }, [isVisible, stackName, projectName, onComplete]);

  const simulateCommandExecution = async (commands: CommandStep[]) => {
    for (let i = 0; i < commands.length; i++) {
      setCurrentStep(i);

      // Update step to running
      setSteps(prev => prev.map((step, index) =>
        index === i ? { ...step, status: 'running' } : step
      ));

      // Simulate command execution time
      const executionTime = commands[i].command.includes('npx create') ? 8000 :
                           commands[i].command.includes('npm install') ? 5000 :
                           commands[i].command.includes('pip install') ? 4000 : 2000;

      await new Promise(resolve => setTimeout(resolve, executionTime));

      // Update step to completed
      setSteps(prev => prev.map((step, index) =>
        index === i ? { ...step, status: 'completed', output: 'Success' } : step
      ));
    }

    // Show "Starting IDE" phase
    setIsStartingIde(true);
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Complete the process
    onComplete();
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center"
         style={{ backgroundColor: 'hsl(var(--background))' }}>
      <Card className="w-full max-w-2xl mx-4 shadow-2xl border-2">
        <CardContent className="p-8">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <div className="relative">
                <Loader2 className="h-8 w-8 animate-spin text-primary mr-3" />
                {currentCommand.includes('Starting IDE') && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                )}
              </div>
              <h2 className="text-2xl font-bold">
                {currentCommand.includes('Starting IDE') ? 'Opening IDE...' : `Creating "${projectName}"`}
              </h2>
            </div>
            <p className="text-muted-foreground">
              {currentCommand.includes('Starting IDE')
                ? 'Your project is ready! Opening in the IDE and returning to dashboard...'
                : `Setting up your ${stackName} project with all dependencies`
              }
            </p>

            {/* Project Name Badge */}
            <div className="mt-4 inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium">
              <Terminal className="h-4 w-4 mr-2" />
              Project: {projectName}
            </div>
          </div>

          {!isStartingIde && (
            <div className="space-y-4">
              {/* Current Command Display */}
              {currentCommand && (
                <div className={cn(
                  "flex items-center p-4 rounded-lg border transition-all duration-500 transform",
                  isCommandRunning
                    ? "bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800 scale-105 shadow-lg"
                    : "bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800 scale-100"
                )}>
                  <div className="flex items-center mr-3">
                    {isCommandRunning ? (
                      <div className="relative">
                        <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
                        <div className="absolute inset-0 h-5 w-5 border-2 border-blue-200 rounded-full animate-ping" />
                      </div>
                    ) : (
                      <div className="relative">
                        <CheckCircle className="h-5 w-5 text-green-500" />
                        <div className="absolute inset-0 h-5 w-5 bg-green-500 rounded-full animate-ping opacity-20" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center">
                      <Terminal className="h-4 w-4 mr-2 text-muted-foreground" />
                      <code className="text-sm font-mono font-medium">
                        {isCommandRunning ? '🔄 Running: ' : '✅ Completed: '}{currentCommand}
                      </code>
                    </div>
                    {isCommandRunning && (
                      <div className="mt-2 w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700">
                        <div className="bg-blue-500 h-1.5 rounded-full animate-pulse" style={{ width: '60%' }} />
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Command Progress Log */}
              {commandProgress.length > 0 && (
                <div className="bg-black/90 rounded-lg border border-gray-700 p-4 max-h-64 overflow-y-auto">
                  <h4 className="font-medium text-sm mb-3 flex items-center text-green-400">
                    <Terminal className="h-4 w-4 mr-2" />
                    Terminal Output:
                    <div className="ml-auto flex items-center space-x-1">
                      <div className="w-2 h-2 bg-red-500 rounded-full" />
                      <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                    </div>
                  </h4>
                  <div className="space-y-1 text-xs font-mono">
                    {commandProgress.map((line, index) => (
                      <div
                        key={index}
                        className={cn(
                          "text-gray-300 transition-all duration-300 transform",
                          line.includes('▶️') && "text-blue-400 font-medium animate-pulse",
                          line.includes('✅') && "text-green-400 font-medium",
                          line.includes('❌') && "text-red-400 font-medium",
                          line.includes('🚀') && "text-purple-400 font-medium animate-bounce"
                        )}
                        style={{
                          animationDelay: `${index * 100}ms`,
                          opacity: index === commandProgress.length - 1 ? 1 : 0.8
                        }}
                      >
                        <span className="text-green-400">$</span> {line}
                      </div>
                    ))}
                    {isCommandRunning && (
                      <div className="flex items-center text-green-400 animate-pulse">
                        <span className="mr-2">$</span>
                        <div className="w-2 h-4 bg-green-400 animate-pulse" />
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Fallback to simulated steps if no real progress */}
              {commandProgress.length === 0 && (
                <div className="space-y-4">
                  {steps.map((step, index) => (
                    <div key={index} className={cn(
                      "flex items-center p-3 rounded-lg border transition-all duration-300",
                      step.status === 'running' && "bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800",
                      step.status === 'completed' && "bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800",
                      step.status === 'pending' && "bg-muted/50 border-muted"
                    )}>
                      <div className="flex items-center mr-3">
                        {step.status === 'running' && (
                          <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                        )}
                        {step.status === 'completed' && (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        )}
                        {step.status === 'pending' && (
                          <Clock className="h-4 w-4 text-muted-foreground" />
                        )}
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center">
                          <Terminal className="h-4 w-4 mr-2 text-muted-foreground" />
                          <code className="text-sm font-mono">{step.command}</code>
                        </div>
                        {step.output && (
                          <p className="text-xs text-green-600 mt-1 ml-6">{step.output}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {isStartingIde && (
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                <div className="animate-pulse bg-primary/20 rounded-full p-4">
                  <Terminal className="h-8 w-8 text-primary" />
                </div>
              </div>
              <p className="text-lg font-medium mb-2">Opening ViceIDE...</p>
              <p className="text-sm text-muted-foreground">
                Your project will be loaded automatically
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ProjectCreationLoading;
