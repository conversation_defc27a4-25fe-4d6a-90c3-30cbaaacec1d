"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/stack/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/create-project-modal.tsx":
/*!*********************************************************!*\
  !*** ./src/components/project/create-project-modal.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateProjectModal: () => (/* binding */ CreateProjectModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* __next_internal_client_entry_do_not_use__ CreateProjectModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CreateProjectModal(param) {\n    let { open, onOpenChange, stackName, projectTitle = '' } = param;\n    var _startingCommands_stackName;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [projectName, setProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(projectTitle);\n    const [projectPath, setProjectPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\ViceProjects\\\\\".concat(stackName.toLowerCase()));\n    const [projectDescription, setProjectDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCommands, setShowCommands] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLoading, setShowLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentCommand, setCurrentCommand] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [commandProgress, setCommandProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCommandRunning, setIsCommandRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Update path when stackName changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateProjectModal.useEffect\": ()=>{\n            setProjectPath(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\ViceProjects\\\\\".concat(stackName.toLowerCase()));\n        }\n    }[\"CreateProjectModal.useEffect\"], [\n        stackName\n    ]);\n    // Validate and format project name (no spaces, no caps, use hyphens)\n    const validateProjectName = (name)=>{\n        return name.trim().toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n    };\n    // Handle project name input with real-time validation\n    const handleProjectNameChange = (e)=>{\n        const value = e.target.value;\n        setProjectName(value);\n    };\n    // Starting commands for different stacks\n    const startingCommands = {\n        'Mern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express mongoose cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/models/User.js server/routes/api.js'\n        ],\n        'Pern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express pg cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/db.js server/routes/api.js'\n        ],\n        'NextJS': [\n            'npx create-next-app@latest my-app',\n            'cd my-app',\n            'npm install'\n        ],\n        'Django': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install django djangorestframework',\n            'django-admin startproject myproject .',\n            'python manage.py startapp myapp'\n        ],\n        'Flask': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install flask flask-sqlalchemy flask-cors',\n            'mkdir app',\n            'touch app/__init__.py app/routes.py app/models.py'\n        ],\n        'Bash': [\n            'mkdir my-script',\n            'cd my-script',\n            'touch script.sh',\n            'chmod +x script.sh',\n            'echo \"#!/bin/bash\" > script.sh'\n        ]\n    };\n    const handleCreate = async ()=>{\n        if (!projectName.trim()) {\n            toast({\n                title: 'Project name required',\n                description: 'Please enter a name for your project.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        // Validate and format project name\n        const formattedProjectName = validateProjectName(projectName);\n        if (!formattedProjectName) {\n            toast({\n                title: 'Invalid project name',\n                description: 'Project name must contain at least one letter or number.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        if (formattedProjectName !== projectName.trim().toLowerCase().replace(/\\s+/g, '-')) {\n            toast({\n                title: 'Project name formatted',\n                description: \"Project name changed to: \".concat(formattedProjectName)\n            });\n            setProjectName(formattedProjectName);\n        }\n        setIsCreating(true);\n        try {\n            // Create project using Electron API - NO FILES PARAMETER\n            if (window.electronAPI && window.electronAPI.createProjectFiles) {\n                const result = await window.electronAPI.createProjectFiles({\n                    projectName: formattedProjectName,\n                    projectPath,\n                    stackName,\n                    description: projectDescription\n                });\n                if (result && result.success) {\n                    const finalProjectPath = result.actualPath || projectPath;\n                    // Open IDE with project - NO FILES PARAMETER\n                    const ideUrl = \"http://localhost:9003?project=\".concat(encodeURIComponent(formattedProjectName), \"&path=\").concat(encodeURIComponent(finalProjectPath));\n                    window.open(ideUrl, '_blank');\n                    toast({\n                        title: 'Project Created Successfully',\n                        description: \"\".concat(formattedProjectName, \" has been created and is opening in the IDE.\")\n                    });\n                    // Reset form and close modal\n                    setProjectName('');\n                    setProjectDescription('');\n                    onOpenChange(false);\n                } else {\n                    throw new Error((result === null || result === void 0 ? void 0 : result.message) || 'Failed to create project');\n                }\n            } else {\n                throw new Error('Electron API not available');\n            }\n        } catch (error) {\n            console.error('[PROJECT] Error creating project:', error);\n            toast({\n                title: 'Failed to create project',\n                description: error instanceof Error ? error.message : 'An error occurred while creating the project.',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"sm:max-w-[550px] md:max-w-[600px] rounded-xl overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-20 w-40\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/logo.png\",\n                            alt: \"VICE Logo\",\n                            fill: true,\n                            style: {\n                                objectFit: 'contain'\n                            },\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-[-100%]' : 'translate-x-0'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                    className: \"mb-[5] pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                            className: \"text-center\",\n                                            children: \"Create New Project\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                            className: \"text-center text-sm text-muted-foreground\",\n                                            children: [\n                                                \"Create a new \",\n                                                stackName,\n                                                \" project. Fill in the details below to get started.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4 py-4 px-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"name\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"name\",\n                                                    value: projectName,\n                                                    onChange: handleProjectNameChange,\n                                                    className: \"w-full\",\n                                                    placeholder: \"my-awesome-project\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: [\n                                                        \"No spaces or caps allowed. Use hyphens for multi-word projects.\",\n                                                        projectName && projectName !== validateProjectName(projectName) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-orange-600 ml-1\",\n                                                            children: [\n                                                                \"→ Will be: \",\n                                                                validateProjectName(projectName)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"path\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Path\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"path\",\n                                                    value: projectPath,\n                                                    onChange: (e)=>setProjectPath(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"Documents/ViceProjects/\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"description\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                    id: \"description\",\n                                                    value: projectDescription,\n                                                    onChange: (e)=>setProjectDescription(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"A brief description of your project\",\n                                                    rows: 3\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-2 mb-4 ml-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-xs text-muted-foreground flex items-center gap-1 p-0 h-auto\",\n                                                onClick: ()=>setShowCommands(true),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3.5 w-3.5 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"See Starting Commands\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-3.5 w-3.5 ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                            className: \"flex justify-end items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>onOpenChange(false),\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        onClick: handleCreate,\n                                                        disabled: isCreating,\n                                                        children: isCreating ? 'Creating...' : 'Create Project'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-0' : 'translate-x-[100%]'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 ml-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"pl-0 flex items-center gap-1\",\n                                        onClick: ()=>setShowCommands(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Project Form\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                            children: [\n                                                \"Starting Commands for \",\n                                                stackName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                            children: [\n                                                \"Use these commands to set up your \",\n                                                stackName,\n                                                \" project manually.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-4 px-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-muted p-4 rounded-md font-mono text-xs sm:text-sm overflow-auto max-h-[250px]\",\n                                        children: ((_startingCommands_stackName = startingCommands[stackName]) === null || _startingCommands_stackName === void 0 ? void 0 : _startingCommands_stackName.map((command, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground mr-2 flex-shrink-0\",\n                                                        children: \"$\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"break-all\",\n                                                        children: command\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                \"No commands available for \",\n                                                stackName,\n                                                \".\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>onOpenChange(false),\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateProjectModal, \"fCLejMUyyFSiJif6mOCBA04DNF8=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = CreateProjectModal;\nvar _c;\n$RefreshReg$(_c, \"CreateProjectModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/create-project-modal.tsx\n"));

/***/ })

});