"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/get-port";
exports.ids = ["vendor-chunks/get-port"];
exports.modules = {

/***/ "(action-browser)/./node_modules/get-port/index.js":
/*!****************************************!*\
  !*** ./node_modules/get-port/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst net = __webpack_require__(/*! net */ \"net\");\n\nclass Locked extends Error {\n\tconstructor(port) {\n\t\tsuper(`${port} is locked`);\n\t}\n}\n\nconst lockedPorts = {\n\told: new Set(),\n\tyoung: new Set()\n};\n\n// On this interval, the old locked ports are discarded,\n// the young locked ports are moved to old locked ports,\n// and a new young set for locked ports are created.\nconst releaseOldLockedPortsIntervalMs = 1000 * 15;\n\n// Lazily create interval on first use\nlet interval;\n\nconst getAvailablePort = options => new Promise((resolve, reject) => {\n\tconst server = net.createServer();\n\tserver.unref();\n\tserver.on('error', reject);\n\tserver.listen(options, () => {\n\t\tconst {port} = server.address();\n\t\tserver.close(() => {\n\t\t\tresolve(port);\n\t\t});\n\t});\n});\n\nconst portCheckSequence = function * (ports) {\n\tif (ports) {\n\t\tyield * ports;\n\t}\n\n\tyield 0; // Fall back to 0 if anything else failed\n};\n\nmodule.exports = async options => {\n\tlet ports;\n\n\tif (options) {\n\t\tports = typeof options.port === 'number' ? [options.port] : options.port;\n\t}\n\n\tif (interval === undefined) {\n\t\tinterval = setInterval(() => {\n\t\t\tlockedPorts.old = lockedPorts.young;\n\t\t\tlockedPorts.young = new Set();\n\t\t}, releaseOldLockedPortsIntervalMs);\n\n\t\t// Does not exist in some environments (Electron, Jest jsdom env, browser, etc).\n\t\tif (interval.unref) {\n\t\t\tinterval.unref();\n\t\t}\n\t}\n\n\tfor (const port of portCheckSequence(ports)) {\n\t\ttry {\n\t\t\tlet availablePort = await getAvailablePort({...options, port}); // eslint-disable-line no-await-in-loop\n\t\t\twhile (lockedPorts.old.has(availablePort) || lockedPorts.young.has(availablePort)) {\n\t\t\t\tif (port !== 0) {\n\t\t\t\t\tthrow new Locked(port);\n\t\t\t\t}\n\n\t\t\t\tavailablePort = await getAvailablePort({...options, port}); // eslint-disable-line no-await-in-loop\n\t\t\t}\n\n\t\t\tlockedPorts.young.add(availablePort);\n\t\t\treturn availablePort;\n\t\t} catch (error) {\n\t\t\tif (!['EADDRINUSE', 'EACCES'].includes(error.code) && !(error instanceof Locked)) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t}\n\n\tthrow new Error('No available ports found');\n};\n\nmodule.exports.makeRange = (from, to) => {\n\tif (!Number.isInteger(from) || !Number.isInteger(to)) {\n\t\tthrow new TypeError('`from` and `to` must be integer numbers');\n\t}\n\n\tif (from < 1024 || from > 65535) {\n\t\tthrow new RangeError('`from` must be between 1024 and 65535');\n\t}\n\n\tif (to < 1024 || to > 65536) {\n\t\tthrow new RangeError('`to` must be between 1024 and 65536');\n\t}\n\n\tif (to < from) {\n\t\tthrow new RangeError('`to` must be greater than or equal to `from`');\n\t}\n\n\tconst generator = function * (from, to) {\n\t\tfor (let port = from; port <= to; port++) {\n\t\t\tyield port;\n\t\t}\n\t};\n\n\treturn generator(from, to);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/get-port/index.js\n");

/***/ })

};
;