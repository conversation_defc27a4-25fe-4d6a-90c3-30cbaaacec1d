"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@js-sdsl";
exports.ids = ["vendor-chunks/@js-sdsl"];
exports.modules = {

/***/ "(action-browser)/./node_modules/@js-sdsl/ordered-map/dist/esm/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/@js-sdsl/ordered-map/dist/esm/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrderedMap: () => (/* binding */ OrderedMap)\n/* harmony export */ });\nvar extendStatics = function(e, r) {\n    extendStatics = Object.setPrototypeOf || {\n        __proto__: []\n    } instanceof Array && function(e, r) {\n        e.__proto__ = r;\n    } || function(e, r) {\n        for (var t in r) if (Object.prototype.hasOwnProperty.call(r, t)) e[t] = r[t];\n    };\n    return extendStatics(e, r);\n};\n\nfunction __extends(e, r) {\n    if (typeof r !== \"function\" && r !== null) throw new TypeError(\"Class extends value \" + String(r) + \" is not a constructor or null\");\n    extendStatics(e, r);\n    function __() {\n        this.constructor = e;\n    }\n    e.prototype = r === null ? Object.create(r) : (__.prototype = r.prototype, new __);\n}\n\nfunction __generator(e, r) {\n    var t = {\n        label: 0,\n        sent: function() {\n            if (s[0] & 1) throw s[1];\n            return s[1];\n        },\n        trys: [],\n        ops: []\n    }, i, n, s, h;\n    return h = {\n        next: verb(0),\n        throw: verb(1),\n        return: verb(2)\n    }, typeof Symbol === \"function\" && (h[Symbol.iterator] = function() {\n        return this;\n    }), h;\n    function verb(e) {\n        return function(r) {\n            return step([ e, r ]);\n        };\n    }\n    function step(a) {\n        if (i) throw new TypeError(\"Generator is already executing.\");\n        while (h && (h = 0, a[0] && (t = 0)), t) try {\n            if (i = 1, n && (s = a[0] & 2 ? n[\"return\"] : a[0] ? n[\"throw\"] || ((s = n[\"return\"]) && s.call(n), \n            0) : n.next) && !(s = s.call(n, a[1])).done) return s;\n            if (n = 0, s) a = [ a[0] & 2, s.value ];\n            switch (a[0]) {\n              case 0:\n              case 1:\n                s = a;\n                break;\n\n              case 4:\n                t.label++;\n                return {\n                    value: a[1],\n                    done: false\n                };\n\n              case 5:\n                t.label++;\n                n = a[1];\n                a = [ 0 ];\n                continue;\n\n              case 7:\n                a = t.ops.pop();\n                t.trys.pop();\n                continue;\n\n              default:\n                if (!(s = t.trys, s = s.length > 0 && s[s.length - 1]) && (a[0] === 6 || a[0] === 2)) {\n                    t = 0;\n                    continue;\n                }\n                if (a[0] === 3 && (!s || a[1] > s[0] && a[1] < s[3])) {\n                    t.label = a[1];\n                    break;\n                }\n                if (a[0] === 6 && t.label < s[1]) {\n                    t.label = s[1];\n                    s = a;\n                    break;\n                }\n                if (s && t.label < s[2]) {\n                    t.label = s[2];\n                    t.ops.push(a);\n                    break;\n                }\n                if (s[2]) t.ops.pop();\n                t.trys.pop();\n                continue;\n            }\n            a = r.call(e, t);\n        } catch (e) {\n            a = [ 6, e ];\n            n = 0;\n        } finally {\n            i = s = 0;\n        }\n        if (a[0] & 5) throw a[1];\n        return {\n            value: a[0] ? a[1] : void 0,\n            done: true\n        };\n    }\n}\n\ntypeof SuppressedError === \"function\" ? SuppressedError : function(e, r, t) {\n    var i = new Error(t);\n    return i.name = \"SuppressedError\", i.error = e, i.suppressed = r, i;\n};\n\nvar TreeNode = function() {\n    function TreeNode(e, r, t) {\n        if (t === void 0) {\n            t = 1;\n        }\n        this.t = undefined;\n        this.i = undefined;\n        this.h = undefined;\n        this.u = e;\n        this.o = r;\n        this.l = t;\n    }\n    TreeNode.prototype.v = function() {\n        var e = this;\n        var r = e.h.h === e;\n        if (r && e.l === 1) {\n            e = e.i;\n        } else if (e.t) {\n            e = e.t;\n            while (e.i) {\n                e = e.i;\n            }\n        } else {\n            if (r) {\n                return e.h;\n            }\n            var t = e.h;\n            while (t.t === e) {\n                e = t;\n                t = e.h;\n            }\n            e = t;\n        }\n        return e;\n    };\n    TreeNode.prototype.p = function() {\n        var e = this;\n        if (e.i) {\n            e = e.i;\n            while (e.t) {\n                e = e.t;\n            }\n            return e;\n        } else {\n            var r = e.h;\n            while (r.i === e) {\n                e = r;\n                r = e.h;\n            }\n            if (e.i !== r) {\n                return r;\n            } else return e;\n        }\n    };\n    TreeNode.prototype.T = function() {\n        var e = this.h;\n        var r = this.i;\n        var t = r.t;\n        if (e.h === this) e.h = r; else if (e.t === this) e.t = r; else e.i = r;\n        r.h = e;\n        r.t = this;\n        this.h = r;\n        this.i = t;\n        if (t) t.h = this;\n        return r;\n    };\n    TreeNode.prototype.I = function() {\n        var e = this.h;\n        var r = this.t;\n        var t = r.i;\n        if (e.h === this) e.h = r; else if (e.t === this) e.t = r; else e.i = r;\n        r.h = e;\n        r.i = this;\n        this.h = r;\n        this.t = t;\n        if (t) t.h = this;\n        return r;\n    };\n    return TreeNode;\n}();\n\nvar TreeNodeEnableIndex = function(e) {\n    __extends(TreeNodeEnableIndex, e);\n    function TreeNodeEnableIndex() {\n        var r = e !== null && e.apply(this, arguments) || this;\n        r.O = 1;\n        return r;\n    }\n    TreeNodeEnableIndex.prototype.T = function() {\n        var r = e.prototype.T.call(this);\n        this.M();\n        r.M();\n        return r;\n    };\n    TreeNodeEnableIndex.prototype.I = function() {\n        var r = e.prototype.I.call(this);\n        this.M();\n        r.M();\n        return r;\n    };\n    TreeNodeEnableIndex.prototype.M = function() {\n        this.O = 1;\n        if (this.t) {\n            this.O += this.t.O;\n        }\n        if (this.i) {\n            this.O += this.i.O;\n        }\n    };\n    return TreeNodeEnableIndex;\n}(TreeNode);\n\nvar ContainerIterator = function() {\n    function ContainerIterator(e) {\n        if (e === void 0) {\n            e = 0;\n        }\n        this.iteratorType = e;\n    }\n    ContainerIterator.prototype.equals = function(e) {\n        return this.C === e.C;\n    };\n    return ContainerIterator;\n}();\n\nvar Base = function() {\n    function Base() {\n        this._ = 0;\n    }\n    Object.defineProperty(Base.prototype, \"length\", {\n        get: function() {\n            return this._;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Base.prototype.size = function() {\n        return this._;\n    };\n    Base.prototype.empty = function() {\n        return this._ === 0;\n    };\n    return Base;\n}();\n\nvar Container = function(e) {\n    __extends(Container, e);\n    function Container() {\n        return e !== null && e.apply(this, arguments) || this;\n    }\n    return Container;\n}(Base);\n\nfunction throwIteratorAccessError() {\n    throw new RangeError(\"Iterator access denied!\");\n}\n\nvar TreeContainer = function(e) {\n    __extends(TreeContainer, e);\n    function TreeContainer(r, t) {\n        if (r === void 0) {\n            r = function(e, r) {\n                if (e < r) return -1;\n                if (e > r) return 1;\n                return 0;\n            };\n        }\n        if (t === void 0) {\n            t = false;\n        }\n        var i = e.call(this) || this;\n        i.N = undefined;\n        i.g = r;\n        i.enableIndex = t;\n        i.S = t ? TreeNodeEnableIndex : TreeNode;\n        i.A = new i.S;\n        return i;\n    }\n    TreeContainer.prototype.m = function(e, r) {\n        var t = this.A;\n        while (e) {\n            var i = this.g(e.u, r);\n            if (i < 0) {\n                e = e.i;\n            } else if (i > 0) {\n                t = e;\n                e = e.t;\n            } else return e;\n        }\n        return t;\n    };\n    TreeContainer.prototype.B = function(e, r) {\n        var t = this.A;\n        while (e) {\n            var i = this.g(e.u, r);\n            if (i <= 0) {\n                e = e.i;\n            } else {\n                t = e;\n                e = e.t;\n            }\n        }\n        return t;\n    };\n    TreeContainer.prototype.j = function(e, r) {\n        var t = this.A;\n        while (e) {\n            var i = this.g(e.u, r);\n            if (i < 0) {\n                t = e;\n                e = e.i;\n            } else if (i > 0) {\n                e = e.t;\n            } else return e;\n        }\n        return t;\n    };\n    TreeContainer.prototype.k = function(e, r) {\n        var t = this.A;\n        while (e) {\n            var i = this.g(e.u, r);\n            if (i < 0) {\n                t = e;\n                e = e.i;\n            } else {\n                e = e.t;\n            }\n        }\n        return t;\n    };\n    TreeContainer.prototype.R = function(e) {\n        while (true) {\n            var r = e.h;\n            if (r === this.A) return;\n            if (e.l === 1) {\n                e.l = 0;\n                return;\n            }\n            if (e === r.t) {\n                var t = r.i;\n                if (t.l === 1) {\n                    t.l = 0;\n                    r.l = 1;\n                    if (r === this.N) {\n                        this.N = r.T();\n                    } else r.T();\n                } else {\n                    if (t.i && t.i.l === 1) {\n                        t.l = r.l;\n                        r.l = 0;\n                        t.i.l = 0;\n                        if (r === this.N) {\n                            this.N = r.T();\n                        } else r.T();\n                        return;\n                    } else if (t.t && t.t.l === 1) {\n                        t.l = 1;\n                        t.t.l = 0;\n                        t.I();\n                    } else {\n                        t.l = 1;\n                        e = r;\n                    }\n                }\n            } else {\n                var t = r.t;\n                if (t.l === 1) {\n                    t.l = 0;\n                    r.l = 1;\n                    if (r === this.N) {\n                        this.N = r.I();\n                    } else r.I();\n                } else {\n                    if (t.t && t.t.l === 1) {\n                        t.l = r.l;\n                        r.l = 0;\n                        t.t.l = 0;\n                        if (r === this.N) {\n                            this.N = r.I();\n                        } else r.I();\n                        return;\n                    } else if (t.i && t.i.l === 1) {\n                        t.l = 1;\n                        t.i.l = 0;\n                        t.T();\n                    } else {\n                        t.l = 1;\n                        e = r;\n                    }\n                }\n            }\n        }\n    };\n    TreeContainer.prototype.G = function(e) {\n        if (this._ === 1) {\n            this.clear();\n            return;\n        }\n        var r = e;\n        while (r.t || r.i) {\n            if (r.i) {\n                r = r.i;\n                while (r.t) r = r.t;\n            } else {\n                r = r.t;\n            }\n            var t = e.u;\n            e.u = r.u;\n            r.u = t;\n            var i = e.o;\n            e.o = r.o;\n            r.o = i;\n            e = r;\n        }\n        if (this.A.t === r) {\n            this.A.t = r.h;\n        } else if (this.A.i === r) {\n            this.A.i = r.h;\n        }\n        this.R(r);\n        var n = r.h;\n        if (r === n.t) {\n            n.t = undefined;\n        } else n.i = undefined;\n        this._ -= 1;\n        this.N.l = 0;\n        if (this.enableIndex) {\n            while (n !== this.A) {\n                n.O -= 1;\n                n = n.h;\n            }\n        }\n    };\n    TreeContainer.prototype.P = function(e) {\n        var r = typeof e === \"number\" ? e : undefined;\n        var t = typeof e === \"function\" ? e : undefined;\n        var i = typeof e === \"undefined\" ? [] : undefined;\n        var n = 0;\n        var s = this.N;\n        var h = [];\n        while (h.length || s) {\n            if (s) {\n                h.push(s);\n                s = s.t;\n            } else {\n                s = h.pop();\n                if (n === r) return s;\n                i && i.push(s);\n                t && t(s, n, this);\n                n += 1;\n                s = s.i;\n            }\n        }\n        return i;\n    };\n    TreeContainer.prototype.q = function(e) {\n        while (true) {\n            var r = e.h;\n            if (r.l === 0) return;\n            var t = r.h;\n            if (r === t.t) {\n                var i = t.i;\n                if (i && i.l === 1) {\n                    i.l = r.l = 0;\n                    if (t === this.N) return;\n                    t.l = 1;\n                    e = t;\n                    continue;\n                } else if (e === r.i) {\n                    e.l = 0;\n                    if (e.t) {\n                        e.t.h = r;\n                    }\n                    if (e.i) {\n                        e.i.h = t;\n                    }\n                    r.i = e.t;\n                    t.t = e.i;\n                    e.t = r;\n                    e.i = t;\n                    if (t === this.N) {\n                        this.N = e;\n                        this.A.h = e;\n                    } else {\n                        var n = t.h;\n                        if (n.t === t) {\n                            n.t = e;\n                        } else n.i = e;\n                    }\n                    e.h = t.h;\n                    r.h = e;\n                    t.h = e;\n                    t.l = 1;\n                } else {\n                    r.l = 0;\n                    if (t === this.N) {\n                        this.N = t.I();\n                    } else t.I();\n                    t.l = 1;\n                    return;\n                }\n            } else {\n                var i = t.t;\n                if (i && i.l === 1) {\n                    i.l = r.l = 0;\n                    if (t === this.N) return;\n                    t.l = 1;\n                    e = t;\n                    continue;\n                } else if (e === r.t) {\n                    e.l = 0;\n                    if (e.t) {\n                        e.t.h = t;\n                    }\n                    if (e.i) {\n                        e.i.h = r;\n                    }\n                    t.i = e.t;\n                    r.t = e.i;\n                    e.t = t;\n                    e.i = r;\n                    if (t === this.N) {\n                        this.N = e;\n                        this.A.h = e;\n                    } else {\n                        var n = t.h;\n                        if (n.t === t) {\n                            n.t = e;\n                        } else n.i = e;\n                    }\n                    e.h = t.h;\n                    r.h = e;\n                    t.h = e;\n                    t.l = 1;\n                } else {\n                    r.l = 0;\n                    if (t === this.N) {\n                        this.N = t.T();\n                    } else t.T();\n                    t.l = 1;\n                    return;\n                }\n            }\n            if (this.enableIndex) {\n                r.M();\n                t.M();\n                e.M();\n            }\n            return;\n        }\n    };\n    TreeContainer.prototype.D = function(e, r, t) {\n        if (this.N === undefined) {\n            this._ += 1;\n            this.N = new this.S(e, r, 0);\n            this.N.h = this.A;\n            this.A.h = this.A.t = this.A.i = this.N;\n            return this._;\n        }\n        var i;\n        var n = this.A.t;\n        var s = this.g(n.u, e);\n        if (s === 0) {\n            n.o = r;\n            return this._;\n        } else if (s > 0) {\n            n.t = new this.S(e, r);\n            n.t.h = n;\n            i = n.t;\n            this.A.t = i;\n        } else {\n            var h = this.A.i;\n            var a = this.g(h.u, e);\n            if (a === 0) {\n                h.o = r;\n                return this._;\n            } else if (a < 0) {\n                h.i = new this.S(e, r);\n                h.i.h = h;\n                i = h.i;\n                this.A.i = i;\n            } else {\n                if (t !== undefined) {\n                    var u = t.C;\n                    if (u !== this.A) {\n                        var f = this.g(u.u, e);\n                        if (f === 0) {\n                            u.o = r;\n                            return this._;\n                        } else if (f > 0) {\n                            var o = u.v();\n                            var d = this.g(o.u, e);\n                            if (d === 0) {\n                                o.o = r;\n                                return this._;\n                            } else if (d < 0) {\n                                i = new this.S(e, r);\n                                if (o.i === undefined) {\n                                    o.i = i;\n                                    i.h = o;\n                                } else {\n                                    u.t = i;\n                                    i.h = u;\n                                }\n                            }\n                        }\n                    }\n                }\n                if (i === undefined) {\n                    i = this.N;\n                    while (true) {\n                        var c = this.g(i.u, e);\n                        if (c > 0) {\n                            if (i.t === undefined) {\n                                i.t = new this.S(e, r);\n                                i.t.h = i;\n                                i = i.t;\n                                break;\n                            }\n                            i = i.t;\n                        } else if (c < 0) {\n                            if (i.i === undefined) {\n                                i.i = new this.S(e, r);\n                                i.i.h = i;\n                                i = i.i;\n                                break;\n                            }\n                            i = i.i;\n                        } else {\n                            i.o = r;\n                            return this._;\n                        }\n                    }\n                }\n            }\n        }\n        if (this.enableIndex) {\n            var l = i.h;\n            while (l !== this.A) {\n                l.O += 1;\n                l = l.h;\n            }\n        }\n        this.q(i);\n        this._ += 1;\n        return this._;\n    };\n    TreeContainer.prototype.F = function(e, r) {\n        while (e) {\n            var t = this.g(e.u, r);\n            if (t < 0) {\n                e = e.i;\n            } else if (t > 0) {\n                e = e.t;\n            } else return e;\n        }\n        return e || this.A;\n    };\n    TreeContainer.prototype.clear = function() {\n        this._ = 0;\n        this.N = undefined;\n        this.A.h = undefined;\n        this.A.t = this.A.i = undefined;\n    };\n    TreeContainer.prototype.updateKeyByIterator = function(e, r) {\n        var t = e.C;\n        if (t === this.A) {\n            throwIteratorAccessError();\n        }\n        if (this._ === 1) {\n            t.u = r;\n            return true;\n        }\n        var i = t.p().u;\n        if (t === this.A.t) {\n            if (this.g(i, r) > 0) {\n                t.u = r;\n                return true;\n            }\n            return false;\n        }\n        var n = t.v().u;\n        if (t === this.A.i) {\n            if (this.g(n, r) < 0) {\n                t.u = r;\n                return true;\n            }\n            return false;\n        }\n        if (this.g(n, r) >= 0 || this.g(i, r) <= 0) return false;\n        t.u = r;\n        return true;\n    };\n    TreeContainer.prototype.eraseElementByPos = function(e) {\n        if (e < 0 || e > this._ - 1) {\n            throw new RangeError;\n        }\n        var r = this.P(e);\n        this.G(r);\n        return this._;\n    };\n    TreeContainer.prototype.eraseElementByKey = function(e) {\n        if (this._ === 0) return false;\n        var r = this.F(this.N, e);\n        if (r === this.A) return false;\n        this.G(r);\n        return true;\n    };\n    TreeContainer.prototype.eraseElementByIterator = function(e) {\n        var r = e.C;\n        if (r === this.A) {\n            throwIteratorAccessError();\n        }\n        var t = r.i === undefined;\n        var i = e.iteratorType === 0;\n        if (i) {\n            if (t) e.next();\n        } else {\n            if (!t || r.t === undefined) e.next();\n        }\n        this.G(r);\n        return e;\n    };\n    TreeContainer.prototype.getHeight = function() {\n        if (this._ === 0) return 0;\n        function traversal(e) {\n            if (!e) return 0;\n            return Math.max(traversal(e.t), traversal(e.i)) + 1;\n        }\n        return traversal(this.N);\n    };\n    return TreeContainer;\n}(Container);\n\nvar TreeIterator = function(e) {\n    __extends(TreeIterator, e);\n    function TreeIterator(r, t, i) {\n        var n = e.call(this, i) || this;\n        n.C = r;\n        n.A = t;\n        if (n.iteratorType === 0) {\n            n.pre = function() {\n                if (this.C === this.A.t) {\n                    throwIteratorAccessError();\n                }\n                this.C = this.C.v();\n                return this;\n            };\n            n.next = function() {\n                if (this.C === this.A) {\n                    throwIteratorAccessError();\n                }\n                this.C = this.C.p();\n                return this;\n            };\n        } else {\n            n.pre = function() {\n                if (this.C === this.A.i) {\n                    throwIteratorAccessError();\n                }\n                this.C = this.C.p();\n                return this;\n            };\n            n.next = function() {\n                if (this.C === this.A) {\n                    throwIteratorAccessError();\n                }\n                this.C = this.C.v();\n                return this;\n            };\n        }\n        return n;\n    }\n    Object.defineProperty(TreeIterator.prototype, \"index\", {\n        get: function() {\n            var e = this.C;\n            var r = this.A.h;\n            if (e === this.A) {\n                if (r) {\n                    return r.O - 1;\n                }\n                return 0;\n            }\n            var t = 0;\n            if (e.t) {\n                t += e.t.O;\n            }\n            while (e !== r) {\n                var i = e.h;\n                if (e === i.i) {\n                    t += 1;\n                    if (i.t) {\n                        t += i.t.O;\n                    }\n                }\n                e = i;\n            }\n            return t;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    TreeIterator.prototype.isAccessible = function() {\n        return this.C !== this.A;\n    };\n    return TreeIterator;\n}(ContainerIterator);\n\nvar OrderedMapIterator = function(e) {\n    __extends(OrderedMapIterator, e);\n    function OrderedMapIterator(r, t, i, n) {\n        var s = e.call(this, r, t, n) || this;\n        s.container = i;\n        return s;\n    }\n    Object.defineProperty(OrderedMapIterator.prototype, \"pointer\", {\n        get: function() {\n            if (this.C === this.A) {\n                throwIteratorAccessError();\n            }\n            var e = this;\n            return new Proxy([], {\n                get: function(r, t) {\n                    if (t === \"0\") return e.C.u; else if (t === \"1\") return e.C.o;\n                    r[0] = e.C.u;\n                    r[1] = e.C.o;\n                    return r[t];\n                },\n                set: function(r, t, i) {\n                    if (t !== \"1\") {\n                        throw new TypeError(\"prop must be 1\");\n                    }\n                    e.C.o = i;\n                    return true;\n                }\n            });\n        },\n        enumerable: false,\n        configurable: true\n    });\n    OrderedMapIterator.prototype.copy = function() {\n        return new OrderedMapIterator(this.C, this.A, this.container, this.iteratorType);\n    };\n    return OrderedMapIterator;\n}(TreeIterator);\n\nvar OrderedMap = function(e) {\n    __extends(OrderedMap, e);\n    function OrderedMap(r, t, i) {\n        if (r === void 0) {\n            r = [];\n        }\n        var n = e.call(this, t, i) || this;\n        var s = n;\n        r.forEach((function(e) {\n            s.setElement(e[0], e[1]);\n        }));\n        return n;\n    }\n    OrderedMap.prototype.begin = function() {\n        return new OrderedMapIterator(this.A.t || this.A, this.A, this);\n    };\n    OrderedMap.prototype.end = function() {\n        return new OrderedMapIterator(this.A, this.A, this);\n    };\n    OrderedMap.prototype.rBegin = function() {\n        return new OrderedMapIterator(this.A.i || this.A, this.A, this, 1);\n    };\n    OrderedMap.prototype.rEnd = function() {\n        return new OrderedMapIterator(this.A, this.A, this, 1);\n    };\n    OrderedMap.prototype.front = function() {\n        if (this._ === 0) return;\n        var e = this.A.t;\n        return [ e.u, e.o ];\n    };\n    OrderedMap.prototype.back = function() {\n        if (this._ === 0) return;\n        var e = this.A.i;\n        return [ e.u, e.o ];\n    };\n    OrderedMap.prototype.lowerBound = function(e) {\n        var r = this.m(this.N, e);\n        return new OrderedMapIterator(r, this.A, this);\n    };\n    OrderedMap.prototype.upperBound = function(e) {\n        var r = this.B(this.N, e);\n        return new OrderedMapIterator(r, this.A, this);\n    };\n    OrderedMap.prototype.reverseLowerBound = function(e) {\n        var r = this.j(this.N, e);\n        return new OrderedMapIterator(r, this.A, this);\n    };\n    OrderedMap.prototype.reverseUpperBound = function(e) {\n        var r = this.k(this.N, e);\n        return new OrderedMapIterator(r, this.A, this);\n    };\n    OrderedMap.prototype.forEach = function(e) {\n        this.P((function(r, t, i) {\n            e([ r.u, r.o ], t, i);\n        }));\n    };\n    OrderedMap.prototype.setElement = function(e, r, t) {\n        return this.D(e, r, t);\n    };\n    OrderedMap.prototype.getElementByPos = function(e) {\n        if (e < 0 || e > this._ - 1) {\n            throw new RangeError;\n        }\n        var r = this.P(e);\n        return [ r.u, r.o ];\n    };\n    OrderedMap.prototype.find = function(e) {\n        var r = this.F(this.N, e);\n        return new OrderedMapIterator(r, this.A, this);\n    };\n    OrderedMap.prototype.getElementByKey = function(e) {\n        var r = this.F(this.N, e);\n        return r.o;\n    };\n    OrderedMap.prototype.union = function(e) {\n        var r = this;\n        e.forEach((function(e) {\n            r.setElement(e[0], e[1]);\n        }));\n        return this._;\n    };\n    OrderedMap.prototype[Symbol.iterator] = function() {\n        var e, r, t, i;\n        return __generator(this, (function(n) {\n            switch (n.label) {\n              case 0:\n                e = this._;\n                r = this.P();\n                t = 0;\n                n.label = 1;\n\n              case 1:\n                if (!(t < e)) return [ 3, 4 ];\n                i = r[t];\n                return [ 4, [ i.u, i.o ] ];\n\n              case 2:\n                n.sent();\n                n.label = 3;\n\n              case 3:\n                ++t;\n                return [ 3, 1 ];\n\n              case 4:\n                return [ 2 ];\n            }\n        }));\n    };\n    return OrderedMap;\n}(TreeContainer);\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@js-sdsl/ordered-map/dist/esm/index.js\n");

/***/ })

};
;