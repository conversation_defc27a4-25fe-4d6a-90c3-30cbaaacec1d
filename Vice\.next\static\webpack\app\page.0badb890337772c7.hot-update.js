"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/project/create-project-modal.tsx":
/*!*********************************************************!*\
  !*** ./src/components/project/create-project-modal.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateProjectModal: () => (/* binding */ CreateProjectModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* __next_internal_client_entry_do_not_use__ CreateProjectModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CreateProjectModal(param) {\n    let { open, onOpenChange, stackName, projectTitle = '' } = param;\n    var _startingCommands_stackName;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [projectName, setProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(projectTitle);\n    const [projectPath, setProjectPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\ViceProjects\\\\\".concat(stackName.toLowerCase()));\n    const [projectDescription, setProjectDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCommands, setShowCommands] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLoading, setShowLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentCommand, setCurrentCommand] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [commandProgress, setCommandProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCommandRunning, setIsCommandRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Update path when stackName changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateProjectModal.useEffect\": ()=>{\n            setProjectPath(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\ViceProjects\\\\\".concat(stackName.toLowerCase()));\n        }\n    }[\"CreateProjectModal.useEffect\"], [\n        stackName\n    ]);\n    // Validate and format project name (no spaces, no caps, use hyphens)\n    const validateProjectName = (name)=>{\n        return name.trim().toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n    };\n    // Handle project name input with real-time validation\n    const handleProjectNameChange = (e)=>{\n        const value = e.target.value;\n        setProjectName(value);\n    };\n    // Listen for command progress events from Electron\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateProjectModal.useEffect\": ()=>{\n            if ( true && window.electronAPI) {\n                const handleCommandProgress = {\n                    \"CreateProjectModal.useEffect.handleCommandProgress\": (data)=>{\n                        console.log('[COMMAND PROGRESS] 📨 Received:', data);\n                        if (data.status === 'running') {\n                            setCurrentCommand(data.command);\n                            setIsCommandRunning(true);\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"▶️ Running: \".concat(data.command)\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                        } else if (data.status === 'completed') {\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"✅ Completed: \".concat(data.command)\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            setIsCommandRunning(false);\n                        } else if (data.status === 'error') {\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"❌ Error: \".concat(data.command, \" - \").concat(data.error || 'Unknown error')\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            setIsCommandRunning(false);\n                        } else if (data.status === 'starting-ide') {\n                            setCurrentCommand(\"Starting IDE for \".concat(data.projectName || 'project', \"...\"));\n                            setIsCommandRunning(true);\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"\\uD83D\\uDE80 Starting IDE and returning to dashboard...\"\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            // Close loading screen and return to dashboard\n                            setTimeout({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": ()=>{\n                                    handleLoadingComplete();\n                                }\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"], 3000);\n                        }\n                    }\n                }[\"CreateProjectModal.useEffect.handleCommandProgress\"];\n                // Listen for the IPC event\n                if (window.electronAPI.onCommandProgress) {\n                    window.electronAPI.onCommandProgress(handleCommandProgress);\n                }\n                return ({\n                    \"CreateProjectModal.useEffect\": ()=>{\n                        console.log('[PROJECT] Cleaning up command progress listener');\n                    }\n                })[\"CreateProjectModal.useEffect\"];\n            }\n        }\n    }[\"CreateProjectModal.useEffect\"], []);\n    const handleLoadingComplete = ()=>{\n        setShowLoading(false);\n        setIsCreating(false);\n        setCurrentCommand('');\n        setCommandProgress([]);\n        setIsCommandRunning(false);\n        toast({\n            title: 'Project Ready',\n            description: \"\".concat(projectName, \" has been created and opened in the IDE.\")\n        });\n    };\n    // Starting commands for different stacks\n    const startingCommands = {\n        'Mern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express mongoose cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/models/User.js server/routes/api.js'\n        ],\n        'Pern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express pg cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/db.js server/routes/api.js'\n        ],\n        'NextJS': [\n            'npx create-next-app@latest my-app',\n            'cd my-app',\n            'npm install'\n        ],\n        'Django': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install django djangorestframework',\n            'django-admin startproject myproject .',\n            'python manage.py startapp myapp'\n        ],\n        'Flask': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install flask flask-sqlalchemy flask-cors',\n            'mkdir app',\n            'touch app/__init__.py app/routes.py app/models.py'\n        ],\n        'Bash': [\n            'mkdir my-script',\n            'cd my-script',\n            'touch script.sh',\n            'chmod +x script.sh',\n            'echo \"#!/bin/bash\" > script.sh'\n        ]\n    };\n    const handleCreate = async ()=>{\n        if (!projectName.trim()) {\n            toast({\n                title: 'Project name required',\n                description: 'Please enter a name for your project.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        // Validate and format project name\n        const formattedProjectName = validateProjectName(projectName);\n        if (!formattedProjectName) {\n            toast({\n                title: 'Invalid project name',\n                description: 'Project name must contain at least one letter or number.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        if (formattedProjectName !== projectName.trim().toLowerCase().replace(/\\s+/g, '-')) {\n            toast({\n                title: 'Project name formatted',\n                description: \"Project name changed to: \".concat(formattedProjectName)\n            });\n            setProjectName(formattedProjectName);\n        }\n        setIsCreating(true);\n        try {\n            // Create project using Electron API - NO FILES PARAMETER\n            if (window.electronAPI && window.electronAPI.createProjectFiles) {\n                const result = await window.electronAPI.createProjectFiles({\n                    projectName: formattedProjectName,\n                    projectPath,\n                    stackName,\n                    description: projectDescription\n                });\n                if (result && result.success) {\n                    const finalProjectPath = result.actualPath || projectPath;\n                    // Open IDE with project - NO FILES PARAMETER\n                    const ideUrl = \"http://localhost:9003?project=\".concat(encodeURIComponent(formattedProjectName), \"&path=\").concat(encodeURIComponent(finalProjectPath));\n                    window.open(ideUrl, '_blank');\n                    toast({\n                        title: 'Project Created Successfully',\n                        description: \"\".concat(formattedProjectName, \" has been created and is opening in the IDE.\")\n                    });\n                    // Reset form and close modal\n                    setProjectName('');\n                    setProjectDescription('');\n                    onOpenChange(false);\n                } else {\n                    throw new Error((result === null || result === void 0 ? void 0 : result.message) || 'Failed to create project');\n                }\n            } else {\n                throw new Error('Electron API not available');\n            }\n        } catch (error) {\n            console.error('[PROJECT] Error creating project:', error);\n            toast({\n                title: 'Failed to create project',\n                description: error instanceof Error ? error.message : 'An error occurred while creating the project.',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"sm:max-w-[550px] md:max-w-[600px] rounded-xl overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-20 w-40\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/logo.png\",\n                            alt: \"VICE Logo\",\n                            fill: true,\n                            style: {\n                                objectFit: 'contain'\n                            },\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-[-100%]' : 'translate-x-0'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                    className: \"mb-[5] pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                            className: \"text-center\",\n                                            children: \"Create New Project\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                            className: \"text-center text-sm text-muted-foreground\",\n                                            children: [\n                                                \"Create a new \",\n                                                stackName,\n                                                \" project. Fill in the details below to get started.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4 py-4 px-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"name\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"name\",\n                                                    value: projectName,\n                                                    onChange: handleProjectNameChange,\n                                                    className: \"w-full\",\n                                                    placeholder: \"my-awesome-project\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: [\n                                                        \"No spaces or caps allowed. Use hyphens for multi-word projects.\",\n                                                        projectName && projectName !== validateProjectName(projectName) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-orange-600 ml-1\",\n                                                            children: [\n                                                                \"→ Will be: \",\n                                                                validateProjectName(projectName)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"path\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Path\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"path\",\n                                                    value: projectPath,\n                                                    onChange: (e)=>setProjectPath(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"Documents/ViceProjects/\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"description\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                    id: \"description\",\n                                                    value: projectDescription,\n                                                    onChange: (e)=>setProjectDescription(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"A brief description of your project\",\n                                                    rows: 3\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-2 mb-4 ml-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-xs text-muted-foreground flex items-center gap-1 p-0 h-auto\",\n                                                onClick: ()=>setShowCommands(true),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3.5 w-3.5 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"See Starting Commands\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-3.5 w-3.5 ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                            className: \"flex justify-end items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>onOpenChange(false),\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        onClick: handleCreate,\n                                                        disabled: isCreating,\n                                                        children: isCreating ? 'Creating...' : 'Create Project'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-0' : 'translate-x-[100%]'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 ml-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"pl-0 flex items-center gap-1\",\n                                        onClick: ()=>setShowCommands(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Project Form\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                            children: [\n                                                \"Starting Commands for \",\n                                                stackName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                            children: [\n                                                \"Use these commands to set up your \",\n                                                stackName,\n                                                \" project manually.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-4 px-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-muted p-4 rounded-md font-mono text-xs sm:text-sm overflow-auto max-h-[250px]\",\n                                        children: ((_startingCommands_stackName = startingCommands[stackName]) === null || _startingCommands_stackName === void 0 ? void 0 : _startingCommands_stackName.map((command, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground mr-2 flex-shrink-0\",\n                                                        children: \"$\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"break-all\",\n                                                        children: command\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 19\n                                            }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                \"No commands available for \",\n                                                stackName,\n                                                \".\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>onOpenChange(false),\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateProjectModal, \"+Wy90OJPGbFq7Wx8RMMP1TbpegU=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = CreateProjectModal;\nvar _c;\n$RefreshReg$(_c, \"CreateProjectModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/create-project-modal.tsx\n"));

/***/ })

});