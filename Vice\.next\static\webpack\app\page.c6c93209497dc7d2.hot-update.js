"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/project/create-project-modal.tsx":
/*!*********************************************************!*\
  !*** ./src/components/project/create-project-modal.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateProjectModal: () => (/* binding */ CreateProjectModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* __next_internal_client_entry_do_not_use__ CreateProjectModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CreateProjectModal(param) {\n    let { open, onOpenChange, stackName, projectTitle = '' } = param;\n    var _startingCommands_stackName;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [projectName, setProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(projectTitle);\n    const [projectPath, setProjectPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\ViceProjects\\\\\".concat(stackName.toLowerCase()));\n    const [projectDescription, setProjectDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCommands, setShowCommands] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Update path when stackName changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateProjectModal.useEffect\": ()=>{\n            setProjectPath(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\ViceProjects\\\\\".concat(stackName.toLowerCase()));\n        }\n    }[\"CreateProjectModal.useEffect\"], [\n        stackName\n    ]);\n    // Validate and format project name (no spaces, no caps, use hyphens)\n    const validateProjectName = (name)=>{\n        return name.trim().toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n    };\n    // Handle project name input with real-time validation\n    const handleProjectNameChange = (e)=>{\n        const value = e.target.value;\n        setProjectName(value);\n    };\n    // Starting commands for different stacks\n    const startingCommands = {\n        'Mern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express mongoose cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/models/User.js server/routes/api.js'\n        ],\n        'Pern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express pg cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/db.js server/routes/api.js'\n        ],\n        'NextJS': [\n            'npx create-next-app@latest my-app',\n            'cd my-app',\n            'npm install'\n        ],\n        'Django': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install django djangorestframework',\n            'django-admin startproject myproject .',\n            'python manage.py startapp myapp'\n        ],\n        'Flask': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install flask flask-sqlalchemy flask-cors',\n            'mkdir app',\n            'touch app/__init__.py app/routes.py app/models.py'\n        ],\n        'Bash': [\n            'mkdir my-script',\n            'cd my-script',\n            'touch script.sh',\n            'chmod +x script.sh',\n            'echo \"#!/bin/bash\" > script.sh'\n        ]\n    };\n    const handleCreate = async ()=>{\n        if (!projectName.trim()) {\n            toast({\n                title: 'Project name required',\n                description: 'Please enter a name for your project.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        // Validate and format project name\n        const formattedProjectName = validateProjectName(projectName);\n        if (!formattedProjectName) {\n            toast({\n                title: 'Invalid project name',\n                description: 'Project name must contain at least one letter or number.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        if (formattedProjectName !== projectName.trim().toLowerCase().replace(/\\s+/g, '-')) {\n            toast({\n                title: 'Project name formatted',\n                description: \"Project name changed to: \".concat(formattedProjectName)\n            });\n            setProjectName(formattedProjectName);\n        }\n        setIsCreating(true);\n        try {\n            // Create project using Electron API - NO FILES PARAMETER\n            if (window.electronAPI && window.electronAPI.createProjectFiles) {\n                const result = await window.electronAPI.createProjectFiles({\n                    projectName: formattedProjectName,\n                    projectPath,\n                    stackName,\n                    description: projectDescription\n                });\n                if (result && result.success) {\n                    const finalProjectPath = result.actualPath || projectPath;\n                    // Open IDE with project - NO FILES PARAMETER\n                    const ideUrl = \"http://localhost:9003?project=\".concat(encodeURIComponent(formattedProjectName), \"&path=\").concat(encodeURIComponent(finalProjectPath));\n                    window.open(ideUrl, '_blank');\n                    toast({\n                        title: 'Project Created Successfully',\n                        description: \"\".concat(formattedProjectName, \" has been created and is opening in the IDE.\")\n                    });\n                    // Reset form and close modal\n                    setProjectName('');\n                    setProjectDescription('');\n                    onOpenChange(false);\n                } else {\n                    throw new Error((result === null || result === void 0 ? void 0 : result.message) || 'Failed to create project');\n                }\n            } else {\n                throw new Error('Electron API not available');\n            }\n        } catch (error) {\n            console.error('[PROJECT] Error creating project:', error);\n            toast({\n                title: 'Failed to create project',\n                description: error instanceof Error ? error.message : 'An error occurred while creating the project.',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"sm:max-w-[550px] md:max-w-[600px] rounded-xl overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-20 w-40\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/logo.png\",\n                            alt: \"VICE Logo\",\n                            fill: true,\n                            style: {\n                                objectFit: 'contain'\n                            },\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-[-100%]' : 'translate-x-0'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                    className: \"mb-[5] pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                            className: \"text-center\",\n                                            children: \"Create New Project\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                            className: \"text-center text-sm text-muted-foreground\",\n                                            children: [\n                                                \"Create a new \",\n                                                stackName,\n                                                \" project. Fill in the details below to get started.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4 py-4 px-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"name\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"name\",\n                                                    value: projectName,\n                                                    onChange: handleProjectNameChange,\n                                                    className: \"w-full\",\n                                                    placeholder: \"my-awesome-project\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: [\n                                                        \"No spaces or caps allowed. Use hyphens for multi-word projects.\",\n                                                        projectName && projectName !== validateProjectName(projectName) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-orange-600 ml-1\",\n                                                            children: [\n                                                                \"→ Will be: \",\n                                                                validateProjectName(projectName)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"path\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Path\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"path\",\n                                                    value: projectPath,\n                                                    onChange: (e)=>setProjectPath(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"Documents/ViceProjects/\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"description\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                    id: \"description\",\n                                                    value: projectDescription,\n                                                    onChange: (e)=>setProjectDescription(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"A brief description of your project\",\n                                                    rows: 3\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-2 mb-4 ml-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-xs text-muted-foreground flex items-center gap-1 p-0 h-auto\",\n                                                onClick: ()=>setShowCommands(true),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3.5 w-3.5 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"See Starting Commands\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-3.5 w-3.5 ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                            className: \"flex justify-end items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>onOpenChange(false),\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        onClick: handleCreate,\n                                                        disabled: isCreating,\n                                                        children: isCreating ? 'Creating...' : 'Create Project'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-0' : 'translate-x-[100%]'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 ml-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"pl-0 flex items-center gap-1\",\n                                        onClick: ()=>setShowCommands(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Project Form\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                            children: [\n                                                \"Starting Commands for \",\n                                                stackName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                            children: [\n                                                \"Use these commands to set up your \",\n                                                stackName,\n                                                \" project manually.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-4 px-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-muted p-4 rounded-md font-mono text-xs sm:text-sm overflow-auto max-h-[250px]\",\n                                        children: ((_startingCommands_stackName = startingCommands[stackName]) === null || _startingCommands_stackName === void 0 ? void 0 : _startingCommands_stackName.map((command, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground mr-2 flex-shrink-0\",\n                                                        children: \"$\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"break-all\",\n                                                        children: command\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                \"No commands available for \",\n                                                stackName,\n                                                \".\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>onOpenChange(false),\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n            lineNumber: 180,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateProjectModal, \"OsRObgBhEhRpnfmwQeAsealTMak=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = CreateProjectModal;\nvar _c;\n$RefreshReg$(_c, \"CreateProjectModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3Byb2plY3QvY3JlYXRlLXByb2plY3QtbW9kYWwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDcEI7QUFRQztBQUNnQjtBQUNGO0FBQ0E7QUFDTTtBQUNQO0FBQ29CO0FBVTFELFNBQVNrQixtQkFBbUIsS0FLVDtRQUxTLEVBQ2pDQyxJQUFJLEVBQ0pDLFlBQVksRUFDWkMsU0FBUyxFQUNUQyxlQUFlLEVBQUUsRUFDTyxHQUxTO1FBMFJsQkM7O0lBcFJmLE1BQU0sRUFBRUMsS0FBSyxFQUFFLEdBQUdWLDBEQUFRQTtJQUMxQixNQUFNLENBQUNXLGFBQWFDLGVBQWUsR0FBR3pCLCtDQUFRQSxDQUFDcUI7SUFDL0MsTUFBTSxDQUFDSyxhQUFhQyxlQUFlLEdBQUczQiwrQ0FBUUEsQ0FBQyw2Q0FBcUUsT0FBeEJvQixVQUFVUSxXQUFXO0lBQ2pILE1BQU0sQ0FBQ0Msb0JBQW9CQyxzQkFBc0IsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQytCLFlBQVlDLGNBQWMsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2lDLGNBQWNDLGdCQUFnQixHQUFHbEMsK0NBQVFBLENBQUM7SUFFakQscUNBQXFDO0lBQ3JDQyxnREFBU0E7d0NBQUM7WUFDUjBCLGVBQWUsNkNBQXFFLE9BQXhCUCxVQUFVUSxXQUFXO1FBQ25GO3VDQUFHO1FBQUNSO0tBQVU7SUFFZCxxRUFBcUU7SUFDckUsTUFBTWUsc0JBQXNCLENBQUNDO1FBQzNCLE9BQU9BLEtBQUtDLElBQUksR0FBR1QsV0FBVyxHQUFHVSxPQUFPLENBQUMsUUFBUSxLQUFLQSxPQUFPLENBQUMsZUFBZTtJQUMvRTtJQUVBLHNEQUFzRDtJQUN0RCxNQUFNQywwQkFBMEIsQ0FBQ0M7UUFDL0IsTUFBTUMsUUFBUUQsRUFBRUUsTUFBTSxDQUFDRCxLQUFLO1FBQzVCaEIsZUFBZWdCO0lBQ2pCO0lBRUEseUNBQXlDO0lBQ3pDLE1BQU1uQixtQkFBbUI7UUFDdkIsUUFBUTtZQUNOO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUNELFFBQVE7WUFDTjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDRCxVQUFVO1lBQ1I7WUFDQTtZQUNBO1NBQ0Q7UUFDRCxVQUFVO1lBQ1I7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0QsU0FBUztZQUNQO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUNELFFBQVE7WUFDTjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7SUFDSDtJQUVBLE1BQU1xQixlQUFlO1FBQ25CLElBQUksQ0FBQ25CLFlBQVlhLElBQUksSUFBSTtZQUN2QmQsTUFBTTtnQkFDSnFCLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtZQUNBO1FBQ0Y7UUFFQSxtQ0FBbUM7UUFDbkMsTUFBTUMsdUJBQXVCWixvQkFBb0JYO1FBQ2pELElBQUksQ0FBQ3VCLHNCQUFzQjtZQUN6QnhCLE1BQU07Z0JBQ0pxQixPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxTQUFTO1lBQ1g7WUFDQTtRQUNGO1FBRUEsSUFBSUMseUJBQXlCdkIsWUFBWWEsSUFBSSxHQUFHVCxXQUFXLEdBQUdVLE9BQU8sQ0FBQyxRQUFRLE1BQU07WUFDbEZmLE1BQU07Z0JBQ0pxQixPQUFPO2dCQUNQQyxhQUFhLDRCQUFpRCxPQUFyQkU7WUFDM0M7WUFDQXRCLGVBQWVzQjtRQUNqQjtRQUVBZixjQUFjO1FBRWQsSUFBSTtZQUNGLHlEQUF5RDtZQUN6RCxJQUFJZ0IsT0FBT0MsV0FBVyxJQUFJRCxPQUFPQyxXQUFXLENBQUNDLGtCQUFrQixFQUFFO2dCQUMvRCxNQUFNQyxTQUFTLE1BQU1ILE9BQU9DLFdBQVcsQ0FBQ0Msa0JBQWtCLENBQUM7b0JBQ3pEMUIsYUFBYXVCO29CQUNickI7b0JBQ0FOO29CQUNBeUIsYUFBYWhCO2dCQUNmO2dCQUVBLElBQUlzQixVQUFVQSxPQUFPQyxPQUFPLEVBQUU7b0JBQzVCLE1BQU1DLG1CQUFtQixPQUFnQkMsVUFBVSxJQUFJNUI7b0JBRXZELDZDQUE2QztvQkFDN0MsTUFBTTZCLFNBQVMsaUNBQWtGQyxPQUFqREEsbUJBQW1CVCx1QkFBc0IsVUFBNkMsT0FBckNTLG1CQUFtQkg7b0JBQ3BITCxPQUFPOUIsSUFBSSxDQUFDcUMsUUFBUTtvQkFFcEJoQyxNQUFNO3dCQUNKcUIsT0FBTzt3QkFDUEMsYUFBYSxHQUF3QixPQUFyQkUsc0JBQXFCO29CQUN2QztvQkFFQSw2QkFBNkI7b0JBQzdCdEIsZUFBZTtvQkFDZkssc0JBQXNCO29CQUN0QlgsYUFBYTtnQkFFZixPQUFPO29CQUNMLE1BQU0sSUFBSXNDLE1BQU0sQ0FBQ04sbUJBQUFBLDZCQUFELE9BQWlCTyxPQUFPLEtBQUk7Z0JBQzlDO1lBQ0YsT0FBTztnQkFDTCxNQUFNLElBQUlELE1BQU07WUFDbEI7UUFDRixFQUFFLE9BQU9FLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHFDQUFxQ0E7WUFFbkRwQyxNQUFNO2dCQUNKcUIsT0FBTztnQkFDUEMsYUFBYWMsaUJBQWlCRixRQUFRRSxNQUFNRCxPQUFPLEdBQUc7Z0JBQ3REWixTQUFTO1lBQ1g7UUFDRixTQUFVO1lBQ1JkLGNBQWM7UUFDaEI7SUFDRjtJQUVBLHFCQUNFLDhEQUFDN0IseURBQU1BO1FBQUNlLE1BQU1BO1FBQU1DLGNBQWNBO2tCQUNoQyw0RUFBQ2YsZ0VBQWFBO1lBQUN5RCxXQUFVOzs4QkFFdkIsOERBQUNDO29CQUFJRCxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTtrQ0FDYiw0RUFBQzNELGtEQUFLQTs0QkFDSjZELEtBQUk7NEJBQ0pDLEtBQUk7NEJBQ0pDLElBQUk7NEJBQ0pDLE9BQU87Z0NBQUVDLFdBQVc7NEJBQVU7NEJBQzlCQyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7OzhCQU1kLDhEQUFDTjtvQkFBSUQsV0FBVTs7c0NBRWIsOERBQUNDOzRCQUNDRCxXQUFXLGlEQUVWLE9BREM1QixlQUFlLHdCQUF3Qjs7OENBR3pDLDhEQUFDMUIsK0RBQVlBO29DQUFDc0QsV0FBVTs7c0RBQ3RCLDhEQUFDckQsOERBQVdBOzRDQUFDcUQsV0FBVTtzREFBYzs7Ozs7O3NEQUNyQyw4REFBQ3hELG9FQUFpQkE7NENBQUN3RCxXQUFVOztnREFBNEM7Z0RBQ3pEekM7Z0RBQVU7Ozs7Ozs7Ozs7Ozs7OENBRzVCLDhEQUFDMEM7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNsRCx1REFBS0E7b0RBQUMwRCxTQUFRO29EQUFPUixXQUFVOzhEQUFzQjs7Ozs7OzhEQUd0RCw4REFBQ25ELHVEQUFLQTtvREFDSjRELElBQUc7b0RBQ0g3QixPQUFPakI7b0RBQ1ArQyxVQUFVaEM7b0RBQ1ZzQixXQUFVO29EQUNWVyxhQUFZO29EQUNaQyxTQUFTOzs7Ozs7OERBRVgsOERBQUNDO29EQUFFYixXQUFVOzt3REFBZ0M7d0RBRTFDckMsZUFBZUEsZ0JBQWdCVyxvQkFBb0JYLDhCQUNsRCw4REFBQ21EOzREQUFLZCxXQUFVOztnRUFBdUI7Z0VBQ3pCMUIsb0JBQW9CWDs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFLeEMsOERBQUNzQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNsRCx1REFBS0E7b0RBQUMwRCxTQUFRO29EQUFPUixXQUFVOzhEQUFzQjs7Ozs7OzhEQUd0RCw4REFBQ25ELHVEQUFLQTtvREFDSjRELElBQUc7b0RBQ0g3QixPQUFPZjtvREFDUDZDLFVBQVUsQ0FBQy9CLElBQU1iLGVBQWVhLEVBQUVFLE1BQU0sQ0FBQ0QsS0FBSztvREFDOUNvQixXQUFVO29EQUNWVyxhQUFZOzs7Ozs7Ozs7Ozs7c0RBR2hCLDhEQUFDVjs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNsRCx1REFBS0E7b0RBQUMwRCxTQUFRO29EQUFjUixXQUFVOzhEQUFzQjs7Ozs7OzhEQUc3RCw4REFBQ2pELDZEQUFRQTtvREFDUDBELElBQUc7b0RBQ0g3QixPQUFPWjtvREFDUDBDLFVBQVUsQ0FBQy9CLElBQU1WLHNCQUFzQlUsRUFBRUUsTUFBTSxDQUFDRCxLQUFLO29EQUNyRG9CLFdBQVU7b0RBQ1ZXLGFBQVk7b0RBQ1pJLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJWiw4REFBQ2Q7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTtzREFDYiw0RUFBQ3BELHlEQUFNQTtnREFDTHFDLFNBQVE7Z0RBQ1IrQixNQUFLO2dEQUNMaEIsV0FBVTtnREFDVmlCLFNBQVMsSUFBTTVDLGdCQUFnQjs7a0VBRS9CLDhEQUFDbEIsMkdBQVFBO3dEQUFDNkMsV0FBVTs7Ozs7O29EQUFxQjtrRUFFekMsOERBQUM5Qyw0R0FBWUE7d0RBQUM4QyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzREFJNUIsOERBQUN2RCwrREFBWUE7NENBQUN1RCxXQUFVO3NEQUN0Qiw0RUFBQ0M7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDcEQseURBQU1BO3dEQUFDcUMsU0FBUTt3REFBVWdDLFNBQVMsSUFBTTNELGFBQWE7a0VBQVE7Ozs7OztrRUFHOUQsOERBQUNWLHlEQUFNQTt3REFBQ3FFLFNBQVNuQzt3REFBY29DLFVBQVVoRDtrRUFDdENBLGFBQWEsZ0JBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FReEMsOERBQUMrQjs0QkFDQ0QsV0FBVyxxRkFFVixPQURDNUIsZUFBZSxrQkFBa0I7OzhDQUduQyw4REFBQzZCO29DQUFJRCxXQUFVOzhDQUNiLDRFQUFDcEQseURBQU1BO3dDQUNMcUMsU0FBUTt3Q0FDUitCLE1BQUs7d0NBQ0xoQixXQUFVO3dDQUNWaUIsU0FBUyxJQUFNNUMsZ0JBQWdCOzswREFFL0IsOERBQUNwQiw0R0FBU0E7Z0RBQUMrQyxXQUFVOzs7Ozs7NENBQVk7Ozs7Ozs7Ozs7Ozs4Q0FLckMsOERBQUN0RCwrREFBWUE7O3NEQUNYLDhEQUFDQyw4REFBV0E7O2dEQUFDO2dEQUF1Qlk7Ozs7Ozs7c0RBQ3BDLDhEQUFDZixvRUFBaUJBOztnREFBQztnREFDa0JlO2dEQUFVOzs7Ozs7Ozs7Ozs7OzhDQUlqRCw4REFBQzBDO29DQUFJRCxXQUFVOzhDQUNiLDRFQUFDQzt3Q0FBSUQsV0FBVTtrREFDWnZDLEVBQUFBLDhCQUFBQSxnQkFBZ0IsQ0FBQ0YsVUFBMkMsY0FBNURFLGtEQUFBQSw0QkFBOEQwRCxHQUFHLENBQUMsQ0FBQ0MsU0FBU0Msc0JBQzNFLDhEQUFDcEI7Z0RBQWdCRCxXQUFVOztrRUFDekIsOERBQUNjO3dEQUFLZCxXQUFVO2tFQUEyQzs7Ozs7O2tFQUMzRCw4REFBQ2M7d0RBQUtkLFdBQVU7a0VBQWFvQjs7Ozs7OzsrQ0FGckJDOzs7O3dFQUtWLDhEQUFDcEI7NENBQUlELFdBQVU7O2dEQUF3QjtnREFBMkJ6QztnREFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS2xGLDhEQUFDZCwrREFBWUE7OENBQ1gsNEVBQUNHLHlEQUFNQTt3Q0FBQ3FDLFNBQVE7d0NBQVVnQyxTQUFTLElBQU0zRCxhQUFhO2tEQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUzVFO0dBL1NnQkY7O1FBTUlKLHNEQUFRQTs7O0tBTlpJIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXE5ldyBmb2xkZXJcXFZpY2VcXFZpY2VcXHNyY1xcY29tcG9uZW50c1xccHJvamVjdFxcY3JlYXRlLXByb2plY3QtbW9kYWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgSW1hZ2UgZnJvbSAnbmV4dC9pbWFnZSc7XG5pbXBvcnQge1xuICBEaWFsb2csXG4gIERpYWxvZ0NvbnRlbnQsXG4gIERpYWxvZ0Rlc2NyaXB0aW9uLFxuICBEaWFsb2dGb290ZXIsXG4gIERpYWxvZ0hlYWRlcixcbiAgRGlhbG9nVGl0bGUsXG59IGZyb20gJ0AvY29tcG9uZW50cy91aS9kaWFsb2cnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9pbnB1dCc7XG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9sYWJlbCc7XG5pbXBvcnQgeyBUZXh0YXJlYSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS90ZXh0YXJlYSc7XG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ0AvaG9va3MvdXNlLXRvYXN0JztcbmltcG9ydCB7IEFycm93TGVmdCwgQ2hldnJvblJpZ2h0LCBUZXJtaW5hbCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgUHJvamVjdENyZWF0aW9uTG9hZGluZyBmcm9tICcuL3Byb2plY3QtY3JlYXRpb24tbG9hZGluZyc7XG5cbmludGVyZmFjZSBDcmVhdGVQcm9qZWN0TW9kYWxQcm9wcyB7XG4gIG9wZW46IGJvb2xlYW47XG4gIG9uT3BlbkNoYW5nZTogKG9wZW46IGJvb2xlYW4pID0+IHZvaWQ7XG4gIHN0YWNrTmFtZTogc3RyaW5nO1xuICBwcm9qZWN0VGl0bGU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBDcmVhdGVQcm9qZWN0TW9kYWwoe1xuICBvcGVuLFxuICBvbk9wZW5DaGFuZ2UsXG4gIHN0YWNrTmFtZSxcbiAgcHJvamVjdFRpdGxlID0gJycsXG59OiBDcmVhdGVQcm9qZWN0TW9kYWxQcm9wcykge1xuICBjb25zdCB7IHRvYXN0IH0gPSB1c2VUb2FzdCgpO1xuICBjb25zdCBbcHJvamVjdE5hbWUsIHNldFByb2plY3ROYW1lXSA9IHVzZVN0YXRlKHByb2plY3RUaXRsZSk7XG4gIGNvbnN0IFtwcm9qZWN0UGF0aCwgc2V0UHJvamVjdFBhdGhdID0gdXNlU3RhdGUoYEM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcRG9jdW1lbnRzXFxcXFZpY2VQcm9qZWN0c1xcXFwke3N0YWNrTmFtZS50b0xvd2VyQ2FzZSgpfWApO1xuICBjb25zdCBbcHJvamVjdERlc2NyaXB0aW9uLCBzZXRQcm9qZWN0RGVzY3JpcHRpb25dID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbaXNDcmVhdGluZywgc2V0SXNDcmVhdGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93Q29tbWFuZHMsIHNldFNob3dDb21tYW5kc10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gVXBkYXRlIHBhdGggd2hlbiBzdGFja05hbWUgY2hhbmdlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldFByb2plY3RQYXRoKGBDOlxcXFxVc2Vyc1xcXFxVc2VyXFxcXERvY3VtZW50c1xcXFxWaWNlUHJvamVjdHNcXFxcJHtzdGFja05hbWUudG9Mb3dlckNhc2UoKX1gKTtcbiAgfSwgW3N0YWNrTmFtZV0pO1xuXG4gIC8vIFZhbGlkYXRlIGFuZCBmb3JtYXQgcHJvamVjdCBuYW1lIChubyBzcGFjZXMsIG5vIGNhcHMsIHVzZSBoeXBoZW5zKVxuICBjb25zdCB2YWxpZGF0ZVByb2plY3ROYW1lID0gKG5hbWU6IHN0cmluZykgPT4ge1xuICAgIHJldHVybiBuYW1lLnRyaW0oKS50b0xvd2VyQ2FzZSgpLnJlcGxhY2UoL1xccysvZywgJy0nKS5yZXBsYWNlKC9bXmEtejAtOS1dL2csICcnKTtcbiAgfTtcblxuICAvLyBIYW5kbGUgcHJvamVjdCBuYW1lIGlucHV0IHdpdGggcmVhbC10aW1lIHZhbGlkYXRpb25cbiAgY29uc3QgaGFuZGxlUHJvamVjdE5hbWVDaGFuZ2UgPSAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCB2YWx1ZSA9IGUudGFyZ2V0LnZhbHVlO1xuICAgIHNldFByb2plY3ROYW1lKHZhbHVlKTtcbiAgfTtcblxuICAvLyBTdGFydGluZyBjb21tYW5kcyBmb3IgZGlmZmVyZW50IHN0YWNrc1xuICBjb25zdCBzdGFydGluZ0NvbW1hbmRzID0ge1xuICAgICdNZXJuJzogW1xuICAgICAgJ25weCBjcmVhdGUtcmVhY3QtYXBwIG15LWFwcCcsXG4gICAgICAnY2QgbXktYXBwJyxcbiAgICAgICducG0gaW5zdGFsbCBleHByZXNzIG1vbmdvb3NlIGNvcnMgZG90ZW52JyxcbiAgICAgICdta2RpciBzZXJ2ZXInLFxuICAgICAgJ3RvdWNoIHNlcnZlci9pbmRleC5qcyBzZXJ2ZXIvbW9kZWxzL1VzZXIuanMgc2VydmVyL3JvdXRlcy9hcGkuanMnXG4gICAgXSxcbiAgICAnUGVybic6IFtcbiAgICAgICducHggY3JlYXRlLXJlYWN0LWFwcCBteS1hcHAnLFxuICAgICAgJ2NkIG15LWFwcCcsXG4gICAgICAnbnBtIGluc3RhbGwgZXhwcmVzcyBwZyBjb3JzIGRvdGVudicsXG4gICAgICAnbWtkaXIgc2VydmVyJyxcbiAgICAgICd0b3VjaCBzZXJ2ZXIvaW5kZXguanMgc2VydmVyL2RiLmpzIHNlcnZlci9yb3V0ZXMvYXBpLmpzJ1xuICAgIF0sXG4gICAgJ05leHRKUyc6IFtcbiAgICAgICducHggY3JlYXRlLW5leHQtYXBwQGxhdGVzdCBteS1hcHAnLFxuICAgICAgJ2NkIG15LWFwcCcsXG4gICAgICAnbnBtIGluc3RhbGwnXG4gICAgXSxcbiAgICAnRGphbmdvJzogW1xuICAgICAgJ3B5dGhvbiAtbSB2ZW52IHZlbnYnLFxuICAgICAgJ3ZlbnZcXFxcU2NyaXB0c1xcXFxhY3RpdmF0ZScsXG4gICAgICAncGlwIGluc3RhbGwgZGphbmdvIGRqYW5nb3Jlc3RmcmFtZXdvcmsnLFxuICAgICAgJ2RqYW5nby1hZG1pbiBzdGFydHByb2plY3QgbXlwcm9qZWN0IC4nLFxuICAgICAgJ3B5dGhvbiBtYW5hZ2UucHkgc3RhcnRhcHAgbXlhcHAnXG4gICAgXSxcbiAgICAnRmxhc2snOiBbXG4gICAgICAncHl0aG9uIC1tIHZlbnYgdmVudicsXG4gICAgICAndmVudlxcXFxTY3JpcHRzXFxcXGFjdGl2YXRlJyxcbiAgICAgICdwaXAgaW5zdGFsbCBmbGFzayBmbGFzay1zcWxhbGNoZW15IGZsYXNrLWNvcnMnLFxuICAgICAgJ21rZGlyIGFwcCcsXG4gICAgICAndG91Y2ggYXBwL19faW5pdF9fLnB5IGFwcC9yb3V0ZXMucHkgYXBwL21vZGVscy5weSdcbiAgICBdLFxuICAgICdCYXNoJzogW1xuICAgICAgJ21rZGlyIG15LXNjcmlwdCcsXG4gICAgICAnY2QgbXktc2NyaXB0JyxcbiAgICAgICd0b3VjaCBzY3JpcHQuc2gnLFxuICAgICAgJ2NobW9kICt4IHNjcmlwdC5zaCcsXG4gICAgICAnZWNobyBcIiMhL2Jpbi9iYXNoXCIgPiBzY3JpcHQuc2gnXG4gICAgXVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNyZWF0ZSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXByb2plY3ROYW1lLnRyaW0oKSkge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ1Byb2plY3QgbmFtZSByZXF1aXJlZCcsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnUGxlYXNlIGVudGVyIGEgbmFtZSBmb3IgeW91ciBwcm9qZWN0LicsXG4gICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsXG4gICAgICB9KTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBWYWxpZGF0ZSBhbmQgZm9ybWF0IHByb2plY3QgbmFtZVxuICAgIGNvbnN0IGZvcm1hdHRlZFByb2plY3ROYW1lID0gdmFsaWRhdGVQcm9qZWN0TmFtZShwcm9qZWN0TmFtZSk7XG4gICAgaWYgKCFmb3JtYXR0ZWRQcm9qZWN0TmFtZSkge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ0ludmFsaWQgcHJvamVjdCBuYW1lJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdQcm9qZWN0IG5hbWUgbXVzdCBjb250YWluIGF0IGxlYXN0IG9uZSBsZXR0ZXIgb3IgbnVtYmVyLicsXG4gICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsXG4gICAgICB9KTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAoZm9ybWF0dGVkUHJvamVjdE5hbWUgIT09IHByb2plY3ROYW1lLnRyaW0oKS50b0xvd2VyQ2FzZSgpLnJlcGxhY2UoL1xccysvZywgJy0nKSkge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ1Byb2plY3QgbmFtZSBmb3JtYXR0ZWQnLFxuICAgICAgICBkZXNjcmlwdGlvbjogYFByb2plY3QgbmFtZSBjaGFuZ2VkIHRvOiAke2Zvcm1hdHRlZFByb2plY3ROYW1lfWAsXG4gICAgICB9KTtcbiAgICAgIHNldFByb2plY3ROYW1lKGZvcm1hdHRlZFByb2plY3ROYW1lKTtcbiAgICB9XG5cbiAgICBzZXRJc0NyZWF0aW5nKHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIENyZWF0ZSBwcm9qZWN0IHVzaW5nIEVsZWN0cm9uIEFQSSAtIE5PIEZJTEVTIFBBUkFNRVRFUlxuICAgICAgaWYgKHdpbmRvdy5lbGVjdHJvbkFQSSAmJiB3aW5kb3cuZWxlY3Ryb25BUEkuY3JlYXRlUHJvamVjdEZpbGVzKSB7XG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHdpbmRvdy5lbGVjdHJvbkFQSS5jcmVhdGVQcm9qZWN0RmlsZXMoe1xuICAgICAgICAgIHByb2plY3ROYW1lOiBmb3JtYXR0ZWRQcm9qZWN0TmFtZSxcbiAgICAgICAgICBwcm9qZWN0UGF0aCxcbiAgICAgICAgICBzdGFja05hbWUsXG4gICAgICAgICAgZGVzY3JpcHRpb246IHByb2plY3REZXNjcmlwdGlvblxuICAgICAgICB9IGFzIGFueSk7XG5cbiAgICAgICAgaWYgKHJlc3VsdCAmJiByZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICAgIGNvbnN0IGZpbmFsUHJvamVjdFBhdGggPSAocmVzdWx0IGFzIGFueSkuYWN0dWFsUGF0aCB8fCBwcm9qZWN0UGF0aDtcblxuICAgICAgICAgIC8vIE9wZW4gSURFIHdpdGggcHJvamVjdCAtIE5PIEZJTEVTIFBBUkFNRVRFUlxuICAgICAgICAgIGNvbnN0IGlkZVVybCA9IGBodHRwOi8vbG9jYWxob3N0OjkwMDM/cHJvamVjdD0ke2VuY29kZVVSSUNvbXBvbmVudChmb3JtYXR0ZWRQcm9qZWN0TmFtZSl9JnBhdGg9JHtlbmNvZGVVUklDb21wb25lbnQoZmluYWxQcm9qZWN0UGF0aCl9YDtcbiAgICAgICAgICB3aW5kb3cub3BlbihpZGVVcmwsICdfYmxhbmsnKTtcblxuICAgICAgICAgIHRvYXN0KHtcbiAgICAgICAgICAgIHRpdGxlOiAnUHJvamVjdCBDcmVhdGVkIFN1Y2Nlc3NmdWxseScsXG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogYCR7Zm9ybWF0dGVkUHJvamVjdE5hbWV9IGhhcyBiZWVuIGNyZWF0ZWQgYW5kIGlzIG9wZW5pbmcgaW4gdGhlIElERS5gLFxuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgLy8gUmVzZXQgZm9ybSBhbmQgY2xvc2UgbW9kYWxcbiAgICAgICAgICBzZXRQcm9qZWN0TmFtZSgnJyk7XG4gICAgICAgICAgc2V0UHJvamVjdERlc2NyaXB0aW9uKCcnKTtcbiAgICAgICAgICBvbk9wZW5DaGFuZ2UoZmFsc2UpO1xuXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKChyZXN1bHQgYXMgYW55KT8ubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGNyZWF0ZSBwcm9qZWN0Jyk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRWxlY3Ryb24gQVBJIG5vdCBhdmFpbGFibGUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignW1BST0pFQ1RdIEVycm9yIGNyZWF0aW5nIHByb2plY3Q6JywgZXJyb3IpO1xuXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAnRmFpbGVkIHRvIGNyZWF0ZSBwcm9qZWN0JyxcbiAgICAgICAgZGVzY3JpcHRpb246IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0FuIGVycm9yIG9jY3VycmVkIHdoaWxlIGNyZWF0aW5nIHRoZSBwcm9qZWN0LicsXG4gICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsXG4gICAgICB9KTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNDcmVhdGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPERpYWxvZyBvcGVuPXtvcGVufSBvbk9wZW5DaGFuZ2U9e29uT3BlbkNoYW5nZX0+XG4gICAgICA8RGlhbG9nQ29udGVudCBjbGFzc05hbWU9XCJzbTptYXgtdy1bNTUwcHhdIG1kOm1heC13LVs2MDBweF0gcm91bmRlZC14bCBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgey8qIExvZ28gYXQgdGhlIHRvcCBjZW50ZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBtYi0yXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBoLTIwIHctNDBcIj5cbiAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICBzcmM9XCIvbG9nby5wbmdcIlxuICAgICAgICAgICAgICBhbHQ9XCJWSUNFIExvZ29cIlxuICAgICAgICAgICAgICBmaWxsXG4gICAgICAgICAgICAgIHN0eWxlPXt7IG9iamVjdEZpdDogJ2NvbnRhaW4nIH19XG4gICAgICAgICAgICAgIHByaW9yaXR5XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQ29udGVudCBjb250YWluZXIgd2l0aCBhbmltYXRpb24gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgey8qIFByb2plY3QgRm9ybSAqL31cbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBjbGFzc05hbWU9e2B0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgJHtcbiAgICAgICAgICAgICAgc2hvd0NvbW1hbmRzID8gJ3RyYW5zbGF0ZS14LVstMTAwJV0nIDogJ3RyYW5zbGF0ZS14LTAnXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8RGlhbG9nSGVhZGVyIGNsYXNzTmFtZT1cIm1iLVs1XSBwYi0yXCI+XG4gICAgICAgICAgICAgIDxEaWFsb2dUaXRsZSBjbGFzc05hbWU9J3RleHQtY2VudGVyJz5DcmVhdGUgTmV3IFByb2plY3Q8L0RpYWxvZ1RpdGxlPlxuICAgICAgICAgICAgICA8RGlhbG9nRGVzY3JpcHRpb24gY2xhc3NOYW1lPSd0ZXh0LWNlbnRlciB0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCc+XG4gICAgICAgICAgICAgICAgQ3JlYXRlIGEgbmV3IHtzdGFja05hbWV9IHByb2plY3QuIEZpbGwgaW4gdGhlIGRldGFpbHMgYmVsb3cgdG8gZ2V0IHN0YXJ0ZWQuXG4gICAgICAgICAgICAgIDwvRGlhbG9nRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0RpYWxvZ0hlYWRlcj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtNCBweS00IHB4LTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC0xLjVcIj5cbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cIm5hbWVcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICBOYW1lXG4gICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgIGlkPVwibmFtZVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17cHJvamVjdE5hbWV9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlUHJvamVjdE5hbWVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJteS1hd2Vzb21lLXByb2plY3RcIlxuICAgICAgICAgICAgICAgICAgYXV0b0ZvY3VzXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgTm8gc3BhY2VzIG9yIGNhcHMgYWxsb3dlZC4gVXNlIGh5cGhlbnMgZm9yIG11bHRpLXdvcmQgcHJvamVjdHMuXG4gICAgICAgICAgICAgICAgICB7cHJvamVjdE5hbWUgJiYgcHJvamVjdE5hbWUgIT09IHZhbGlkYXRlUHJvamVjdE5hbWUocHJvamVjdE5hbWUpICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1vcmFuZ2UtNjAwIG1sLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICDihpIgV2lsbCBiZToge3ZhbGlkYXRlUHJvamVjdE5hbWUocHJvamVjdE5hbWUpfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBnYXAtMS41XCI+XG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJwYXRoXCIgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgUGF0aFxuICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICBpZD1cInBhdGhcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3Byb2plY3RQYXRofVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRQcm9qZWN0UGF0aChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJEb2N1bWVudHMvVmljZVByb2plY3RzL1wiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBnYXAtMS41XCI+XG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJkZXNjcmlwdGlvblwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgIERlc2NyaXB0aW9uXG4gICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgIGlkPVwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3Byb2plY3REZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UHJvamVjdERlc2NyaXB0aW9uKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkEgYnJpZWYgZGVzY3JpcHRpb24gb2YgeW91ciBwcm9qZWN0XCJcbiAgICAgICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG10LTIgbWItNCBtbC0zXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMSBwLTAgaC1hdXRvXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDb21tYW5kcyh0cnVlKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8VGVybWluYWwgY2xhc3NOYW1lPVwiaC0zLjUgdy0zLjUgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICBTZWUgU3RhcnRpbmcgQ29tbWFuZHNcbiAgICAgICAgICAgICAgICAgIDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwiaC0zLjUgdy0zLjUgbWwtMVwiIC8+XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxEaWFsb2dGb290ZXIgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXsoKSA9PiBvbk9wZW5DaGFuZ2UoZmFsc2UpfT5cbiAgICAgICAgICAgICAgICAgICAgQ2FuY2VsXG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17aGFuZGxlQ3JlYXRlfSBkaXNhYmxlZD17aXNDcmVhdGluZ30+XG4gICAgICAgICAgICAgICAgICAgIHtpc0NyZWF0aW5nID8gJ0NyZWF0aW5nLi4uJyA6ICdDcmVhdGUgUHJvamVjdCd9XG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9EaWFsb2dGb290ZXI+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBTdGFydGluZyBDb21tYW5kcyAqL31cbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBjbGFzc05hbWU9e2BhYnNvbHV0ZSB0b3AtMCBsZWZ0LTAgdy1mdWxsIGgtZnVsbCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgJHtcbiAgICAgICAgICAgICAgc2hvd0NvbW1hbmRzID8gJ3RyYW5zbGF0ZS14LTAnIDogJ3RyYW5zbGF0ZS14LVsxMDAlXSdcbiAgICAgICAgICAgIH1gfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNCBtbC0zXCI+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicGwtMCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0NvbW1hbmRzKGZhbHNlKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxBcnJvd0xlZnQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgQmFjayB0byBQcm9qZWN0IEZvcm1cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPERpYWxvZ0hlYWRlcj5cbiAgICAgICAgICAgICAgPERpYWxvZ1RpdGxlPlN0YXJ0aW5nIENvbW1hbmRzIGZvciB7c3RhY2tOYW1lfTwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgICAgIDxEaWFsb2dEZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICBVc2UgdGhlc2UgY29tbWFuZHMgdG8gc2V0IHVwIHlvdXIge3N0YWNrTmFtZX0gcHJvamVjdCBtYW51YWxseS5cbiAgICAgICAgICAgICAgPC9EaWFsb2dEZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvRGlhbG9nSGVhZGVyPlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB5LTQgcHgtM1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLW11dGVkIHAtNCByb3VuZGVkLW1kIGZvbnQtbW9ubyB0ZXh0LXhzIHNtOnRleHQtc20gb3ZlcmZsb3ctYXV0byBtYXgtaC1bMjUwcHhdXCI+XG4gICAgICAgICAgICAgICAge3N0YXJ0aW5nQ29tbWFuZHNbc3RhY2tOYW1lIGFzIGtleW9mIHR5cGVvZiBzdGFydGluZ0NvbW1hbmRzXT8ubWFwKChjb21tYW5kLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1yLTIgZmxleC1zaHJpbmstMFwiPiQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJyZWFrLWFsbFwiPntjb21tYW5kfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpIHx8IChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+Tm8gY29tbWFuZHMgYXZhaWxhYmxlIGZvciB7c3RhY2tOYW1lfS48L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8RGlhbG9nRm9vdGVyPlxuICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17KCkgPT4gb25PcGVuQ2hhbmdlKGZhbHNlKX0+XG4gICAgICAgICAgICAgICAgQ2xvc2VcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L0RpYWxvZ0Zvb3Rlcj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0RpYWxvZ0NvbnRlbnQ+XG4gICAgPC9EaWFsb2c+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkltYWdlIiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0Rlc2NyaXB0aW9uIiwiRGlhbG9nRm9vdGVyIiwiRGlhbG9nSGVhZGVyIiwiRGlhbG9nVGl0bGUiLCJCdXR0b24iLCJJbnB1dCIsIkxhYmVsIiwiVGV4dGFyZWEiLCJ1c2VUb2FzdCIsIkFycm93TGVmdCIsIkNoZXZyb25SaWdodCIsIlRlcm1pbmFsIiwiQ3JlYXRlUHJvamVjdE1vZGFsIiwib3BlbiIsIm9uT3BlbkNoYW5nZSIsInN0YWNrTmFtZSIsInByb2plY3RUaXRsZSIsInN0YXJ0aW5nQ29tbWFuZHMiLCJ0b2FzdCIsInByb2plY3ROYW1lIiwic2V0UHJvamVjdE5hbWUiLCJwcm9qZWN0UGF0aCIsInNldFByb2plY3RQYXRoIiwidG9Mb3dlckNhc2UiLCJwcm9qZWN0RGVzY3JpcHRpb24iLCJzZXRQcm9qZWN0RGVzY3JpcHRpb24iLCJpc0NyZWF0aW5nIiwic2V0SXNDcmVhdGluZyIsInNob3dDb21tYW5kcyIsInNldFNob3dDb21tYW5kcyIsInZhbGlkYXRlUHJvamVjdE5hbWUiLCJuYW1lIiwidHJpbSIsInJlcGxhY2UiLCJoYW5kbGVQcm9qZWN0TmFtZUNoYW5nZSIsImUiLCJ2YWx1ZSIsInRhcmdldCIsImhhbmRsZUNyZWF0ZSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJ2YXJpYW50IiwiZm9ybWF0dGVkUHJvamVjdE5hbWUiLCJ3aW5kb3ciLCJlbGVjdHJvbkFQSSIsImNyZWF0ZVByb2plY3RGaWxlcyIsInJlc3VsdCIsInN1Y2Nlc3MiLCJmaW5hbFByb2plY3RQYXRoIiwiYWN0dWFsUGF0aCIsImlkZVVybCIsImVuY29kZVVSSUNvbXBvbmVudCIsIkVycm9yIiwibWVzc2FnZSIsImVycm9yIiwiY29uc29sZSIsImNsYXNzTmFtZSIsImRpdiIsInNyYyIsImFsdCIsImZpbGwiLCJzdHlsZSIsIm9iamVjdEZpdCIsInByaW9yaXR5IiwiaHRtbEZvciIsImlkIiwib25DaGFuZ2UiLCJwbGFjZWhvbGRlciIsImF1dG9Gb2N1cyIsInAiLCJzcGFuIiwicm93cyIsInNpemUiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJtYXAiLCJjb21tYW5kIiwiaW5kZXgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/create-project-modal.tsx\n"));

/***/ })

});