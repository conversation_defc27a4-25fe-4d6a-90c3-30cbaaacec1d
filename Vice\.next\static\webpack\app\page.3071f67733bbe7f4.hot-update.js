"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js":
/*!**********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-check-big.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ CircleCheckBig)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21.801 10A10 10 0 1 1 17 3.335\",\n            key: \"yps3ct\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 11 3 3L22 4\",\n            key: \"1pflzl\"\n        }\n    ]\n];\nconst CircleCheckBig = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CircleCheckBig\", __iconNode);\n //# sourceMappingURL=circle-check-big.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/clock.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Clock)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"12 6 12 12 16 14\",\n            key: \"68esgv\"\n        }\n    ]\n];\nconst Clock = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Clock\", __iconNode);\n //# sourceMappingURL=clock.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2xvY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR08sTUFBTSxVQUF1QjtJQUNsQztRQUFDLFFBQVU7UUFBQTtZQUFFLEVBQUk7WUFBTSxDQUFJLFFBQU07WUFBQSxDQUFHO1lBQU0sR0FBSztRQUFBLENBQVU7S0FBQTtJQUN6RDtRQUFDLFVBQVk7UUFBQTtZQUFFLFFBQVEsQ0FBb0I7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0NBQzVEO0FBYU0sWUFBUSxrRUFBaUIsVUFBUyxDQUFVIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXHNyY1xcaWNvbnNcXGNsb2NrLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtcbiAgWydjaXJjbGUnLCB7IGN4OiAnMTInLCBjeTogJzEyJywgcjogJzEwJywga2V5OiAnMW1nbGF5JyB9XSxcbiAgWydwb2x5bGluZScsIHsgcG9pbnRzOiAnMTIgNiAxMiAxMiAxNiAxNCcsIGtleTogJzY4ZXNndicgfV0sXG5dO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgQ2xvY2tcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOFkybHlZMnhsSUdONFBTSXhNaUlnWTNrOUlqRXlJaUJ5UFNJeE1DSWdMejRLSUNBOGNHOXNlV3hwYm1VZ2NHOXBiblJ6UFNJeE1pQTJJREV5SURFeUlERTJJREUwSWlBdlBnbzhMM04yWno0SykgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2xvY2tcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBDbG9jayA9IGNyZWF0ZUx1Y2lkZUljb24oJ0Nsb2NrJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IENsb2NrO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/loader-circle.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ LoaderCircle)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21 12a9 9 0 1 1-6.219-8.56\",\n            key: \"13zald\"\n        }\n    ]\n];\nconst LoaderCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"LoaderCircle\", __iconNode);\n //# sourceMappingURL=loader-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbG9hZGVyLWNpcmNsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHYSxpQkFBdUI7SUFBQztRQUFDLE1BQVE7UUFBQSxDQUFFO1lBQUEsRUFBRyw4QkFBK0I7WUFBQSxLQUFLLENBQVM7UUFBQSxDQUFDO0tBQUM7Q0FBQTtBQWE1RixtQkFBZSxrRUFBaUIsaUJBQWdCLENBQVUiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcc3JjXFxpY29uc1xcbG9hZGVyLWNpcmNsZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbWydwYXRoJywgeyBkOiAnTTIxIDEyYTkgOSAwIDEgMS02LjIxOS04LjU2Jywga2V5OiAnMTN6YWxkJyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBMb2FkZXJDaXJjbGVcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1qRWdNVEpoT1NBNUlEQWdNU0F4TFRZdU1qRTVMVGd1TlRZaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2xvYWRlci1jaXJjbGVcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBMb2FkZXJDaXJjbGUgPSBjcmVhdGVMdWNpZGVJY29uKCdMb2FkZXJDaXJjbGUnLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgTG9hZGVyQ2lyY2xlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/project/create-project-modal.tsx":
/*!*********************************************************!*\
  !*** ./src/components/project/create-project-modal.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateProjectModal: () => (/* binding */ CreateProjectModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _project_creation_loading__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./project-creation-loading */ \"(app-pages-browser)/./src/components/project/project-creation-loading.tsx\");\n/* __next_internal_client_entry_do_not_use__ CreateProjectModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction CreateProjectModal(param) {\n    let { open, onOpenChange, stackName, projectTitle = '' } = param;\n    var _startingCommands_stackName;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [projectName, setProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(projectTitle);\n    const [projectPath, setProjectPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\ViceProjects\\\\\".concat(stackName.toLowerCase()));\n    const [projectDescription, setProjectDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCommands, setShowCommands] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLoading, setShowLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentCommand, setCurrentCommand] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [commandProgress, setCommandProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCommandRunning, setIsCommandRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Update path when stackName changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateProjectModal.useEffect\": ()=>{\n            setProjectPath(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\ViceProjects\\\\\".concat(stackName.toLowerCase()));\n        }\n    }[\"CreateProjectModal.useEffect\"], [\n        stackName\n    ]);\n    // Validate and format project name (no spaces, no caps, use hyphens)\n    const validateProjectName = (name)=>{\n        return name.trim().toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n    };\n    // Handle project name input with real-time validation\n    const handleProjectNameChange = (e)=>{\n        const value = e.target.value;\n        setProjectName(value);\n    };\n    // Listen for command progress events from Electron\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateProjectModal.useEffect\": ()=>{\n            if ( true && window.electronAPI) {\n                const handleCommandProgress = {\n                    \"CreateProjectModal.useEffect.handleCommandProgress\": (data)=>{\n                        console.log('[COMMAND PROGRESS] 📨 Received:', data);\n                        if (data.status === 'running') {\n                            setCurrentCommand(data.command);\n                            setIsCommandRunning(true);\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"▶️ Running: \".concat(data.command)\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                        } else if (data.status === 'completed') {\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"✅ Completed: \".concat(data.command)\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            setIsCommandRunning(false);\n                        } else if (data.status === 'error') {\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"❌ Error: \".concat(data.command, \" - \").concat(data.error || 'Unknown error')\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            setIsCommandRunning(false);\n                        } else if (data.status === 'starting-ide') {\n                            setCurrentCommand(\"Starting IDE for \".concat(data.projectName || 'project', \"...\"));\n                            setIsCommandRunning(true);\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"\\uD83D\\uDE80 Starting IDE and returning to dashboard...\"\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            // Close loading screen and return to dashboard\n                            setTimeout({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": ()=>{\n                                    handleLoadingComplete();\n                                }\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"], 3000);\n                        } else if (data.status === 'ide-opened') {\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"✅ IDE opened successfully! Returning to dashboard...\"\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            setTimeout({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": ()=>{\n                                    handleLoadingComplete();\n                                }\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"], 1000);\n                        }\n                    }\n                }[\"CreateProjectModal.useEffect.handleCommandProgress\"];\n                // Listen for the IPC event\n                if (window.electronAPI.onCommandProgress) {\n                    window.electronAPI.onCommandProgress(handleCommandProgress);\n                }\n                return ({\n                    \"CreateProjectModal.useEffect\": ()=>{\n                        console.log('[PROJECT] Cleaning up command progress listener');\n                    }\n                })[\"CreateProjectModal.useEffect\"];\n            }\n        }\n    }[\"CreateProjectModal.useEffect\"], []);\n    const handleLoadingComplete = ()=>{\n        setShowLoading(false);\n        setIsCreating(false);\n        setCurrentCommand('');\n        setCommandProgress([]);\n        setIsCommandRunning(false);\n        toast({\n            title: 'Project Ready',\n            description: \"\".concat(projectName, \" has been created and opened in the IDE.\")\n        });\n    };\n    // Starting commands for different stacks\n    const startingCommands = {\n        'Mern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express mongoose cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/models/User.js server/routes/api.js'\n        ],\n        'Pern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express pg cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/db.js server/routes/api.js'\n        ],\n        'NextJS': [\n            'npx create-next-app@latest my-app',\n            'cd my-app',\n            'npm install'\n        ],\n        'Django': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install django djangorestframework',\n            'django-admin startproject myproject .',\n            'python manage.py startapp myapp'\n        ],\n        'Flask': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install flask flask-sqlalchemy flask-cors',\n            'mkdir app',\n            'touch app/__init__.py app/routes.py app/models.py'\n        ],\n        'Bash': [\n            'mkdir my-script',\n            'cd my-script',\n            'touch script.sh',\n            'chmod +x script.sh',\n            'echo \"#!/bin/bash\" > script.sh'\n        ]\n    };\n    const handleCreate = async ()=>{\n        if (!projectName.trim()) {\n            toast({\n                title: 'Project name required',\n                description: 'Please enter a name for your project.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        // Validate and format project name\n        const formattedProjectName = validateProjectName(projectName);\n        if (!formattedProjectName) {\n            toast({\n                title: 'Invalid project name',\n                description: 'Project name must contain at least one letter or number.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        if (formattedProjectName !== projectName.trim().toLowerCase().replace(/\\s+/g, '-')) {\n            toast({\n                title: 'Project name formatted',\n                description: \"Project name changed to: \".concat(formattedProjectName)\n            });\n            setProjectName(formattedProjectName);\n        }\n        setIsCreating(true);\n        setShowLoading(true);\n        try {\n            // Create project using Electron API\n            if (window.electronAPI && window.electronAPI.createProjectFiles) {\n                const result = await window.electronAPI.createProjectFiles({\n                    projectName: formattedProjectName,\n                    projectPath,\n                    description: projectDescription,\n                    stackName\n                });\n                if (result && result.success) {\n                    const finalProjectPath = result.actualPath || projectPath;\n                    // Load project in IDE\n                    if (window.electronAPI && window.electronAPI.loadProjectInIde) {\n                        console.log('[PROJECT] Loading project in IDE via Electron API');\n                        window.electronAPI.loadProjectInIde({\n                            projectName: formattedProjectName,\n                            projectPath: finalProjectPath,\n                            files: [],\n                            stackName\n                        }).catch((error)=>{\n                            console.error('[PROJECT] Error loading project in IDE:', error);\n                            // Fallback: direct navigation to IDE\n                            const ideUrl = \"http://localhost:9003?project=\".concat(encodeURIComponent(formattedProjectName), \"&path=\").concat(encodeURIComponent(finalProjectPath));\n                            window.open(ideUrl, '_blank');\n                        });\n                    } else {\n                        // Fallback: open IDE in new window\n                        const ideUrl = \"http://localhost:9003?project=\".concat(encodeURIComponent(formattedProjectName), \"&path=\").concat(encodeURIComponent(finalProjectPath));\n                        window.open(ideUrl, '_blank');\n                    }\n                    toast({\n                        title: 'Project Created Successfully',\n                        description: \"\".concat(formattedProjectName, \" has been created and is opening in the IDE.\")\n                    });\n                    // Reset form\n                    setProjectName('');\n                    setProjectDescription('');\n                } else {\n                    throw new Error((result === null || result === void 0 ? void 0 : result.message) || 'Failed to create project');\n                }\n            } else {\n                throw new Error('Electron API not available');\n            }\n        } catch (error) {\n            console.error('[PROJECT] Error creating project:', error);\n            setIsCreating(false);\n            setShowLoading(false);\n            toast({\n                title: 'Failed to create project',\n                description: error instanceof Error ? error.message : 'An error occurred while creating the project.',\n                variant: 'destructive'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                open: open,\n                onOpenChange: onOpenChange,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                    className: \"sm:max-w-[550px] md:max-w-[600px] rounded-xl overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mb-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-20 w-40\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: \"/logo.png\",\n                                    alt: \"VICE Logo\",\n                                    fill: true,\n                                    style: {\n                                        objectFit: 'contain'\n                                    },\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-[-100%]' : 'translate-x-0'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                            className: \"mb-[5] pb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                                    className: \"text-center\",\n                                                    children: \"Create New Project\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                                    className: \"text-center text-sm text-muted-foreground\",\n                                                    children: [\n                                                        \"Create a new \",\n                                                        stackName,\n                                                        \" project. Fill in the details below to get started.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid gap-4 py-4 px-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col gap-1.5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"name\",\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"name\",\n                                                            value: projectName,\n                                                            onChange: handleProjectNameChange,\n                                                            className: \"w-full\",\n                                                            placeholder: \"my-awesome-project\",\n                                                            autoFocus: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                \"No spaces or caps allowed. Use hyphens for multi-word projects.\",\n                                                                projectName && projectName !== validateProjectName(projectName) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-orange-600 ml-1\",\n                                                                    children: [\n                                                                        \"→ Will be: \",\n                                                                        validateProjectName(projectName)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col gap-1.5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"path\",\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Path\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"path\",\n                                                            value: projectPath,\n                                                            onChange: (e)=>setProjectPath(e.target.value),\n                                                            className: \"w-full\",\n                                                            placeholder: \"Documents/ViceProjects/\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col gap-1.5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"description\",\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                            id: \"description\",\n                                                            value: projectDescription,\n                                                            onChange: (e)=>setProjectDescription(e.target.value),\n                                                            className: \"w-full\",\n                                                            placeholder: \"A brief description of your project\",\n                                                            rows: 3\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mt-2 mb-4 ml-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"text-xs text-muted-foreground flex items-center gap-1 p-0 h-auto\",\n                                                        onClick: ()=>setShowCommands(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-3.5 w-3.5 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"See Starting Commands\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-3.5 w-3.5 ml-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                                    className: \"flex justify-end items-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                onClick: ()=>onOpenChange(false),\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                onClick: handleCreate,\n                                                                disabled: isCreating,\n                                                                children: isCreating ? 'Creating...' : 'Create Project'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-0' : 'translate-x-[100%]'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4 ml-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"pl-0 flex items-center gap-1\",\n                                                onClick: ()=>setShowCommands(false),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Back to Project Form\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                                    children: [\n                                                        \"Starting Commands for \",\n                                                        stackName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                                    children: [\n                                                        \"Use these commands to set up your \",\n                                                        stackName,\n                                                        \" project manually.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-4 px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted p-4 rounded-md font-mono text-xs sm:text-sm overflow-auto max-h-[250px]\",\n                                                children: ((_startingCommands_stackName = startingCommands[stackName]) === null || _startingCommands_stackName === void 0 ? void 0 : _startingCommands_stackName.map((command, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-muted-foreground mr-2 flex-shrink-0\",\n                                                                children: \"$\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"break-all\",\n                                                                children: command\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: [\n                                                        \"No commands available for \",\n                                                        stackName,\n                                                        \".\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>onOpenChange(false),\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                lineNumber: 260,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_project_creation_loading__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isVisible: showLoading,\n                stackName: stackName,\n                projectName: projectName,\n                onComplete: handleLoadingComplete,\n                currentCommand: currentCommand,\n                commandProgress: commandProgress,\n                isCommandRunning: isCommandRunning\n            }, void 0, false, {\n                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                lineNumber: 412,\n                columnNumber: 5\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CreateProjectModal, \"+Wy90OJPGbFq7Wx8RMMP1TbpegU=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = CreateProjectModal;\nvar _c;\n$RefreshReg$(_c, \"CreateProjectModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/create-project-modal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/project/project-creation-loading.tsx":
/*!*************************************************************!*\
  !*** ./src/components/project/project-creation-loading.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Loader2,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Loader2,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Loader2,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Loader2,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst ProjectCreationLoading = (param)=>{\n    let { isVisible, stackName, projectName, onComplete, currentCommand = '', commandProgress = [], isCommandRunning = false } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [steps, setSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isStartingIde, setIsStartingIde] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Generate commands based on stack\n    const generateCommands = (stack, name)=>{\n        const projectNameKebab = name.toLowerCase().replace(/\\s+/g, '-');\n        switch(stack.toLowerCase()){\n            case 'mern':\n            case 'mern stack':\n                return [\n                    {\n                        command: \"npx create-react-app \".concat(projectNameKebab),\n                        status: 'pending'\n                    },\n                    {\n                        command: \"cd \".concat(projectNameKebab),\n                        status: 'pending'\n                    },\n                    {\n                        command: 'npm install express mongoose cors dotenv',\n                        status: 'pending'\n                    },\n                    {\n                        command: 'mkdir server',\n                        status: 'pending'\n                    },\n                    {\n                        command: 'Creating server files...',\n                        status: 'pending'\n                    }\n                ];\n            case 'nextjs':\n            case 'next.js':\n                return [\n                    {\n                        command: \"npx create-next-app@latest \".concat(projectNameKebab, \" --typescript --tailwind --eslint\"),\n                        status: 'pending'\n                    },\n                    {\n                        command: \"cd \".concat(projectNameKebab),\n                        status: 'pending'\n                    },\n                    {\n                        command: 'npm install',\n                        status: 'pending'\n                    }\n                ];\n            case 'flask':\n                return [\n                    {\n                        command: \"mkdir \".concat(projectNameKebab),\n                        status: 'pending'\n                    },\n                    {\n                        command: \"cd \".concat(projectNameKebab),\n                        status: 'pending'\n                    },\n                    {\n                        command: 'python -m venv venv',\n                        status: 'pending'\n                    },\n                    {\n                        command: 'venv\\\\Scripts\\\\activate',\n                        status: 'pending'\n                    },\n                    {\n                        command: 'pip install flask flask-cors python-dotenv',\n                        status: 'pending'\n                    },\n                    {\n                        command: 'Creating Flask application files...',\n                        status: 'pending'\n                    }\n                ];\n            case 'django':\n                return [\n                    {\n                        command: \"mkdir \".concat(projectNameKebab),\n                        status: 'pending'\n                    },\n                    {\n                        command: \"cd \".concat(projectNameKebab),\n                        status: 'pending'\n                    },\n                    {\n                        command: 'python -m venv venv',\n                        status: 'pending'\n                    },\n                    {\n                        command: 'venv\\\\Scripts\\\\activate',\n                        status: 'pending'\n                    },\n                    {\n                        command: 'pip install django djangorestframework',\n                        status: 'pending'\n                    },\n                    {\n                        command: \"django-admin startproject \".concat(projectNameKebab, \" .\"),\n                        status: 'pending'\n                    },\n                    {\n                        command: 'python manage.py startapp main',\n                        status: 'pending'\n                    }\n                ];\n            default:\n                return [\n                    {\n                        command: \"mkdir \".concat(projectNameKebab),\n                        status: 'pending'\n                    },\n                    {\n                        command: \"cd \".concat(projectNameKebab),\n                        status: 'pending'\n                    },\n                    {\n                        command: 'Initializing project...',\n                        status: 'pending'\n                    }\n                ];\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectCreationLoading.useEffect\": ()=>{\n            if (isVisible) {\n                const commands = generateCommands(stackName, projectName);\n                setSteps(commands);\n                setCurrentStep(0);\n                setIsStartingIde(false);\n                // Listen for real command progress if electron API is available\n                if ( true && window.electronAPI) {\n                    const handleCommandProgress = {\n                        \"ProjectCreationLoading.useEffect.handleCommandProgress\": (progressData)=>{\n                            if (progressData.status === 'starting-ide') {\n                                setIsStartingIde(true);\n                                setTimeout({\n                                    \"ProjectCreationLoading.useEffect.handleCommandProgress\": ()=>{\n                                        onComplete();\n                                    }\n                                }[\"ProjectCreationLoading.useEffect.handleCommandProgress\"], 3000);\n                            } else if (progressData.status === 'error') {\n                                console.error('Command execution error:', progressData.error);\n                                onComplete();\n                            } else {\n                                setCurrentStep(progressData.currentStep);\n                                setSteps({\n                                    \"ProjectCreationLoading.useEffect.handleCommandProgress\": (prev)=>prev.map({\n                                            \"ProjectCreationLoading.useEffect.handleCommandProgress\": (step, index)=>{\n                                                if (index === progressData.currentStep) {\n                                                    return {\n                                                        ...step,\n                                                        status: progressData.status,\n                                                        command: progressData.command\n                                                    };\n                                                } else if (index < progressData.currentStep) {\n                                                    return {\n                                                        ...step,\n                                                        status: 'completed'\n                                                    };\n                                                }\n                                                return step;\n                                            }\n                                        }[\"ProjectCreationLoading.useEffect.handleCommandProgress\"])\n                                }[\"ProjectCreationLoading.useEffect.handleCommandProgress\"]);\n                            }\n                        }\n                    }[\"ProjectCreationLoading.useEffect.handleCommandProgress\"];\n                    window.electronAPI.onCommandProgress(handleCommandProgress);\n                    // Cleanup function\n                    return ({\n                        \"ProjectCreationLoading.useEffect\": ()=>{\n                            if (window.electronAPI) {\n                                window.electronAPI.removeAllListeners('command-progress');\n                            }\n                        }\n                    })[\"ProjectCreationLoading.useEffect\"];\n                } else {\n                    // Fallback simulation for web version\n                    simulateCommandExecution(commands);\n                }\n            }\n        }\n    }[\"ProjectCreationLoading.useEffect\"], [\n        isVisible,\n        stackName,\n        projectName,\n        onComplete\n    ]);\n    const simulateCommandExecution = async (commands)=>{\n        for(let i = 0; i < commands.length; i++){\n            setCurrentStep(i);\n            // Update step to running\n            setSteps((prev)=>prev.map((step, index)=>index === i ? {\n                        ...step,\n                        status: 'running'\n                    } : step));\n            // Simulate command execution time\n            const executionTime = commands[i].command.includes('npx create') ? 8000 : commands[i].command.includes('npm install') ? 5000 : commands[i].command.includes('pip install') ? 4000 : 2000;\n            await new Promise((resolve)=>setTimeout(resolve, executionTime));\n            // Update step to completed\n            setSteps((prev)=>prev.map((step, index)=>index === i ? {\n                        ...step,\n                        status: 'completed',\n                        output: 'Success'\n                    } : step));\n        }\n        // Show \"Starting IDE\" phase\n        setIsStartingIde(true);\n        await new Promise((resolve)=>setTimeout(resolve, 3000));\n        // Complete the process\n        onComplete();\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center\",\n        style: {\n            backgroundColor: 'hsl(var(--background))'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"w-full max-w-2xl mx-4 shadow-2xl border-2\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-8 w-8 animate-spin text-primary mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            currentCommand.includes('Starting IDE') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: currentCommand.includes('Starting IDE') ? 'Opening IDE...' : 'Creating \"'.concat(projectName, '\"')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: currentCommand.includes('Starting IDE') ? 'Your project is ready! Opening in the IDE and returning to dashboard...' : \"Setting up your \".concat(stackName, \" project with all dependencies\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Project: \",\n                                    projectName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, undefined),\n                    !isStartingIde && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            currentCommand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center p-4 rounded-lg border transition-all duration-500 transform\", isCommandRunning ? \"bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800 scale-105 shadow-lg\" : \"bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800 scale-100\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mr-3\",\n                                        children: isCommandRunning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5 animate-spin text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 h-5 w-5 border-2 border-blue-200 rounded-full animate-ping\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 23\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 h-5 w-5 bg-green-500 rounded-full animate-ping opacity-20\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"text-sm font-mono font-medium\",\n                                                        children: [\n                                                            isCommandRunning ? '🔄 Running: ' : '✅ Completed: ',\n                                                            currentCommand\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            isCommandRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-500 h-1.5 rounded-full animate-pulse\",\n                                                    style: {\n                                                        width: '60%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 17\n                            }, undefined),\n                            commandProgress.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/90 rounded-lg border border-gray-700 p-4 max-h-64 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-sm mb-3 flex items-center text-green-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            \"Terminal Output:\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-auto flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1 text-xs font-mono\",\n                                        children: [\n                                            commandProgress.map((line, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-gray-300 transition-all duration-300 transform\", line.includes('▶️') && \"text-blue-400 font-medium animate-pulse\", line.includes('✅') && \"text-green-400 font-medium\", line.includes('❌') && \"text-red-400 font-medium\", line.includes('🚀') && \"text-purple-400 font-medium animate-bounce\"),\n                                                    style: {\n                                                        animationDelay: \"\".concat(index * 100, \"ms\"),\n                                                        opacity: index === commandProgress.length - 1 ? 1 : 0.8\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400\",\n                                                            children: \"$\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        \" \",\n                                                        line\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 23\n                                                }, undefined)),\n                                            isCommandRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-green-400 animate-pulse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: \"$\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-4 bg-green-400 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 17\n                            }, undefined),\n                            commandProgress.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center p-3 rounded-lg border transition-all duration-300\", step.status === 'running' && \"bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800\", step.status === 'completed' && \"bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800\", step.status === 'pending' && \"bg-muted/50 border-muted\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mr-3\",\n                                                children: [\n                                                    step.status === 'running' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-4 w-4 animate-spin text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    step.status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    step.status === 'pending' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2 text-muted-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                className: \"text-sm font-mono\",\n                                                                children: step.command\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    step.output && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-600 mt-1 ml-6\",\n                                                        children: step.output\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 21\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 13\n                    }, undefined),\n                    isStartingIde && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-pulse bg-primary/20 rounded-full p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Loader2_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-8 w-8 text-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"Opening ViceIDE...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Your project will be loaded automatically\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n            lineNumber: 177,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\project-creation-loading.tsx\",\n        lineNumber: 175,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProjectCreationLoading, \"9hSrfFVeldf5kEJMEWx4TVqkKGo=\");\n_c = ProjectCreationLoading;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProjectCreationLoading);\nvar _c;\n$RefreshReg$(_c, \"ProjectCreationLoading\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/project-creation-loading.tsx\n"));

/***/ })

});