{"ui": {"displayRawMarkdown": false}, "models": [{"title": "TEST LLM", "provider": "test", "model": "this field is not used"}, {"title": "<PERSON><PERSON>", "provider": "mock", "model": "this field is not used"}], "analytics": {"provider": "continue-proxy"}, "tabAutocompleteModel": {"title": "TEST LLM", "provider": "test", "model": "this field is not used"}, "tabAutocompleteOptions": {"useCache": false}, "contextProviders": [{"name": "docs"}, {"name": "diff"}, {"name": "url"}, {"name": "folder"}], "docs": []}