"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dotprompt";
exports.ids = ["vendor-chunks/dotprompt"];
exports.modules = {

/***/ "(action-browser)/./node_modules/dotprompt/dist/index.js":
/*!**********************************************!*\
  !*** ./node_modules/dotprompt/dist/index.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  Dotprompt: () => Dotprompt,\n  PicoschemaParser: () => PicoschemaParser,\n  picoschema: () => picoschema\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/dotprompt.ts\nvar Handlebars = __toESM(__webpack_require__(/*! handlebars */ \"handlebars\"));\n\n// src/helpers.ts\nvar helpers_exports = {};\n__export(helpers_exports, {\n  history: () => history,\n  ifEquals: () => ifEquals,\n  json: () => json,\n  media: () => media,\n  role: () => role,\n  section: () => section,\n  unlessEquals: () => unlessEquals\n});\nvar import_handlebars = __webpack_require__(/*! handlebars */ \"handlebars\");\nfunction json(serializable, options) {\n  return new import_handlebars.SafeString(JSON.stringify(serializable, null, options.hash.indent || 0));\n}\nfunction role(role2) {\n  return new import_handlebars.SafeString(`<<<dotprompt:role:${role2}>>>`);\n}\nfunction history() {\n  return new import_handlebars.SafeString(\"<<<dotprompt:history>>>\");\n}\nfunction section(name) {\n  return new import_handlebars.SafeString(`<<<dotprompt:section ${name}>>>`);\n}\nfunction media(options) {\n  return new import_handlebars.SafeString(\n    `<<<dotprompt:media:url ${options.hash.url}${options.hash.contentType ? ` ${options.hash.contentType}` : \"\"}>>>`\n  );\n}\nfunction ifEquals(arg1, arg2, options) {\n  return arg1 == arg2 ? options.fn(this) : options.inverse(this);\n}\nfunction unlessEquals(arg1, arg2, options) {\n  return arg1 != arg2 ? options.fn(this) : options.inverse(this);\n}\n\n// src/parse.ts\nvar import_yaml = __webpack_require__(/*! yaml */ \"(action-browser)/./node_modules/yaml/dist/index.js\");\nvar FRONTMATTER_REGEX = /^---\\s*\\n([\\s\\S]*?)\\n---\\s*\\n([\\s\\S]*)$/;\nvar RESERVED_METADATA_KEYWORDS = [\n  \"name\",\n  \"variant\",\n  \"version\",\n  \"description\",\n  \"model\",\n  \"tools\",\n  \"toolDefs\",\n  \"config\",\n  \"input\",\n  \"output\",\n  \"raw\",\n  \"ext\"\n];\nvar BASE_METADATA = {\n  ext: {},\n  metadata: {},\n  config: {}\n};\nfunction parseDocument(source) {\n  const match = source.match(FRONTMATTER_REGEX);\n  if (match) {\n    const [, frontmatter, content] = match;\n    try {\n      const parsedMetadata = (0, import_yaml.parse)(frontmatter);\n      const raw = { ...parsedMetadata };\n      const pruned = { ...BASE_METADATA };\n      const ext = {};\n      for (const k in raw) {\n        const key = k;\n        if (RESERVED_METADATA_KEYWORDS.includes(key)) {\n          pruned[key] = raw[key];\n        } else if (key.includes(\".\")) {\n          const lastDotIndex = key.lastIndexOf(\".\");\n          const namespace = key.substring(0, lastDotIndex);\n          const fieldName = key.substring(lastDotIndex + 1);\n          ext[namespace] = ext[namespace] || {};\n          ext[namespace][fieldName] = raw[key];\n        }\n      }\n      return { ...pruned, raw, ext, template: content.trim() };\n    } catch (error) {\n      console.error(\"Dotprompt: Error parsing YAML frontmatter:\", error);\n      return { ...BASE_METADATA, template: source.trim() };\n    }\n  }\n  return { ...BASE_METADATA, template: source };\n}\nvar ROLE_REGEX = /(<<<dotprompt:(?:role:[a-z]+|history))>>>/g;\nfunction toMessages(renderedString, data) {\n  let currentMessage = {\n    role: \"user\",\n    source: \"\"\n  };\n  const messageSources = [currentMessage];\n  for (const piece of renderedString.split(ROLE_REGEX).filter((s) => s.trim() !== \"\")) {\n    if (piece.startsWith(\"<<<dotprompt:role:\")) {\n      const role2 = piece.substring(18);\n      if (currentMessage.source.trim()) {\n        currentMessage = { role: role2, source: \"\" };\n        messageSources.push(currentMessage);\n      } else {\n        currentMessage.role = role2;\n      }\n    } else if (piece.startsWith(\"<<<dotprompt:history\")) {\n      messageSources.push(\n        ...data?.messages?.map((m) => {\n          return {\n            ...m,\n            metadata: { ...m.metadata || {}, purpose: \"history\" }\n          };\n        }) || []\n      );\n      currentMessage = { role: \"model\", source: \"\" };\n      messageSources.push(currentMessage);\n    } else {\n      currentMessage.source += piece;\n    }\n  }\n  const messages = messageSources.filter((ms) => ms.content || ms.source).map((m) => {\n    const out = {\n      role: m.role,\n      content: m.content || toParts(m.source)\n    };\n    if (m.metadata) out.metadata = m.metadata;\n    return out;\n  });\n  return insertHistory(messages, data?.messages);\n}\nfunction insertHistory(messages, history2 = []) {\n  if (!history2 || messages.find((m) => m.metadata?.purpose === \"history\")) return messages;\n  if (messages.at(-1)?.role === \"user\") {\n    return [...messages.slice(0, -1), ...history2, messages.at(-1)];\n  }\n  return [...messages, ...history2];\n}\nvar PART_REGEX = /(<<<dotprompt:(?:media:url|section).*?)>>>/g;\nfunction toParts(source) {\n  const parts = [];\n  const pieces = source.split(PART_REGEX).filter((s) => s.trim() !== \"\");\n  for (let i = 0; i < pieces.length; i++) {\n    const piece = pieces[i];\n    if (piece.startsWith(\"<<<dotprompt:media:\")) {\n      const [_, url, contentType] = piece.split(\" \");\n      const part = { media: { url } };\n      if (contentType) part.media.contentType = contentType;\n      parts.push(part);\n    } else if (piece.startsWith(\"<<<dotprompt:section\")) {\n      const [_, sectionType] = piece.split(\" \");\n      parts.push({ metadata: { purpose: sectionType, pending: true } });\n    } else {\n      parts.push({ text: piece });\n    }\n  }\n  const apart = { text: \"foo\" };\n  return parts;\n}\n\n// src/picoschema.ts\nvar JSON_SCHEMA_SCALAR_TYPES = [\"string\", \"boolean\", \"null\", \"number\", \"integer\", \"any\"];\nvar WILDCARD_PROPERTY_NAME = \"(*)\";\nasync function picoschema(schema, options) {\n  return new PicoschemaParser(options).parse(schema);\n}\nvar PicoschemaParser = class {\n  schemaResolver;\n  constructor(options) {\n    this.schemaResolver = options?.schemaResolver;\n  }\n  async mustResolveSchema(schemaName) {\n    if (!this.schemaResolver) {\n      throw new Error(`Picoschema: unsupported scalar type '${schemaName}'.`);\n    }\n    const val = await this.schemaResolver(schemaName);\n    if (!val) {\n      throw new Error(`Picoschema: could not find schema with name '${schemaName}'`);\n    }\n    return val;\n  }\n  async parse(schema) {\n    if (!schema) return null;\n    if (typeof schema === \"string\") {\n      const [type, description] = extractDescription(schema);\n      if (JSON_SCHEMA_SCALAR_TYPES.includes(type)) {\n        let out = { type };\n        if (description) out = { ...out, description };\n        return out;\n      }\n      const resolvedSchema = await this.mustResolveSchema(type);\n      return description ? { ...resolvedSchema, description } : resolvedSchema;\n    }\n    if ([...JSON_SCHEMA_SCALAR_TYPES, \"object\", \"array\"].includes(schema?.type)) {\n      return schema;\n    }\n    if (typeof schema?.properties === \"object\") {\n      return { ...schema, type: \"object\" };\n    }\n    return this.parsePico(schema);\n  }\n  async parsePico(obj, path = []) {\n    if (typeof obj === \"string\") {\n      const [type, description] = extractDescription(obj);\n      if (!JSON_SCHEMA_SCALAR_TYPES.includes(type)) {\n        let resolvedSchema = await this.mustResolveSchema(type);\n        if (description) resolvedSchema = { ...resolvedSchema, description };\n        return resolvedSchema;\n      }\n      if (type === \"any\") {\n        return description ? { description } : {};\n      }\n      return description ? { type, description } : { type };\n    } else if (typeof obj !== \"object\") {\n      throw new Error(\n        \"Picoschema: only consists of objects and strings. Got: \" + JSON.stringify(obj)\n      );\n    }\n    const schema = {\n      type: \"object\",\n      properties: {},\n      required: [],\n      additionalProperties: false\n    };\n    for (const key in obj) {\n      if (key === WILDCARD_PROPERTY_NAME) {\n        schema.additionalProperties = await this.parsePico(obj[key], [...path, key]);\n        continue;\n      }\n      const [name, typeInfo] = key.split(\"(\");\n      const isOptional = name.endsWith(\"?\");\n      const propertyName = isOptional ? name.slice(0, -1) : name;\n      if (!isOptional) {\n        schema.required.push(propertyName);\n      }\n      if (!typeInfo) {\n        const prop = { ...await this.parsePico(obj[key], [...path, key]) };\n        if (isOptional && typeof prop.type === \"string\") {\n          prop.type = [prop.type, \"null\"];\n        }\n        schema.properties[propertyName] = prop;\n        continue;\n      }\n      const [type, description] = extractDescription(typeInfo.substring(0, typeInfo.length - 1));\n      if (type === \"array\") {\n        schema.properties[propertyName] = {\n          type: isOptional ? [\"array\", \"null\"] : \"array\",\n          items: await this.parsePico(obj[key], [...path, key])\n        };\n      } else if (type === \"object\") {\n        const prop = await this.parsePico(obj[key], [...path, key]);\n        if (isOptional) prop.type = [prop.type, \"null\"];\n        schema.properties[propertyName] = prop;\n      } else if (type === \"enum\") {\n        const prop = { enum: obj[key] };\n        if (isOptional && !prop.enum.includes(null)) prop.enum.push(null);\n        schema.properties[propertyName] = prop;\n      } else {\n        throw new Error(\n          \"Picoschema: parenthetical types must be 'object' or 'array', got: \" + type\n        );\n      }\n      if (description) {\n        schema.properties[propertyName].description = description;\n      }\n    }\n    if (!schema.required.length) delete schema.required;\n    return schema;\n  }\n};\nfunction extractDescription(input) {\n  if (!input.includes(\",\")) return [input, null];\n  const match = input.match(/(.*?), *(.*)$/);\n  return [match[1], match[2]];\n}\n\n// src/util.ts\nfunction removeUndefinedFields(obj) {\n  if (obj === null || typeof obj !== \"object\") {\n    return obj;\n  }\n  if (Array.isArray(obj)) {\n    return obj.map((item) => removeUndefinedFields(item));\n  }\n  const result = {};\n  for (const [key, value] of Object.entries(obj)) {\n    if (value !== void 0) {\n      result[key] = removeUndefinedFields(value);\n    }\n  }\n  return result;\n}\n\n// src/dotprompt.ts\nvar Dotprompt = class {\n  handlebars;\n  knownHelpers = {};\n  defaultModel;\n  modelConfigs = {};\n  tools = {};\n  toolResolver;\n  schemas = {};\n  schemaResolver;\n  partialResolver;\n  store;\n  constructor(options) {\n    this.handlebars = Handlebars.noConflict();\n    this.modelConfigs = options?.modelConfigs || this.modelConfigs;\n    this.defaultModel = options?.defaultModel;\n    this.tools = options?.tools || {};\n    this.toolResolver = options?.toolResolver;\n    this.schemas = options?.schemas || {};\n    this.schemaResolver = options?.schemaResolver;\n    this.partialResolver = options?.partialResolver;\n    for (const key in helpers_exports) {\n      this.defineHelper(key, helpers_exports[key]);\n      this.handlebars.registerHelper(key, helpers_exports[key]);\n    }\n    if (options?.helpers) {\n      for (const key in options.helpers) {\n        this.defineHelper(key, options.helpers[key]);\n      }\n    }\n    if (options?.partials) {\n      for (const key in options.partials) {\n        this.definePartial(key, options.partials[key]);\n      }\n    }\n  }\n  defineHelper(name, fn) {\n    this.handlebars.registerHelper(name, fn);\n    this.knownHelpers[name] = true;\n    return this;\n  }\n  definePartial(name, source) {\n    this.handlebars.registerPartial(name, source);\n    return this;\n  }\n  defineTool(def) {\n    this.tools[def.name] = def;\n    return this;\n  }\n  parse(source) {\n    return parseDocument(source);\n  }\n  async render(source, data = {}, options) {\n    const renderer = await this.compile(source);\n    return renderer(data, options);\n  }\n  async renderPicoschema(meta) {\n    if (!meta.output?.schema && !meta.input?.schema) return meta;\n    const newMeta = { ...meta };\n    if (meta.input?.schema) {\n      newMeta.input = {\n        ...meta.input,\n        schema: await picoschema(meta.input.schema, {\n          schemaResolver: this.wrappedSchemaResolver.bind(this)\n        })\n      };\n    }\n    if (meta.output?.schema) {\n      newMeta.output = {\n        ...meta.output,\n        schema: await picoschema(meta.output.schema, {\n          schemaResolver: this.wrappedSchemaResolver.bind(this)\n        })\n      };\n    }\n    return newMeta;\n  }\n  async wrappedSchemaResolver(name) {\n    if (this.schemas[name]) return this.schemas[name];\n    if (this.schemaResolver) return await this.schemaResolver(name);\n    return null;\n  }\n  async resolveMetadata(base, ...merges) {\n    let out = { ...base };\n    for (let i = 0; i < merges.length; i++) {\n      if (!merges[i]) continue;\n      const config = out.config || {};\n      out = { ...out, ...merges[i] };\n      out.config = { ...config, ...merges[i]?.config || {} };\n    }\n    delete out.template;\n    out = removeUndefinedFields(out);\n    out = await this.resolveTools(out);\n    out = await this.renderPicoschema(out);\n    return out;\n  }\n  async resolveTools(base) {\n    const out = { ...base };\n    if (out.tools) {\n      const outTools = [];\n      out.toolDefs = out.toolDefs || [];\n      await Promise.all(\n        out.tools.map(async (toolName) => {\n          if (this.tools[toolName]) {\n            out.toolDefs.push(this.tools[toolName]);\n          } else if (this.toolResolver) {\n            const resolvedTool = await this.toolResolver(toolName);\n            if (!resolvedTool) {\n              throw new Error(\n                `Dotprompt: Unable to resolve tool '${toolName}' to a recognized tool definition.`\n              );\n            }\n            out.toolDefs.push(resolvedTool);\n          } else {\n            outTools.push(toolName);\n          }\n        })\n      );\n      out.tools = outTools;\n    }\n    return out;\n  }\n  identifyPartials(template) {\n    const ast = this.handlebars.parse(template);\n    const partials = /* @__PURE__ */ new Set();\n    class PartialVisitor extends this.handlebars.Visitor {\n      constructor(partials2) {\n        super();\n        this.partials = partials2;\n      }\n      PartialStatement(partial) {\n        if (\"original\" in partial.name) {\n          this.partials.add(partial.name.original);\n        }\n      }\n    }\n    new PartialVisitor(partials).accept(ast);\n    return partials;\n  }\n  async resolvePartials(template) {\n    if (!this.partialResolver && !this.store) return;\n    const partials = this.identifyPartials(template);\n    await Promise.all(\n      Array.from(partials).map(async (name) => {\n        if (!this.handlebars.partials[name]) {\n          const content = await this.partialResolver(name) || (await this.store?.loadPartial(name))?.source;\n          if (content) {\n            this.definePartial(name, content);\n            await this.resolvePartials(content);\n          }\n        }\n      })\n    );\n  }\n  async compile(source, additionalMetadata) {\n    if (typeof source === \"string\") source = this.parse(source);\n    if (additionalMetadata) source = { ...source, ...additionalMetadata };\n    await this.resolvePartials(source.template);\n    const renderString = this.handlebars.compile(source.template, {\n      knownHelpers: this.knownHelpers,\n      knownHelpersOnly: true\n    });\n    const renderFunc = async (data, options) => {\n      const { input, ...mergedMetadata } = await this.renderMetadata(source);\n      const renderedString = renderString(\n        { ...options?.input?.default || {}, ...data.input },\n        {\n          data: {\n            metadata: { prompt: mergedMetadata, docs: data.docs, messages: data.messages },\n            ...data.context || {}\n          }\n        }\n      );\n      return {\n        ...mergedMetadata,\n        messages: toMessages(renderedString, data)\n      };\n    };\n    renderFunc.prompt = source;\n    return renderFunc;\n  }\n  async renderMetadata(source, additionalMetadata) {\n    if (typeof source === \"string\") source = this.parse(source);\n    const selectedModel = additionalMetadata?.model || source.model || this.defaultModel;\n    const modelConfig = this.modelConfigs[selectedModel];\n    return this.resolveMetadata(\n      modelConfig ? { config: modelConfig } : {},\n      source,\n      additionalMetadata\n    );\n  }\n};\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/dotprompt/dist/index.js\n");

/***/ })

};
;