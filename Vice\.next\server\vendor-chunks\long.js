/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/long";
exports.ids = ["vendor-chunks/long"];
exports.modules = {

/***/ "(action-browser)/./node_modules/long/umd/index.js":
/*!****************************************!*\
  !*** ./node_modules/long/umd/index.js ***!
  \****************************************/
/***/ ((module, exports) => {

eval("var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;// GENERATED FILE. DO NOT EDIT.\nvar Long = (function(exports) {\n  \"use strict\";\n  \n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  /**\n   * @license\n   * Copyright 2009 The Closure Library Authors\n   * Copyright 2020 Daniel Wirtz / The long.js Authors.\n   *\n   * Licensed under the Apache License, Version 2.0 (the \"License\");\n   * you may not use this file except in compliance with the License.\n   * You may obtain a copy of the License at\n   *\n   *     http://www.apache.org/licenses/LICENSE-2.0\n   *\n   * Unless required by applicable law or agreed to in writing, software\n   * distributed under the License is distributed on an \"AS IS\" BASIS,\n   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   * See the License for the specific language governing permissions and\n   * limitations under the License.\n   *\n   * SPDX-License-Identifier: Apache-2.0\n   */\n  \n  // WebAssembly optimizations to do native i64 multiplication and divide\n  var wasm = null;\n  try {\n    wasm = new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([\n    // \\0asm\n    0, 97, 115, 109,\n    // version 1\n    1, 0, 0, 0,\n    // section \"type\"\n    1, 13, 2,\n    // 0, () => i32\n    96, 0, 1, 127,\n    // 1, (i32, i32, i32, i32) => i32\n    96, 4, 127, 127, 127, 127, 1, 127,\n    // section \"function\"\n    3, 7, 6,\n    // 0, type 0\n    0,\n    // 1, type 1\n    1,\n    // 2, type 1\n    1,\n    // 3, type 1\n    1,\n    // 4, type 1\n    1,\n    // 5, type 1\n    1,\n    // section \"global\"\n    6, 6, 1,\n    // 0, \"high\", mutable i32\n    127, 1, 65, 0, 11,\n    // section \"export\"\n    7, 50, 6,\n    // 0, \"mul\"\n    3, 109, 117, 108, 0, 1,\n    // 1, \"div_s\"\n    5, 100, 105, 118, 95, 115, 0, 2,\n    // 2, \"div_u\"\n    5, 100, 105, 118, 95, 117, 0, 3,\n    // 3, \"rem_s\"\n    5, 114, 101, 109, 95, 115, 0, 4,\n    // 4, \"rem_u\"\n    5, 114, 101, 109, 95, 117, 0, 5,\n    // 5, \"get_high\"\n    8, 103, 101, 116, 95, 104, 105, 103, 104, 0, 0,\n    // section \"code\"\n    10, 191, 1, 6,\n    // 0, \"get_high\"\n    4, 0, 35, 0, 11,\n    // 1, \"mul\"\n    36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 126, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11,\n    // 2, \"div_s\"\n    36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 127, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11,\n    // 3, \"div_u\"\n    36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 128, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11,\n    // 4, \"rem_s\"\n    36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 129, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11,\n    // 5, \"rem_u\"\n    36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 130, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11])), {}).exports;\n  } catch {\n    // no wasm support :(\n  }\n  \n  /**\n   * Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers.\n   *  See the from* functions below for more convenient ways of constructing Longs.\n   * @exports Long\n   * @class A Long class for representing a 64 bit two's-complement integer value.\n   * @param {number} low The low (signed) 32 bits of the long\n   * @param {number} high The high (signed) 32 bits of the long\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @constructor\n   */\n  function Long(low, high, unsigned) {\n    /**\n     * The low 32 bits as a signed value.\n     * @type {number}\n     */\n    this.low = low | 0;\n  \n    /**\n     * The high 32 bits as a signed value.\n     * @type {number}\n     */\n    this.high = high | 0;\n  \n    /**\n     * Whether unsigned or not.\n     * @type {boolean}\n     */\n    this.unsigned = !!unsigned;\n  }\n  \n  // The internal representation of a long is the two given signed, 32-bit values.\n  // We use 32-bit pieces because these are the size of integers on which\n  // Javascript performs bit-operations.  For operations like addition and\n  // multiplication, we split each number into 16 bit pieces, which can easily be\n  // multiplied within Javascript's floating-point representation without overflow\n  // or change in sign.\n  //\n  // In the algorithms below, we frequently reduce the negative case to the\n  // positive case by negating the input(s) and then post-processing the result.\n  // Note that we must ALWAYS check specially whether those values are MIN_VALUE\n  // (-2^63) because -MIN_VALUE == MIN_VALUE (since 2^63 cannot be represented as\n  // a positive number, it overflows back into a negative).  Not handling this\n  // case would often result in infinite recursion.\n  //\n  // Common constant values ZERO, ONE, NEG_ONE, etc. are defined below the from*\n  // methods on which they depend.\n  \n  /**\n   * An indicator used to reliably determine if an object is a Long or not.\n   * @type {boolean}\n   * @const\n   * @private\n   */\n  Long.prototype.__isLong__;\n  Object.defineProperty(Long.prototype, \"__isLong__\", {\n    value: true\n  });\n  \n  /**\n   * @function\n   * @param {*} obj Object\n   * @returns {boolean}\n   * @inner\n   */\n  function isLong(obj) {\n    return (obj && obj[\"__isLong__\"]) === true;\n  }\n  \n  /**\n   * @function\n   * @param {*} value number\n   * @returns {number}\n   * @inner\n   */\n  function ctz32(value) {\n    var c = Math.clz32(value & -value);\n    return value ? 31 - c : c;\n  }\n  \n  /**\n   * Tests if the specified object is a Long.\n   * @function\n   * @param {*} obj Object\n   * @returns {boolean}\n   */\n  Long.isLong = isLong;\n  \n  /**\n   * A cache of the Long representations of small integer values.\n   * @type {!Object}\n   * @inner\n   */\n  var INT_CACHE = {};\n  \n  /**\n   * A cache of the Long representations of small unsigned integer values.\n   * @type {!Object}\n   * @inner\n   */\n  var UINT_CACHE = {};\n  \n  /**\n   * @param {number} value\n   * @param {boolean=} unsigned\n   * @returns {!Long}\n   * @inner\n   */\n  function fromInt(value, unsigned) {\n    var obj, cachedObj, cache;\n    if (unsigned) {\n      value >>>= 0;\n      if (cache = 0 <= value && value < 256) {\n        cachedObj = UINT_CACHE[value];\n        if (cachedObj) return cachedObj;\n      }\n      obj = fromBits(value, 0, true);\n      if (cache) UINT_CACHE[value] = obj;\n      return obj;\n    } else {\n      value |= 0;\n      if (cache = -128 <= value && value < 128) {\n        cachedObj = INT_CACHE[value];\n        if (cachedObj) return cachedObj;\n      }\n      obj = fromBits(value, value < 0 ? -1 : 0, false);\n      if (cache) INT_CACHE[value] = obj;\n      return obj;\n    }\n  }\n  \n  /**\n   * Returns a Long representing the given 32 bit integer value.\n   * @function\n   * @param {number} value The 32 bit integer in question\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @returns {!Long} The corresponding Long value\n   */\n  Long.fromInt = fromInt;\n  \n  /**\n   * @param {number} value\n   * @param {boolean=} unsigned\n   * @returns {!Long}\n   * @inner\n   */\n  function fromNumber(value, unsigned) {\n    if (isNaN(value)) return unsigned ? UZERO : ZERO;\n    if (unsigned) {\n      if (value < 0) return UZERO;\n      if (value >= TWO_PWR_64_DBL) return MAX_UNSIGNED_VALUE;\n    } else {\n      if (value <= -TWO_PWR_63_DBL) return MIN_VALUE;\n      if (value + 1 >= TWO_PWR_63_DBL) return MAX_VALUE;\n    }\n    if (value < 0) return fromNumber(-value, unsigned).neg();\n    return fromBits(value % TWO_PWR_32_DBL | 0, value / TWO_PWR_32_DBL | 0, unsigned);\n  }\n  \n  /**\n   * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\n   * @function\n   * @param {number} value The number in question\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @returns {!Long} The corresponding Long value\n   */\n  Long.fromNumber = fromNumber;\n  \n  /**\n   * @param {number} lowBits\n   * @param {number} highBits\n   * @param {boolean=} unsigned\n   * @returns {!Long}\n   * @inner\n   */\n  function fromBits(lowBits, highBits, unsigned) {\n    return new Long(lowBits, highBits, unsigned);\n  }\n  \n  /**\n   * Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits. Each is\n   *  assumed to use 32 bits.\n   * @function\n   * @param {number} lowBits The low 32 bits\n   * @param {number} highBits The high 32 bits\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @returns {!Long} The corresponding Long value\n   */\n  Long.fromBits = fromBits;\n  \n  /**\n   * @function\n   * @param {number} base\n   * @param {number} exponent\n   * @returns {number}\n   * @inner\n   */\n  var pow_dbl = Math.pow; // Used 4 times (4*8 to 15+4)\n  \n  /**\n   * @param {string} str\n   * @param {(boolean|number)=} unsigned\n   * @param {number=} radix\n   * @returns {!Long}\n   * @inner\n   */\n  function fromString(str, unsigned, radix) {\n    if (str.length === 0) throw Error('empty string');\n    if (typeof unsigned === 'number') {\n      // For goog.math.long compatibility\n      radix = unsigned;\n      unsigned = false;\n    } else {\n      unsigned = !!unsigned;\n    }\n    if (str === \"NaN\" || str === \"Infinity\" || str === \"+Infinity\" || str === \"-Infinity\") return unsigned ? UZERO : ZERO;\n    radix = radix || 10;\n    if (radix < 2 || 36 < radix) throw RangeError('radix');\n    var p;\n    if ((p = str.indexOf('-')) > 0) throw Error('interior hyphen');else if (p === 0) {\n      return fromString(str.substring(1), unsigned, radix).neg();\n    }\n  \n    // Do several (8) digits each time through the loop, so as to\n    // minimize the calls to the very expensive emulated div.\n    var radixToPower = fromNumber(pow_dbl(radix, 8));\n    var result = ZERO;\n    for (var i = 0; i < str.length; i += 8) {\n      var size = Math.min(8, str.length - i),\n        value = parseInt(str.substring(i, i + size), radix);\n      if (size < 8) {\n        var power = fromNumber(pow_dbl(radix, size));\n        result = result.mul(power).add(fromNumber(value));\n      } else {\n        result = result.mul(radixToPower);\n        result = result.add(fromNumber(value));\n      }\n    }\n    result.unsigned = unsigned;\n    return result;\n  }\n  \n  /**\n   * Returns a Long representation of the given string, written using the specified radix.\n   * @function\n   * @param {string} str The textual representation of the Long\n   * @param {(boolean|number)=} unsigned Whether unsigned or not, defaults to signed\n   * @param {number=} radix The radix in which the text is written (2-36), defaults to 10\n   * @returns {!Long} The corresponding Long value\n   */\n  Long.fromString = fromString;\n  \n  /**\n   * @function\n   * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val\n   * @param {boolean=} unsigned\n   * @returns {!Long}\n   * @inner\n   */\n  function fromValue(val, unsigned) {\n    if (typeof val === 'number') return fromNumber(val, unsigned);\n    if (typeof val === 'string') return fromString(val, unsigned);\n    // Throws for non-objects, converts non-instanceof Long:\n    return fromBits(val.low, val.high, typeof unsigned === 'boolean' ? unsigned : val.unsigned);\n  }\n  \n  /**\n   * Converts the specified value to a Long using the appropriate from* function for its type.\n   * @function\n   * @param {!Long|number|bigint|string|!{low: number, high: number, unsigned: boolean}} val Value\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @returns {!Long}\n   */\n  Long.fromValue = fromValue;\n  \n  // NOTE: the compiler should inline these constant values below and then remove these variables, so there should be\n  // no runtime penalty for these.\n  \n  /**\n   * @type {number}\n   * @const\n   * @inner\n   */\n  var TWO_PWR_16_DBL = 1 << 16;\n  \n  /**\n   * @type {number}\n   * @const\n   * @inner\n   */\n  var TWO_PWR_24_DBL = 1 << 24;\n  \n  /**\n   * @type {number}\n   * @const\n   * @inner\n   */\n  var TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;\n  \n  /**\n   * @type {number}\n   * @const\n   * @inner\n   */\n  var TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;\n  \n  /**\n   * @type {number}\n   * @const\n   * @inner\n   */\n  var TWO_PWR_63_DBL = TWO_PWR_64_DBL / 2;\n  \n  /**\n   * @type {!Long}\n   * @const\n   * @inner\n   */\n  var TWO_PWR_24 = fromInt(TWO_PWR_24_DBL);\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var ZERO = fromInt(0);\n  \n  /**\n   * Signed zero.\n   * @type {!Long}\n   */\n  Long.ZERO = ZERO;\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var UZERO = fromInt(0, true);\n  \n  /**\n   * Unsigned zero.\n   * @type {!Long}\n   */\n  Long.UZERO = UZERO;\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var ONE = fromInt(1);\n  \n  /**\n   * Signed one.\n   * @type {!Long}\n   */\n  Long.ONE = ONE;\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var UONE = fromInt(1, true);\n  \n  /**\n   * Unsigned one.\n   * @type {!Long}\n   */\n  Long.UONE = UONE;\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var NEG_ONE = fromInt(-1);\n  \n  /**\n   * Signed negative one.\n   * @type {!Long}\n   */\n  Long.NEG_ONE = NEG_ONE;\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var MAX_VALUE = fromBits(0xFFFFFFFF | 0, 0x7FFFFFFF | 0, false);\n  \n  /**\n   * Maximum signed value.\n   * @type {!Long}\n   */\n  Long.MAX_VALUE = MAX_VALUE;\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var MAX_UNSIGNED_VALUE = fromBits(0xFFFFFFFF | 0, 0xFFFFFFFF | 0, true);\n  \n  /**\n   * Maximum unsigned value.\n   * @type {!Long}\n   */\n  Long.MAX_UNSIGNED_VALUE = MAX_UNSIGNED_VALUE;\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var MIN_VALUE = fromBits(0, 0x80000000 | 0, false);\n  \n  /**\n   * Minimum signed value.\n   * @type {!Long}\n   */\n  Long.MIN_VALUE = MIN_VALUE;\n  \n  /**\n   * @alias Long.prototype\n   * @inner\n   */\n  var LongPrototype = Long.prototype;\n  \n  /**\n   * Converts the Long to a 32 bit integer, assuming it is a 32 bit integer.\n   * @this {!Long}\n   * @returns {number}\n   */\n  LongPrototype.toInt = function toInt() {\n    return this.unsigned ? this.low >>> 0 : this.low;\n  };\n  \n  /**\n   * Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa).\n   * @this {!Long}\n   * @returns {number}\n   */\n  LongPrototype.toNumber = function toNumber() {\n    if (this.unsigned) return (this.high >>> 0) * TWO_PWR_32_DBL + (this.low >>> 0);\n    return this.high * TWO_PWR_32_DBL + (this.low >>> 0);\n  };\n  \n  /**\n   * Converts the Long to a string written in the specified radix.\n   * @this {!Long}\n   * @param {number=} radix Radix (2-36), defaults to 10\n   * @returns {string}\n   * @override\n   * @throws {RangeError} If `radix` is out of range\n   */\n  LongPrototype.toString = function toString(radix) {\n    radix = radix || 10;\n    if (radix < 2 || 36 < radix) throw RangeError('radix');\n    if (this.isZero()) return '0';\n    if (this.isNegative()) {\n      // Unsigned Longs are never negative\n      if (this.eq(MIN_VALUE)) {\n        // We need to change the Long value before it can be negated, so we remove\n        // the bottom-most digit in this base and then recurse to do the rest.\n        var radixLong = fromNumber(radix),\n          div = this.div(radixLong),\n          rem1 = div.mul(radixLong).sub(this);\n        return div.toString(radix) + rem1.toInt().toString(radix);\n      } else return '-' + this.neg().toString(radix);\n    }\n  \n    // Do several (6) digits each time through the loop, so as to\n    // minimize the calls to the very expensive emulated div.\n    var radixToPower = fromNumber(pow_dbl(radix, 6), this.unsigned),\n      rem = this;\n    var result = '';\n    while (true) {\n      var remDiv = rem.div(radixToPower),\n        intval = rem.sub(remDiv.mul(radixToPower)).toInt() >>> 0,\n        digits = intval.toString(radix);\n      rem = remDiv;\n      if (rem.isZero()) return digits + result;else {\n        while (digits.length < 6) digits = '0' + digits;\n        result = '' + digits + result;\n      }\n    }\n  };\n  \n  /**\n   * Gets the high 32 bits as a signed integer.\n   * @this {!Long}\n   * @returns {number} Signed high bits\n   */\n  LongPrototype.getHighBits = function getHighBits() {\n    return this.high;\n  };\n  \n  /**\n   * Gets the high 32 bits as an unsigned integer.\n   * @this {!Long}\n   * @returns {number} Unsigned high bits\n   */\n  LongPrototype.getHighBitsUnsigned = function getHighBitsUnsigned() {\n    return this.high >>> 0;\n  };\n  \n  /**\n   * Gets the low 32 bits as a signed integer.\n   * @this {!Long}\n   * @returns {number} Signed low bits\n   */\n  LongPrototype.getLowBits = function getLowBits() {\n    return this.low;\n  };\n  \n  /**\n   * Gets the low 32 bits as an unsigned integer.\n   * @this {!Long}\n   * @returns {number} Unsigned low bits\n   */\n  LongPrototype.getLowBitsUnsigned = function getLowBitsUnsigned() {\n    return this.low >>> 0;\n  };\n  \n  /**\n   * Gets the number of bits needed to represent the absolute value of this Long.\n   * @this {!Long}\n   * @returns {number}\n   */\n  LongPrototype.getNumBitsAbs = function getNumBitsAbs() {\n    if (this.isNegative())\n      // Unsigned Longs are never negative\n      return this.eq(MIN_VALUE) ? 64 : this.neg().getNumBitsAbs();\n    var val = this.high != 0 ? this.high : this.low;\n    for (var bit = 31; bit > 0; bit--) if ((val & 1 << bit) != 0) break;\n    return this.high != 0 ? bit + 33 : bit + 1;\n  };\n  \n  /**\n   * Tests if this Long can be safely represented as a JavaScript number.\n   * @this {!Long}\n   * @returns {boolean}\n   */\n  LongPrototype.isSafeInteger = function isSafeInteger() {\n    // 2^53-1 is the maximum safe value\n    var top11Bits = this.high >> 21;\n    // [0, 2^53-1]\n    if (!top11Bits) return true;\n    // > 2^53-1\n    if (this.unsigned) return false;\n    // [-2^53, -1] except -2^53\n    return top11Bits === -1 && !(this.low === 0 && this.high === -0x200000);\n  };\n  \n  /**\n   * Tests if this Long's value equals zero.\n   * @this {!Long}\n   * @returns {boolean}\n   */\n  LongPrototype.isZero = function isZero() {\n    return this.high === 0 && this.low === 0;\n  };\n  \n  /**\n   * Tests if this Long's value equals zero. This is an alias of {@link Long#isZero}.\n   * @returns {boolean}\n   */\n  LongPrototype.eqz = LongPrototype.isZero;\n  \n  /**\n   * Tests if this Long's value is negative.\n   * @this {!Long}\n   * @returns {boolean}\n   */\n  LongPrototype.isNegative = function isNegative() {\n    return !this.unsigned && this.high < 0;\n  };\n  \n  /**\n   * Tests if this Long's value is positive or zero.\n   * @this {!Long}\n   * @returns {boolean}\n   */\n  LongPrototype.isPositive = function isPositive() {\n    return this.unsigned || this.high >= 0;\n  };\n  \n  /**\n   * Tests if this Long's value is odd.\n   * @this {!Long}\n   * @returns {boolean}\n   */\n  LongPrototype.isOdd = function isOdd() {\n    return (this.low & 1) === 1;\n  };\n  \n  /**\n   * Tests if this Long's value is even.\n   * @this {!Long}\n   * @returns {boolean}\n   */\n  LongPrototype.isEven = function isEven() {\n    return (this.low & 1) === 0;\n  };\n  \n  /**\n   * Tests if this Long's value equals the specified's.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.equals = function equals(other) {\n    if (!isLong(other)) other = fromValue(other);\n    if (this.unsigned !== other.unsigned && this.high >>> 31 === 1 && other.high >>> 31 === 1) return false;\n    return this.high === other.high && this.low === other.low;\n  };\n  \n  /**\n   * Tests if this Long's value equals the specified's. This is an alias of {@link Long#equals}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.eq = LongPrototype.equals;\n  \n  /**\n   * Tests if this Long's value differs from the specified's.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.notEquals = function notEquals(other) {\n    return !this.eq(/* validates */other);\n  };\n  \n  /**\n   * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.neq = LongPrototype.notEquals;\n  \n  /**\n   * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.ne = LongPrototype.notEquals;\n  \n  /**\n   * Tests if this Long's value is less than the specified's.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.lessThan = function lessThan(other) {\n    return this.comp(/* validates */other) < 0;\n  };\n  \n  /**\n   * Tests if this Long's value is less than the specified's. This is an alias of {@link Long#lessThan}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.lt = LongPrototype.lessThan;\n  \n  /**\n   * Tests if this Long's value is less than or equal the specified's.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.lessThanOrEqual = function lessThanOrEqual(other) {\n    return this.comp(/* validates */other) <= 0;\n  };\n  \n  /**\n   * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.lte = LongPrototype.lessThanOrEqual;\n  \n  /**\n   * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.le = LongPrototype.lessThanOrEqual;\n  \n  /**\n   * Tests if this Long's value is greater than the specified's.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.greaterThan = function greaterThan(other) {\n    return this.comp(/* validates */other) > 0;\n  };\n  \n  /**\n   * Tests if this Long's value is greater than the specified's. This is an alias of {@link Long#greaterThan}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.gt = LongPrototype.greaterThan;\n  \n  /**\n   * Tests if this Long's value is greater than or equal the specified's.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.greaterThanOrEqual = function greaterThanOrEqual(other) {\n    return this.comp(/* validates */other) >= 0;\n  };\n  \n  /**\n   * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.gte = LongPrototype.greaterThanOrEqual;\n  \n  /**\n   * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.ge = LongPrototype.greaterThanOrEqual;\n  \n  /**\n   * Compares this Long's value with the specified's.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n   *  if the given one is greater\n   */\n  LongPrototype.compare = function compare(other) {\n    if (!isLong(other)) other = fromValue(other);\n    if (this.eq(other)) return 0;\n    var thisNeg = this.isNegative(),\n      otherNeg = other.isNegative();\n    if (thisNeg && !otherNeg) return -1;\n    if (!thisNeg && otherNeg) return 1;\n    // At this point the sign bits are the same\n    if (!this.unsigned) return this.sub(other).isNegative() ? -1 : 1;\n    // Both are positive if at least one is unsigned\n    return other.high >>> 0 > this.high >>> 0 || other.high === this.high && other.low >>> 0 > this.low >>> 0 ? -1 : 1;\n  };\n  \n  /**\n   * Compares this Long's value with the specified's. This is an alias of {@link Long#compare}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n   *  if the given one is greater\n   */\n  LongPrototype.comp = LongPrototype.compare;\n  \n  /**\n   * Negates this Long's value.\n   * @this {!Long}\n   * @returns {!Long} Negated Long\n   */\n  LongPrototype.negate = function negate() {\n    if (!this.unsigned && this.eq(MIN_VALUE)) return MIN_VALUE;\n    return this.not().add(ONE);\n  };\n  \n  /**\n   * Negates this Long's value. This is an alias of {@link Long#negate}.\n   * @function\n   * @returns {!Long} Negated Long\n   */\n  LongPrototype.neg = LongPrototype.negate;\n  \n  /**\n   * Returns the sum of this and the specified Long.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} addend Addend\n   * @returns {!Long} Sum\n   */\n  LongPrototype.add = function add(addend) {\n    if (!isLong(addend)) addend = fromValue(addend);\n  \n    // Divide each number into 4 chunks of 16 bits, and then sum the chunks.\n  \n    var a48 = this.high >>> 16;\n    var a32 = this.high & 0xFFFF;\n    var a16 = this.low >>> 16;\n    var a00 = this.low & 0xFFFF;\n    var b48 = addend.high >>> 16;\n    var b32 = addend.high & 0xFFFF;\n    var b16 = addend.low >>> 16;\n    var b00 = addend.low & 0xFFFF;\n    var c48 = 0,\n      c32 = 0,\n      c16 = 0,\n      c00 = 0;\n    c00 += a00 + b00;\n    c16 += c00 >>> 16;\n    c00 &= 0xFFFF;\n    c16 += a16 + b16;\n    c32 += c16 >>> 16;\n    c16 &= 0xFFFF;\n    c32 += a32 + b32;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c48 += a48 + b48;\n    c48 &= 0xFFFF;\n    return fromBits(c16 << 16 | c00, c48 << 16 | c32, this.unsigned);\n  };\n  \n  /**\n   * Returns the difference of this and the specified Long.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} subtrahend Subtrahend\n   * @returns {!Long} Difference\n   */\n  LongPrototype.subtract = function subtract(subtrahend) {\n    if (!isLong(subtrahend)) subtrahend = fromValue(subtrahend);\n    return this.add(subtrahend.neg());\n  };\n  \n  /**\n   * Returns the difference of this and the specified Long. This is an alias of {@link Long#subtract}.\n   * @function\n   * @param {!Long|number|bigint|string} subtrahend Subtrahend\n   * @returns {!Long} Difference\n   */\n  LongPrototype.sub = LongPrototype.subtract;\n  \n  /**\n   * Returns the product of this and the specified Long.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} multiplier Multiplier\n   * @returns {!Long} Product\n   */\n  LongPrototype.multiply = function multiply(multiplier) {\n    if (this.isZero()) return this;\n    if (!isLong(multiplier)) multiplier = fromValue(multiplier);\n  \n    // use wasm support if present\n    if (wasm) {\n      var low = wasm[\"mul\"](this.low, this.high, multiplier.low, multiplier.high);\n      return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n    }\n    if (multiplier.isZero()) return this.unsigned ? UZERO : ZERO;\n    if (this.eq(MIN_VALUE)) return multiplier.isOdd() ? MIN_VALUE : ZERO;\n    if (multiplier.eq(MIN_VALUE)) return this.isOdd() ? MIN_VALUE : ZERO;\n    if (this.isNegative()) {\n      if (multiplier.isNegative()) return this.neg().mul(multiplier.neg());else return this.neg().mul(multiplier).neg();\n    } else if (multiplier.isNegative()) return this.mul(multiplier.neg()).neg();\n  \n    // If both longs are small, use float multiplication\n    if (this.lt(TWO_PWR_24) && multiplier.lt(TWO_PWR_24)) return fromNumber(this.toNumber() * multiplier.toNumber(), this.unsigned);\n  \n    // Divide each long into 4 chunks of 16 bits, and then add up 4x4 products.\n    // We can skip products that would overflow.\n  \n    var a48 = this.high >>> 16;\n    var a32 = this.high & 0xFFFF;\n    var a16 = this.low >>> 16;\n    var a00 = this.low & 0xFFFF;\n    var b48 = multiplier.high >>> 16;\n    var b32 = multiplier.high & 0xFFFF;\n    var b16 = multiplier.low >>> 16;\n    var b00 = multiplier.low & 0xFFFF;\n    var c48 = 0,\n      c32 = 0,\n      c16 = 0,\n      c00 = 0;\n    c00 += a00 * b00;\n    c16 += c00 >>> 16;\n    c00 &= 0xFFFF;\n    c16 += a16 * b00;\n    c32 += c16 >>> 16;\n    c16 &= 0xFFFF;\n    c16 += a00 * b16;\n    c32 += c16 >>> 16;\n    c16 &= 0xFFFF;\n    c32 += a32 * b00;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c32 += a16 * b16;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c32 += a00 * b32;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c48 += a48 * b00 + a32 * b16 + a16 * b32 + a00 * b48;\n    c48 &= 0xFFFF;\n    return fromBits(c16 << 16 | c00, c48 << 16 | c32, this.unsigned);\n  };\n  \n  /**\n   * Returns the product of this and the specified Long. This is an alias of {@link Long#multiply}.\n   * @function\n   * @param {!Long|number|bigint|string} multiplier Multiplier\n   * @returns {!Long} Product\n   */\n  LongPrototype.mul = LongPrototype.multiply;\n  \n  /**\n   * Returns this Long divided by the specified. The result is signed if this Long is signed or\n   *  unsigned if this Long is unsigned.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} divisor Divisor\n   * @returns {!Long} Quotient\n   */\n  LongPrototype.divide = function divide(divisor) {\n    if (!isLong(divisor)) divisor = fromValue(divisor);\n    if (divisor.isZero()) throw Error('division by zero');\n  \n    // use wasm support if present\n    if (wasm) {\n      // guard against signed division overflow: the largest\n      // negative number / -1 would be 1 larger than the largest\n      // positive number, due to two's complement.\n      if (!this.unsigned && this.high === -0x80000000 && divisor.low === -1 && divisor.high === -1) {\n        // be consistent with non-wasm code path\n        return this;\n      }\n      var low = (this.unsigned ? wasm[\"div_u\"] : wasm[\"div_s\"])(this.low, this.high, divisor.low, divisor.high);\n      return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n    }\n    if (this.isZero()) return this.unsigned ? UZERO : ZERO;\n    var approx, rem, res;\n    if (!this.unsigned) {\n      // This section is only relevant for signed longs and is derived from the\n      // closure library as a whole.\n      if (this.eq(MIN_VALUE)) {\n        if (divisor.eq(ONE) || divisor.eq(NEG_ONE)) return MIN_VALUE; // recall that -MIN_VALUE == MIN_VALUE\n        else if (divisor.eq(MIN_VALUE)) return ONE;else {\n          // At this point, we have |other| >= 2, so |this/other| < |MIN_VALUE|.\n          var halfThis = this.shr(1);\n          approx = halfThis.div(divisor).shl(1);\n          if (approx.eq(ZERO)) {\n            return divisor.isNegative() ? ONE : NEG_ONE;\n          } else {\n            rem = this.sub(divisor.mul(approx));\n            res = approx.add(rem.div(divisor));\n            return res;\n          }\n        }\n      } else if (divisor.eq(MIN_VALUE)) return this.unsigned ? UZERO : ZERO;\n      if (this.isNegative()) {\n        if (divisor.isNegative()) return this.neg().div(divisor.neg());\n        return this.neg().div(divisor).neg();\n      } else if (divisor.isNegative()) return this.div(divisor.neg()).neg();\n      res = ZERO;\n    } else {\n      // The algorithm below has not been made for unsigned longs. It's therefore\n      // required to take special care of the MSB prior to running it.\n      if (!divisor.unsigned) divisor = divisor.toUnsigned();\n      if (divisor.gt(this)) return UZERO;\n      if (divisor.gt(this.shru(1)))\n        // 15 >>> 1 = 7 ; with divisor = 8 ; true\n        return UONE;\n      res = UZERO;\n    }\n  \n    // Repeat the following until the remainder is less than other:  find a\n    // floating-point that approximates remainder / other *from below*, add this\n    // into the result, and subtract it from the remainder.  It is critical that\n    // the approximate value is less than or equal to the real value so that the\n    // remainder never becomes negative.\n    rem = this;\n    while (rem.gte(divisor)) {\n      // Approximate the result of division. This may be a little greater or\n      // smaller than the actual value.\n      approx = Math.max(1, Math.floor(rem.toNumber() / divisor.toNumber()));\n  \n      // We will tweak the approximate result by changing it in the 48-th digit or\n      // the smallest non-fractional digit, whichever is larger.\n      var log2 = Math.ceil(Math.log(approx) / Math.LN2),\n        delta = log2 <= 48 ? 1 : pow_dbl(2, log2 - 48),\n        // Decrease the approximation until it is smaller than the remainder.  Note\n        // that if it is too large, the product overflows and is negative.\n        approxRes = fromNumber(approx),\n        approxRem = approxRes.mul(divisor);\n      while (approxRem.isNegative() || approxRem.gt(rem)) {\n        approx -= delta;\n        approxRes = fromNumber(approx, this.unsigned);\n        approxRem = approxRes.mul(divisor);\n      }\n  \n      // We know the answer can't be zero... and actually, zero would cause\n      // infinite recursion since we would make no progress.\n      if (approxRes.isZero()) approxRes = ONE;\n      res = res.add(approxRes);\n      rem = rem.sub(approxRem);\n    }\n    return res;\n  };\n  \n  /**\n   * Returns this Long divided by the specified. This is an alias of {@link Long#divide}.\n   * @function\n   * @param {!Long|number|bigint|string} divisor Divisor\n   * @returns {!Long} Quotient\n   */\n  LongPrototype.div = LongPrototype.divide;\n  \n  /**\n   * Returns this Long modulo the specified.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} divisor Divisor\n   * @returns {!Long} Remainder\n   */\n  LongPrototype.modulo = function modulo(divisor) {\n    if (!isLong(divisor)) divisor = fromValue(divisor);\n  \n    // use wasm support if present\n    if (wasm) {\n      var low = (this.unsigned ? wasm[\"rem_u\"] : wasm[\"rem_s\"])(this.low, this.high, divisor.low, divisor.high);\n      return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n    }\n    return this.sub(this.div(divisor).mul(divisor));\n  };\n  \n  /**\n   * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n   * @function\n   * @param {!Long|number|bigint|string} divisor Divisor\n   * @returns {!Long} Remainder\n   */\n  LongPrototype.mod = LongPrototype.modulo;\n  \n  /**\n   * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n   * @function\n   * @param {!Long|number|bigint|string} divisor Divisor\n   * @returns {!Long} Remainder\n   */\n  LongPrototype.rem = LongPrototype.modulo;\n  \n  /**\n   * Returns the bitwise NOT of this Long.\n   * @this {!Long}\n   * @returns {!Long}\n   */\n  LongPrototype.not = function not() {\n    return fromBits(~this.low, ~this.high, this.unsigned);\n  };\n  \n  /**\n   * Returns count leading zeros of this Long.\n   * @this {!Long}\n   * @returns {!number}\n   */\n  LongPrototype.countLeadingZeros = function countLeadingZeros() {\n    return this.high ? Math.clz32(this.high) : Math.clz32(this.low) + 32;\n  };\n  \n  /**\n   * Returns count leading zeros. This is an alias of {@link Long#countLeadingZeros}.\n   * @function\n   * @param {!Long}\n   * @returns {!number}\n   */\n  LongPrototype.clz = LongPrototype.countLeadingZeros;\n  \n  /**\n   * Returns count trailing zeros of this Long.\n   * @this {!Long}\n   * @returns {!number}\n   */\n  LongPrototype.countTrailingZeros = function countTrailingZeros() {\n    return this.low ? ctz32(this.low) : ctz32(this.high) + 32;\n  };\n  \n  /**\n   * Returns count trailing zeros. This is an alias of {@link Long#countTrailingZeros}.\n   * @function\n   * @param {!Long}\n   * @returns {!number}\n   */\n  LongPrototype.ctz = LongPrototype.countTrailingZeros;\n  \n  /**\n   * Returns the bitwise AND of this Long and the specified.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other Long\n   * @returns {!Long}\n   */\n  LongPrototype.and = function and(other) {\n    if (!isLong(other)) other = fromValue(other);\n    return fromBits(this.low & other.low, this.high & other.high, this.unsigned);\n  };\n  \n  /**\n   * Returns the bitwise OR of this Long and the specified.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other Long\n   * @returns {!Long}\n   */\n  LongPrototype.or = function or(other) {\n    if (!isLong(other)) other = fromValue(other);\n    return fromBits(this.low | other.low, this.high | other.high, this.unsigned);\n  };\n  \n  /**\n   * Returns the bitwise XOR of this Long and the given one.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other Long\n   * @returns {!Long}\n   */\n  LongPrototype.xor = function xor(other) {\n    if (!isLong(other)) other = fromValue(other);\n    return fromBits(this.low ^ other.low, this.high ^ other.high, this.unsigned);\n  };\n  \n  /**\n   * Returns this Long with bits shifted to the left by the given amount.\n   * @this {!Long}\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Shifted Long\n   */\n  LongPrototype.shiftLeft = function shiftLeft(numBits) {\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;else if (numBits < 32) return fromBits(this.low << numBits, this.high << numBits | this.low >>> 32 - numBits, this.unsigned);else return fromBits(0, this.low << numBits - 32, this.unsigned);\n  };\n  \n  /**\n   * Returns this Long with bits shifted to the left by the given amount. This is an alias of {@link Long#shiftLeft}.\n   * @function\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Shifted Long\n   */\n  LongPrototype.shl = LongPrototype.shiftLeft;\n  \n  /**\n   * Returns this Long with bits arithmetically shifted to the right by the given amount.\n   * @this {!Long}\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Shifted Long\n   */\n  LongPrototype.shiftRight = function shiftRight(numBits) {\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;else if (numBits < 32) return fromBits(this.low >>> numBits | this.high << 32 - numBits, this.high >> numBits, this.unsigned);else return fromBits(this.high >> numBits - 32, this.high >= 0 ? 0 : -1, this.unsigned);\n  };\n  \n  /**\n   * Returns this Long with bits arithmetically shifted to the right by the given amount. This is an alias of {@link Long#shiftRight}.\n   * @function\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Shifted Long\n   */\n  LongPrototype.shr = LongPrototype.shiftRight;\n  \n  /**\n   * Returns this Long with bits logically shifted to the right by the given amount.\n   * @this {!Long}\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Shifted Long\n   */\n  LongPrototype.shiftRightUnsigned = function shiftRightUnsigned(numBits) {\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;\n    if (numBits < 32) return fromBits(this.low >>> numBits | this.high << 32 - numBits, this.high >>> numBits, this.unsigned);\n    if (numBits === 32) return fromBits(this.high, 0, this.unsigned);\n    return fromBits(this.high >>> numBits - 32, 0, this.unsigned);\n  };\n  \n  /**\n   * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n   * @function\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Shifted Long\n   */\n  LongPrototype.shru = LongPrototype.shiftRightUnsigned;\n  \n  /**\n   * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n   * @function\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Shifted Long\n   */\n  LongPrototype.shr_u = LongPrototype.shiftRightUnsigned;\n  \n  /**\n   * Returns this Long with bits rotated to the left by the given amount.\n   * @this {!Long}\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Rotated Long\n   */\n  LongPrototype.rotateLeft = function rotateLeft(numBits) {\n    var b;\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;\n    if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n    if (numBits < 32) {\n      b = 32 - numBits;\n      return fromBits(this.low << numBits | this.high >>> b, this.high << numBits | this.low >>> b, this.unsigned);\n    }\n    numBits -= 32;\n    b = 32 - numBits;\n    return fromBits(this.high << numBits | this.low >>> b, this.low << numBits | this.high >>> b, this.unsigned);\n  };\n  /**\n   * Returns this Long with bits rotated to the left by the given amount. This is an alias of {@link Long#rotateLeft}.\n   * @function\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Rotated Long\n   */\n  LongPrototype.rotl = LongPrototype.rotateLeft;\n  \n  /**\n   * Returns this Long with bits rotated to the right by the given amount.\n   * @this {!Long}\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Rotated Long\n   */\n  LongPrototype.rotateRight = function rotateRight(numBits) {\n    var b;\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;\n    if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n    if (numBits < 32) {\n      b = 32 - numBits;\n      return fromBits(this.high << b | this.low >>> numBits, this.low << b | this.high >>> numBits, this.unsigned);\n    }\n    numBits -= 32;\n    b = 32 - numBits;\n    return fromBits(this.low << b | this.high >>> numBits, this.high << b | this.low >>> numBits, this.unsigned);\n  };\n  /**\n   * Returns this Long with bits rotated to the right by the given amount. This is an alias of {@link Long#rotateRight}.\n   * @function\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Rotated Long\n   */\n  LongPrototype.rotr = LongPrototype.rotateRight;\n  \n  /**\n   * Converts this Long to signed.\n   * @this {!Long}\n   * @returns {!Long} Signed long\n   */\n  LongPrototype.toSigned = function toSigned() {\n    if (!this.unsigned) return this;\n    return fromBits(this.low, this.high, false);\n  };\n  \n  /**\n   * Converts this Long to unsigned.\n   * @this {!Long}\n   * @returns {!Long} Unsigned long\n   */\n  LongPrototype.toUnsigned = function toUnsigned() {\n    if (this.unsigned) return this;\n    return fromBits(this.low, this.high, true);\n  };\n  \n  /**\n   * Converts this Long to its byte representation.\n   * @param {boolean=} le Whether little or big endian, defaults to big endian\n   * @this {!Long}\n   * @returns {!Array.<number>} Byte representation\n   */\n  LongPrototype.toBytes = function toBytes(le) {\n    return le ? this.toBytesLE() : this.toBytesBE();\n  };\n  \n  /**\n   * Converts this Long to its little endian byte representation.\n   * @this {!Long}\n   * @returns {!Array.<number>} Little endian byte representation\n   */\n  LongPrototype.toBytesLE = function toBytesLE() {\n    var hi = this.high,\n      lo = this.low;\n    return [lo & 0xff, lo >>> 8 & 0xff, lo >>> 16 & 0xff, lo >>> 24, hi & 0xff, hi >>> 8 & 0xff, hi >>> 16 & 0xff, hi >>> 24];\n  };\n  \n  /**\n   * Converts this Long to its big endian byte representation.\n   * @this {!Long}\n   * @returns {!Array.<number>} Big endian byte representation\n   */\n  LongPrototype.toBytesBE = function toBytesBE() {\n    var hi = this.high,\n      lo = this.low;\n    return [hi >>> 24, hi >>> 16 & 0xff, hi >>> 8 & 0xff, hi & 0xff, lo >>> 24, lo >>> 16 & 0xff, lo >>> 8 & 0xff, lo & 0xff];\n  };\n  \n  /**\n   * Creates a Long from its byte representation.\n   * @param {!Array.<number>} bytes Byte representation\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @param {boolean=} le Whether little or big endian, defaults to big endian\n   * @returns {Long} The corresponding Long value\n   */\n  Long.fromBytes = function fromBytes(bytes, unsigned, le) {\n    return le ? Long.fromBytesLE(bytes, unsigned) : Long.fromBytesBE(bytes, unsigned);\n  };\n  \n  /**\n   * Creates a Long from its little endian byte representation.\n   * @param {!Array.<number>} bytes Little endian byte representation\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @returns {Long} The corresponding Long value\n   */\n  Long.fromBytesLE = function fromBytesLE(bytes, unsigned) {\n    return new Long(bytes[0] | bytes[1] << 8 | bytes[2] << 16 | bytes[3] << 24, bytes[4] | bytes[5] << 8 | bytes[6] << 16 | bytes[7] << 24, unsigned);\n  };\n  \n  /**\n   * Creates a Long from its big endian byte representation.\n   * @param {!Array.<number>} bytes Big endian byte representation\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @returns {Long} The corresponding Long value\n   */\n  Long.fromBytesBE = function fromBytesBE(bytes, unsigned) {\n    return new Long(bytes[4] << 24 | bytes[5] << 16 | bytes[6] << 8 | bytes[7], bytes[0] << 24 | bytes[1] << 16 | bytes[2] << 8 | bytes[3], unsigned);\n  };\n  \n  // Support conversion to/from BigInt where available\n  if (typeof BigInt === \"function\") {\n    /**\n     * Returns a Long representing the given big integer.\n     * @function\n     * @param {number} value The big integer value\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {!Long} The corresponding Long value\n     */\n    Long.fromBigInt = function fromBigInt(value, unsigned) {\n      var lowBits = Number(BigInt.asIntN(32, value));\n      var highBits = Number(BigInt.asIntN(32, value >> BigInt(32)));\n      return fromBits(lowBits, highBits, unsigned);\n    };\n  \n    // Override\n    Long.fromValue = function fromValueWithBigInt(value, unsigned) {\n      if (typeof value === \"bigint\") return fromBigInt(value, unsigned);\n      return fromValue(value, unsigned);\n    };\n  \n    /**\n     * Converts the Long to its big integer representation.\n     * @this {!Long}\n     * @returns {bigint}\n     */\n    LongPrototype.toBigInt = function toBigInt() {\n      var lowBigInt = BigInt(this.low >>> 0);\n      var highBigInt = BigInt(this.unsigned ? this.high >>> 0 : this.high);\n      return highBigInt << BigInt(32) | lowBigInt;\n    };\n  }\n  var _default = exports.default = Long;\n  return \"default\" in exports ? exports.default : exports;\n})({});\nif (true) !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_RESULT__ = (function() { return Long; }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\nelse {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/long/umd/index.js\n");

/***/ })

};
;