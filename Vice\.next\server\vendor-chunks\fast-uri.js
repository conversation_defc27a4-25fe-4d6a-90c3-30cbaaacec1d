"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-uri";
exports.ids = ["vendor-chunks/fast-uri"];
exports.modules = {

/***/ "(action-browser)/./node_modules/fast-uri/index.js":
/*!****************************************!*\
  !*** ./node_modules/fast-uri/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { normalizeIPv6, normalizeIPv4, removeDotSegments, recomposeAuthority, normalizeComponentEncoding } = __webpack_require__(/*! ./lib/utils */ \"(action-browser)/./node_modules/fast-uri/lib/utils.js\")\nconst SCHEMES = __webpack_require__(/*! ./lib/schemes */ \"(action-browser)/./node_modules/fast-uri/lib/schemes.js\")\n\nfunction normalize (uri, options) {\n  if (typeof uri === 'string') {\n    uri = serialize(parse(uri, options), options)\n  } else if (typeof uri === 'object') {\n    uri = parse(serialize(uri, options), options)\n  }\n  return uri\n}\n\nfunction resolve (baseURI, relativeURI, options) {\n  const schemelessOptions = Object.assign({ scheme: 'null' }, options)\n  const resolved = resolveComponents(parse(baseURI, schemelessOptions), parse(relativeURI, schemelessOptions), schemelessOptions, true)\n  return serialize(resolved, { ...schemelessOptions, skipEscape: true })\n}\n\nfunction resolveComponents (base, relative, options, skipNormalization) {\n  const target = {}\n  if (!skipNormalization) {\n    base = parse(serialize(base, options), options) // normalize base components\n    relative = parse(serialize(relative, options), options) // normalize relative components\n  }\n  options = options || {}\n\n  if (!options.tolerant && relative.scheme) {\n    target.scheme = relative.scheme\n    // target.authority = relative.authority;\n    target.userinfo = relative.userinfo\n    target.host = relative.host\n    target.port = relative.port\n    target.path = removeDotSegments(relative.path || '')\n    target.query = relative.query\n  } else {\n    if (relative.userinfo !== undefined || relative.host !== undefined || relative.port !== undefined) {\n      // target.authority = relative.authority;\n      target.userinfo = relative.userinfo\n      target.host = relative.host\n      target.port = relative.port\n      target.path = removeDotSegments(relative.path || '')\n      target.query = relative.query\n    } else {\n      if (!relative.path) {\n        target.path = base.path\n        if (relative.query !== undefined) {\n          target.query = relative.query\n        } else {\n          target.query = base.query\n        }\n      } else {\n        if (relative.path.charAt(0) === '/') {\n          target.path = removeDotSegments(relative.path)\n        } else {\n          if ((base.userinfo !== undefined || base.host !== undefined || base.port !== undefined) && !base.path) {\n            target.path = '/' + relative.path\n          } else if (!base.path) {\n            target.path = relative.path\n          } else {\n            target.path = base.path.slice(0, base.path.lastIndexOf('/') + 1) + relative.path\n          }\n          target.path = removeDotSegments(target.path)\n        }\n        target.query = relative.query\n      }\n      // target.authority = base.authority;\n      target.userinfo = base.userinfo\n      target.host = base.host\n      target.port = base.port\n    }\n    target.scheme = base.scheme\n  }\n\n  target.fragment = relative.fragment\n\n  return target\n}\n\nfunction equal (uriA, uriB, options) {\n  if (typeof uriA === 'string') {\n    uriA = unescape(uriA)\n    uriA = serialize(normalizeComponentEncoding(parse(uriA, options), true), { ...options, skipEscape: true })\n  } else if (typeof uriA === 'object') {\n    uriA = serialize(normalizeComponentEncoding(uriA, true), { ...options, skipEscape: true })\n  }\n\n  if (typeof uriB === 'string') {\n    uriB = unescape(uriB)\n    uriB = serialize(normalizeComponentEncoding(parse(uriB, options), true), { ...options, skipEscape: true })\n  } else if (typeof uriB === 'object') {\n    uriB = serialize(normalizeComponentEncoding(uriB, true), { ...options, skipEscape: true })\n  }\n\n  return uriA.toLowerCase() === uriB.toLowerCase()\n}\n\nfunction serialize (cmpts, opts) {\n  const components = {\n    host: cmpts.host,\n    scheme: cmpts.scheme,\n    userinfo: cmpts.userinfo,\n    port: cmpts.port,\n    path: cmpts.path,\n    query: cmpts.query,\n    nid: cmpts.nid,\n    nss: cmpts.nss,\n    uuid: cmpts.uuid,\n    fragment: cmpts.fragment,\n    reference: cmpts.reference,\n    resourceName: cmpts.resourceName,\n    secure: cmpts.secure,\n    error: ''\n  }\n  const options = Object.assign({}, opts)\n  const uriTokens = []\n\n  // find scheme handler\n  const schemeHandler = SCHEMES[(options.scheme || components.scheme || '').toLowerCase()]\n\n  // perform scheme specific serialization\n  if (schemeHandler && schemeHandler.serialize) schemeHandler.serialize(components, options)\n\n  if (components.path !== undefined) {\n    if (!options.skipEscape) {\n      components.path = escape(components.path)\n\n      if (components.scheme !== undefined) {\n        components.path = components.path.split('%3A').join(':')\n      }\n    } else {\n      components.path = unescape(components.path)\n    }\n  }\n\n  if (options.reference !== 'suffix' && components.scheme) {\n    uriTokens.push(components.scheme, ':')\n  }\n\n  const authority = recomposeAuthority(components)\n  if (authority !== undefined) {\n    if (options.reference !== 'suffix') {\n      uriTokens.push('//')\n    }\n\n    uriTokens.push(authority)\n\n    if (components.path && components.path.charAt(0) !== '/') {\n      uriTokens.push('/')\n    }\n  }\n  if (components.path !== undefined) {\n    let s = components.path\n\n    if (!options.absolutePath && (!schemeHandler || !schemeHandler.absolutePath)) {\n      s = removeDotSegments(s)\n    }\n\n    if (authority === undefined) {\n      s = s.replace(/^\\/\\//u, '/%2F') // don't allow the path to start with \"//\"\n    }\n\n    uriTokens.push(s)\n  }\n\n  if (components.query !== undefined) {\n    uriTokens.push('?', components.query)\n  }\n\n  if (components.fragment !== undefined) {\n    uriTokens.push('#', components.fragment)\n  }\n  return uriTokens.join('')\n}\n\nconst hexLookUp = Array.from({ length: 127 }, (_v, k) => /[^!\"$&'()*+,\\-.;=_`a-z{}~]/u.test(String.fromCharCode(k)))\n\nfunction nonSimpleDomain (value) {\n  let code = 0\n  for (let i = 0, len = value.length; i < len; ++i) {\n    code = value.charCodeAt(i)\n    if (code > 126 || hexLookUp[code]) {\n      return true\n    }\n  }\n  return false\n}\n\nconst URI_PARSE = /^(?:([^#/:?]+):)?(?:\\/\\/((?:([^#/?@]*)@)?(\\[[^#/?\\]]+\\]|[^#/:?]*)(?::(\\d*))?))?([^#?]*)(?:\\?([^#]*))?(?:#((?:.|[\\n\\r])*))?/u\n\nfunction parse (uri, opts) {\n  const options = Object.assign({}, opts)\n  const parsed = {\n    scheme: undefined,\n    userinfo: undefined,\n    host: '',\n    port: undefined,\n    path: '',\n    query: undefined,\n    fragment: undefined\n  }\n  const gotEncoding = uri.indexOf('%') !== -1\n  let isIP = false\n  if (options.reference === 'suffix') uri = (options.scheme ? options.scheme + ':' : '') + '//' + uri\n\n  const matches = uri.match(URI_PARSE)\n\n  if (matches) {\n    // store each component\n    parsed.scheme = matches[1]\n    parsed.userinfo = matches[3]\n    parsed.host = matches[4]\n    parsed.port = parseInt(matches[5], 10)\n    parsed.path = matches[6] || ''\n    parsed.query = matches[7]\n    parsed.fragment = matches[8]\n\n    // fix port number\n    if (isNaN(parsed.port)) {\n      parsed.port = matches[5]\n    }\n    if (parsed.host) {\n      const ipv4result = normalizeIPv4(parsed.host)\n      if (ipv4result.isIPV4 === false) {\n        const ipv6result = normalizeIPv6(ipv4result.host)\n        parsed.host = ipv6result.host.toLowerCase()\n        isIP = ipv6result.isIPV6\n      } else {\n        parsed.host = ipv4result.host\n        isIP = true\n      }\n    }\n    if (parsed.scheme === undefined && parsed.userinfo === undefined && parsed.host === undefined && parsed.port === undefined && parsed.query === undefined && !parsed.path) {\n      parsed.reference = 'same-document'\n    } else if (parsed.scheme === undefined) {\n      parsed.reference = 'relative'\n    } else if (parsed.fragment === undefined) {\n      parsed.reference = 'absolute'\n    } else {\n      parsed.reference = 'uri'\n    }\n\n    // check for reference errors\n    if (options.reference && options.reference !== 'suffix' && options.reference !== parsed.reference) {\n      parsed.error = parsed.error || 'URI is not a ' + options.reference + ' reference.'\n    }\n\n    // find scheme handler\n    const schemeHandler = SCHEMES[(options.scheme || parsed.scheme || '').toLowerCase()]\n\n    // check if scheme can't handle IRIs\n    if (!options.unicodeSupport && (!schemeHandler || !schemeHandler.unicodeSupport)) {\n      // if host component is a domain name\n      if (parsed.host && (options.domainHost || (schemeHandler && schemeHandler.domainHost)) && isIP === false && nonSimpleDomain(parsed.host)) {\n        // convert Unicode IDN -> ASCII IDN\n        try {\n          parsed.host = URL.domainToASCII(parsed.host.toLowerCase())\n        } catch (e) {\n          parsed.error = parsed.error || \"Host's domain name can not be converted to ASCII: \" + e\n        }\n      }\n      // convert IRI -> URI\n    }\n\n    if (!schemeHandler || (schemeHandler && !schemeHandler.skipNormalize)) {\n      if (gotEncoding && parsed.scheme !== undefined) {\n        parsed.scheme = unescape(parsed.scheme)\n      }\n      if (gotEncoding && parsed.host !== undefined) {\n        parsed.host = unescape(parsed.host)\n      }\n      if (parsed.path) {\n        parsed.path = escape(unescape(parsed.path))\n      }\n      if (parsed.fragment) {\n        parsed.fragment = encodeURI(decodeURIComponent(parsed.fragment))\n      }\n    }\n\n    // perform scheme specific parsing\n    if (schemeHandler && schemeHandler.parse) {\n      schemeHandler.parse(parsed, options)\n    }\n  } else {\n    parsed.error = parsed.error || 'URI can not be parsed.'\n  }\n  return parsed\n}\n\nconst fastUri = {\n  SCHEMES,\n  normalize,\n  resolve,\n  resolveComponents,\n  equal,\n  serialize,\n  parse\n}\n\nmodule.exports = fastUri\nmodule.exports[\"default\"] = fastUri\nmodule.exports.fastUri = fastUri\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fast-uri/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/fast-uri/lib/schemes.js":
/*!**********************************************!*\
  !*** ./node_modules/fast-uri/lib/schemes.js ***!
  \**********************************************/
/***/ ((module) => {

eval("\n\nconst UUID_REG = /^[\\da-f]{8}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{12}$/iu\nconst URN_REG = /([\\da-z][\\d\\-a-z]{0,31}):((?:[\\w!$'()*+,\\-.:;=@]|%[\\da-f]{2})+)/iu\n\nfunction isSecure (wsComponents) {\n  return typeof wsComponents.secure === 'boolean' ? wsComponents.secure : String(wsComponents.scheme).toLowerCase() === 'wss'\n}\n\nfunction httpParse (components) {\n  if (!components.host) {\n    components.error = components.error || 'HTTP URIs must have a host.'\n  }\n\n  return components\n}\n\nfunction httpSerialize (components) {\n  const secure = String(components.scheme).toLowerCase() === 'https'\n\n  // normalize the default port\n  if (components.port === (secure ? 443 : 80) || components.port === '') {\n    components.port = undefined\n  }\n\n  // normalize the empty path\n  if (!components.path) {\n    components.path = '/'\n  }\n\n  // NOTE: We do not parse query strings for HTTP URIs\n  // as WWW Form Url Encoded query strings are part of the HTML4+ spec,\n  // and not the HTTP spec.\n\n  return components\n}\n\nfunction wsParse (wsComponents) {\n// indicate if the secure flag is set\n  wsComponents.secure = isSecure(wsComponents)\n\n  // construct resouce name\n  wsComponents.resourceName = (wsComponents.path || '/') + (wsComponents.query ? '?' + wsComponents.query : '')\n  wsComponents.path = undefined\n  wsComponents.query = undefined\n\n  return wsComponents\n}\n\nfunction wsSerialize (wsComponents) {\n// normalize the default port\n  if (wsComponents.port === (isSecure(wsComponents) ? 443 : 80) || wsComponents.port === '') {\n    wsComponents.port = undefined\n  }\n\n  // ensure scheme matches secure flag\n  if (typeof wsComponents.secure === 'boolean') {\n    wsComponents.scheme = (wsComponents.secure ? 'wss' : 'ws')\n    wsComponents.secure = undefined\n  }\n\n  // reconstruct path from resource name\n  if (wsComponents.resourceName) {\n    const [path, query] = wsComponents.resourceName.split('?')\n    wsComponents.path = (path && path !== '/' ? path : undefined)\n    wsComponents.query = query\n    wsComponents.resourceName = undefined\n  }\n\n  // forbid fragment component\n  wsComponents.fragment = undefined\n\n  return wsComponents\n}\n\nfunction urnParse (urnComponents, options) {\n  if (!urnComponents.path) {\n    urnComponents.error = 'URN can not be parsed'\n    return urnComponents\n  }\n  const matches = urnComponents.path.match(URN_REG)\n  if (matches) {\n    const scheme = options.scheme || urnComponents.scheme || 'urn'\n    urnComponents.nid = matches[1].toLowerCase()\n    urnComponents.nss = matches[2]\n    const urnScheme = `${scheme}:${options.nid || urnComponents.nid}`\n    const schemeHandler = SCHEMES[urnScheme]\n    urnComponents.path = undefined\n\n    if (schemeHandler) {\n      urnComponents = schemeHandler.parse(urnComponents, options)\n    }\n  } else {\n    urnComponents.error = urnComponents.error || 'URN can not be parsed.'\n  }\n\n  return urnComponents\n}\n\nfunction urnSerialize (urnComponents, options) {\n  const scheme = options.scheme || urnComponents.scheme || 'urn'\n  const nid = urnComponents.nid.toLowerCase()\n  const urnScheme = `${scheme}:${options.nid || nid}`\n  const schemeHandler = SCHEMES[urnScheme]\n\n  if (schemeHandler) {\n    urnComponents = schemeHandler.serialize(urnComponents, options)\n  }\n\n  const uriComponents = urnComponents\n  const nss = urnComponents.nss\n  uriComponents.path = `${nid || options.nid}:${nss}`\n\n  options.skipEscape = true\n  return uriComponents\n}\n\nfunction urnuuidParse (urnComponents, options) {\n  const uuidComponents = urnComponents\n  uuidComponents.uuid = uuidComponents.nss\n  uuidComponents.nss = undefined\n\n  if (!options.tolerant && (!uuidComponents.uuid || !UUID_REG.test(uuidComponents.uuid))) {\n    uuidComponents.error = uuidComponents.error || 'UUID is not valid.'\n  }\n\n  return uuidComponents\n}\n\nfunction urnuuidSerialize (uuidComponents) {\n  const urnComponents = uuidComponents\n  // normalize UUID\n  urnComponents.nss = (uuidComponents.uuid || '').toLowerCase()\n  return urnComponents\n}\n\nconst http = {\n  scheme: 'http',\n  domainHost: true,\n  parse: httpParse,\n  serialize: httpSerialize\n}\n\nconst https = {\n  scheme: 'https',\n  domainHost: http.domainHost,\n  parse: httpParse,\n  serialize: httpSerialize\n}\n\nconst ws = {\n  scheme: 'ws',\n  domainHost: true,\n  parse: wsParse,\n  serialize: wsSerialize\n}\n\nconst wss = {\n  scheme: 'wss',\n  domainHost: ws.domainHost,\n  parse: ws.parse,\n  serialize: ws.serialize\n}\n\nconst urn = {\n  scheme: 'urn',\n  parse: urnParse,\n  serialize: urnSerialize,\n  skipNormalize: true\n}\n\nconst urnuuid = {\n  scheme: 'urn:uuid',\n  parse: urnuuidParse,\n  serialize: urnuuidSerialize,\n  skipNormalize: true\n}\n\nconst SCHEMES = {\n  http,\n  https,\n  ws,\n  wss,\n  urn,\n  'urn:uuid': urnuuid\n}\n\nmodule.exports = SCHEMES\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fast-uri/lib/schemes.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/fast-uri/lib/scopedChars.js":
/*!**************************************************!*\
  !*** ./node_modules/fast-uri/lib/scopedChars.js ***!
  \**************************************************/
/***/ ((module) => {

eval("\n\nconst HEX = {\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3,\n  4: 4,\n  5: 5,\n  6: 6,\n  7: 7,\n  8: 8,\n  9: 9,\n  a: 10,\n  A: 10,\n  b: 11,\n  B: 11,\n  c: 12,\n  C: 12,\n  d: 13,\n  D: 13,\n  e: 14,\n  E: 14,\n  f: 15,\n  F: 15\n}\n\nmodule.exports = {\n  HEX\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mYXN0LXVyaS9saWIvc2NvcGVkQ2hhcnMuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHByb2plY3RzXFxOZXcgZm9sZGVyXFxWaWNlXFxWaWNlXFxub2RlX21vZHVsZXNcXGZhc3QtdXJpXFxsaWJcXHNjb3BlZENoYXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBIRVggPSB7XG4gIDA6IDAsXG4gIDE6IDEsXG4gIDI6IDIsXG4gIDM6IDMsXG4gIDQ6IDQsXG4gIDU6IDUsXG4gIDY6IDYsXG4gIDc6IDcsXG4gIDg6IDgsXG4gIDk6IDksXG4gIGE6IDEwLFxuICBBOiAxMCxcbiAgYjogMTEsXG4gIEI6IDExLFxuICBjOiAxMixcbiAgQzogMTIsXG4gIGQ6IDEzLFxuICBEOiAxMyxcbiAgZTogMTQsXG4gIEU6IDE0LFxuICBmOiAxNSxcbiAgRjogMTVcbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIEhFWFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fast-uri/lib/scopedChars.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/fast-uri/lib/utils.js":
/*!********************************************!*\
  !*** ./node_modules/fast-uri/lib/utils.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { HEX } = __webpack_require__(/*! ./scopedChars */ \"(action-browser)/./node_modules/fast-uri/lib/scopedChars.js\")\n\nconst IPV4_REG = /^(?:(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)$/u\n\nfunction normalizeIPv4 (host) {\n  if (findToken(host, '.') < 3) { return { host, isIPV4: false } }\n  const matches = host.match(IPV4_REG) || []\n  const [address] = matches\n  if (address) {\n    return { host: stripLeadingZeros(address, '.'), isIPV4: true }\n  } else {\n    return { host, isIPV4: false }\n  }\n}\n\n/**\n * @param {string[]} input\n * @param {boolean} [keepZero=false]\n * @returns {string|undefined}\n */\nfunction stringArrayToHexStripped (input, keepZero = false) {\n  let acc = ''\n  let strip = true\n  for (const c of input) {\n    if (HEX[c] === undefined) return undefined\n    if (c !== '0' && strip === true) strip = false\n    if (!strip) acc += c\n  }\n  if (keepZero && acc.length === 0) acc = '0'\n  return acc\n}\n\nfunction getIPV6 (input) {\n  let tokenCount = 0\n  const output = { error: false, address: '', zone: '' }\n  const address = []\n  const buffer = []\n  let isZone = false\n  let endipv6Encountered = false\n  let endIpv6 = false\n\n  function consume () {\n    if (buffer.length) {\n      if (isZone === false) {\n        const hex = stringArrayToHexStripped(buffer)\n        if (hex !== undefined) {\n          address.push(hex)\n        } else {\n          output.error = true\n          return false\n        }\n      }\n      buffer.length = 0\n    }\n    return true\n  }\n\n  for (let i = 0; i < input.length; i++) {\n    const cursor = input[i]\n    if (cursor === '[' || cursor === ']') { continue }\n    if (cursor === ':') {\n      if (endipv6Encountered === true) {\n        endIpv6 = true\n      }\n      if (!consume()) { break }\n      tokenCount++\n      address.push(':')\n      if (tokenCount > 7) {\n        // not valid\n        output.error = true\n        break\n      }\n      if (i - 1 >= 0 && input[i - 1] === ':') {\n        endipv6Encountered = true\n      }\n      continue\n    } else if (cursor === '%') {\n      if (!consume()) { break }\n      // switch to zone detection\n      isZone = true\n    } else {\n      buffer.push(cursor)\n      continue\n    }\n  }\n  if (buffer.length) {\n    if (isZone) {\n      output.zone = buffer.join('')\n    } else if (endIpv6) {\n      address.push(buffer.join(''))\n    } else {\n      address.push(stringArrayToHexStripped(buffer))\n    }\n  }\n  output.address = address.join('')\n  return output\n}\n\nfunction normalizeIPv6 (host) {\n  if (findToken(host, ':') < 2) { return { host, isIPV6: false } }\n  const ipv6 = getIPV6(host)\n\n  if (!ipv6.error) {\n    let newHost = ipv6.address\n    let escapedHost = ipv6.address\n    if (ipv6.zone) {\n      newHost += '%' + ipv6.zone\n      escapedHost += '%25' + ipv6.zone\n    }\n    return { host: newHost, escapedHost, isIPV6: true }\n  } else {\n    return { host, isIPV6: false }\n  }\n}\n\nfunction stripLeadingZeros (str, token) {\n  let out = ''\n  let skip = true\n  const l = str.length\n  for (let i = 0; i < l; i++) {\n    const c = str[i]\n    if (c === '0' && skip) {\n      if ((i + 1 <= l && str[i + 1] === token) || i + 1 === l) {\n        out += c\n        skip = false\n      }\n    } else {\n      if (c === token) {\n        skip = true\n      } else {\n        skip = false\n      }\n      out += c\n    }\n  }\n  return out\n}\n\nfunction findToken (str, token) {\n  let ind = 0\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === token) ind++\n  }\n  return ind\n}\n\nconst RDS1 = /^\\.\\.?\\//u\nconst RDS2 = /^\\/\\.(?:\\/|$)/u\nconst RDS3 = /^\\/\\.\\.(?:\\/|$)/u\nconst RDS5 = /^\\/?(?:.|\\n)*?(?=\\/|$)/u\n\nfunction removeDotSegments (input) {\n  const output = []\n\n  while (input.length) {\n    if (input.match(RDS1)) {\n      input = input.replace(RDS1, '')\n    } else if (input.match(RDS2)) {\n      input = input.replace(RDS2, '/')\n    } else if (input.match(RDS3)) {\n      input = input.replace(RDS3, '/')\n      output.pop()\n    } else if (input === '.' || input === '..') {\n      input = ''\n    } else {\n      const im = input.match(RDS5)\n      if (im) {\n        const s = im[0]\n        input = input.slice(s.length)\n        output.push(s)\n      } else {\n        throw new Error('Unexpected dot segment condition')\n      }\n    }\n  }\n  return output.join('')\n}\n\nfunction normalizeComponentEncoding (components, esc) {\n  const func = esc !== true ? escape : unescape\n  if (components.scheme !== undefined) {\n    components.scheme = func(components.scheme)\n  }\n  if (components.userinfo !== undefined) {\n    components.userinfo = func(components.userinfo)\n  }\n  if (components.host !== undefined) {\n    components.host = func(components.host)\n  }\n  if (components.path !== undefined) {\n    components.path = func(components.path)\n  }\n  if (components.query !== undefined) {\n    components.query = func(components.query)\n  }\n  if (components.fragment !== undefined) {\n    components.fragment = func(components.fragment)\n  }\n  return components\n}\n\nfunction recomposeAuthority (components) {\n  const uriTokens = []\n\n  if (components.userinfo !== undefined) {\n    uriTokens.push(components.userinfo)\n    uriTokens.push('@')\n  }\n\n  if (components.host !== undefined) {\n    let host = unescape(components.host)\n    const ipV4res = normalizeIPv4(host)\n\n    if (ipV4res.isIPV4) {\n      host = ipV4res.host\n    } else {\n      const ipV6res = normalizeIPv6(ipV4res.host)\n      if (ipV6res.isIPV6 === true) {\n        host = `[${ipV6res.escapedHost}]`\n      } else {\n        host = components.host\n      }\n    }\n    uriTokens.push(host)\n  }\n\n  if (typeof components.port === 'number' || typeof components.port === 'string') {\n    uriTokens.push(':')\n    uriTokens.push(String(components.port))\n  }\n\n  return uriTokens.length ? uriTokens.join('') : undefined\n};\n\nmodule.exports = {\n  recomposeAuthority,\n  normalizeComponentEncoding,\n  removeDotSegments,\n  normalizeIPv4,\n  normalizeIPv6,\n  stringArrayToHexStripped\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fast-uri/lib/utils.js\n");

/***/ })

};
;