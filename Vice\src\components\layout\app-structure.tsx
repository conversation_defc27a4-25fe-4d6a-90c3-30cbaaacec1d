'use client';

import React from 'react';
import { useAuth } from '@/providers/auth-provider';
import { SidebarProvider, Sidebar, SidebarInset } from '@/components/ui/sidebar';
import LeftSidebar from '@/components/layout/left-sidebar';
import TopBar from '@/components/layout/top-bar';
import { AIChatProvider, useAIChat } from '@/providers/ai-chat-provider';
import { IframePanelProvider, useIframePanel } from '@/providers/iframe-panel-provider';
import AIChatModal from '@/components/ai-chat/ai-chat-modal';
import QuizPanel from '@/components/iframe-panels/quiz-panel';
import GitHubPanel from '@/components/iframe-panels/github-panel';
import SketchPanel from '@/components/iframe-panels/sketch-panel';
import FormsPanel from '@/components/iframe-panels/forms-panel';
import ComingSoonPanel from '@/components/coming-soon/coming-soon-panel';

interface AppStructureProps {
  children: React.ReactNode;
}

// Inner component that has access to AI chat and iframe panel contexts
const AppStructureInner: React.FC<AppStructureProps> = ({ children }) => {
  const { isAIChatOpen, setIsAIChatOpen } = useAIChat();
  const { isQuizOpen, isGitHubOpen, isSketchOpen, isDiagramOpen, isChatPdfOpen, isGeneralSettingOpen, closeAllPanels } = useIframePanel();

  // If AI Chat is open, show AI chat in the main content area
  if (isAIChatOpen) {
    return (
      <div className="flex h-screen overflow-hidden">
        <SidebarProvider>
          <Sidebar collapsible="icon" style={{ '--sidebar-width': '12rem' } as React.CSSProperties}>
            <LeftSidebar />
          </Sidebar>
          <SidebarInset className="flex flex-col flex-1 min-h-0 bg-background overflow-hidden">
            <TopBar />
            <main className="flex-1 overflow-hidden">
              <AIChatModal
                isOpen={true}
                onClose={() => setIsAIChatOpen(false)}
              />
            </main>
          </SidebarInset>
        </SidebarProvider>
      </div>
    );
  }

  // If Quiz panel is open, show Quiz iframe in the main content area
  if (isQuizOpen) {
    return (
      <div className="flex h-screen overflow-hidden">
        <SidebarProvider>
          <Sidebar>
            <LeftSidebar />
          </Sidebar>
          <SidebarInset className="flex flex-col flex-1 min-h-0 bg-background overflow-hidden">
            <TopBar />
            <main className="flex-1 overflow-hidden">
              <QuizPanel
                isOpen={true}
                onClose={closeAllPanels}
              />
            </main>
          </SidebarInset>
        </SidebarProvider>
      </div>
    );
  }

  // If GitHub panel is open, show GitHub iframe in the main content area
  if (isGitHubOpen) {
    return (
      <div className="flex h-screen overflow-hidden">
        <SidebarProvider>
          <Sidebar>
            <LeftSidebar />
          </Sidebar>
          <SidebarInset className="flex flex-col flex-1 min-h-0 bg-background overflow-hidden">
            <TopBar />
            <main className="flex-1 overflow-hidden">
              <GitHubPanel
                isOpen={true}
                onClose={closeAllPanels}
              />
            </main>
          </SidebarInset>
        </SidebarProvider>
      </div>
    );
  }

  // If Sketch panel is open, show Sketch iframe in the main content area
  if (isSketchOpen) {
    return (
      <div className="flex h-screen overflow-hidden">
        <SidebarProvider>
          <Sidebar>
            <LeftSidebar />
          </Sidebar>
          <SidebarInset className="flex flex-col flex-1 min-h-0 bg-background overflow-hidden">
            <TopBar />
            <main className="flex-1 overflow-hidden">
              <SketchPanel
                isOpen={true}
                onClose={closeAllPanels}
              />
            </main>
          </SidebarInset>
        </SidebarProvider>
      </div>
    );
  }

  // If Diagram panel is open, show coming soon page
  if (isDiagramOpen) {
    return (
      <div className="flex h-screen overflow-hidden">
        <SidebarProvider>
          <Sidebar>
            <LeftSidebar />
          </Sidebar>
          <SidebarInset className="flex flex-col flex-1 min-h-0 bg-background overflow-hidden">
            <TopBar />
            <main className="flex-1 overflow-hidden">
              <ComingSoonPanel
                isOpen={true}
                onClose={closeAllPanels}
                featureName="Diagram"
              />
            </main>
          </SidebarInset>
        </SidebarProvider>
      </div>
    );
  }

  // If Chat PDF panel is open, show Forms panel
  if (isChatPdfOpen) {
    return (
      <div className="flex h-screen overflow-hidden">
        <SidebarProvider>
          <Sidebar>
            <LeftSidebar />
          </Sidebar>
          <SidebarInset className="flex flex-col flex-1 min-h-0 bg-background overflow-hidden">
            <TopBar />
            <main className="flex-1 overflow-hidden">
              <FormsPanel
                isOpen={true}
                onClose={closeAllPanels}
              />
            </main>
          </SidebarInset>
        </SidebarProvider>
      </div>
    );
  }

  // If General Setting panel is open, show coming soon page
  if (isGeneralSettingOpen) {
    return (
      <div className="flex h-screen overflow-hidden">
        <SidebarProvider>
          <Sidebar>
            <LeftSidebar />
          </Sidebar>
          <SidebarInset className="flex flex-col flex-1 min-h-0 bg-background overflow-hidden">
            <TopBar />
            <main className="flex-1 overflow-hidden">
              <ComingSoonPanel
                isOpen={true}
                onClose={closeAllPanels}
                featureName="General Settings"
                description="Customize your Vice experience with comprehensive settings for themes, preferences, integrations, and workspace configurations."
              />
            </main>
          </SidebarInset>
        </SidebarProvider>
      </div>
    );
  }

  // Otherwise show normal content
  return (
    <div className="flex h-screen overflow-hidden">
      <SidebarProvider>
        <Sidebar collapsible="icon" style={{ '--sidebar-width': '12rem' } as React.CSSProperties}>
          <LeftSidebar />
        </Sidebar>
        <SidebarInset className="flex flex-col flex-1 min-h-0 bg-background overflow-hidden">
          <TopBar />
          <main className="flex-1 overflow-y-auto">
            {children}
          </main>
        </SidebarInset>
      </SidebarProvider>
    </div>
  );
};

const AppStructure: React.FC<AppStructureProps> = ({ children }) => {
  // Get auth state from useAuth
  const { user, loading } = useAuth();

  // While loading, render nothing or a minimal loading state
  if (loading) {
    // Optional: Render a global loading indicator if needed,
    // but page.tsx already handles its own loading state.
    // Return null to let the page handle its loading state.
    return null;
     // Or return <div className="flex items-center justify-center h-screen bg-muted">Loading App...</div>;
  }

  // If user is not logged in, render children directly (LoginPage)
  if (!user) {
    return <>{children}</>;
  }

  // If user is logged in, render the full dashboard structure
  return (
    <AIChatProvider>
      <IframePanelProvider>
        <AppStructureInner>{children}</AppStructureInner>
      </IframePanelProvider>
    </AIChatProvider>
  );
};

export default AppStructure;
