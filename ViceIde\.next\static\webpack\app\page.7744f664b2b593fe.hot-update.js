"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AIChatAssistant.tsx":
/*!********************************************!*\
  !*** ./src/components/AIChatAssistant.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_command__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/command */ \"(app-pages-browser)/./src/components/ui/command.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Check,ClipboardCheck,Copy,FileCode,FilePlus2,Library,Loader2,Send,SquarePen,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Check,ClipboardCheck,Copy,FileCode,FilePlus2,Library,Loader2,Send,SquarePen,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Check,ClipboardCheck,Copy,FileCode,FilePlus2,Library,Loader2,Send,SquarePen,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Check,ClipboardCheck,Copy,FileCode,FilePlus2,Library,Loader2,Send,SquarePen,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-plus-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Check,ClipboardCheck,Copy,FileCode,FilePlus2,Library,Loader2,Send,SquarePen,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/library.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Check,ClipboardCheck,Copy,FileCode,FilePlus2,Library,Loader2,Send,SquarePen,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Check,ClipboardCheck,Copy,FileCode,FilePlus2,Library,Loader2,Send,SquarePen,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Check,ClipboardCheck,Copy,FileCode,FilePlus2,Library,Loader2,Send,SquarePen,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Check,ClipboardCheck,Copy,FileCode,FilePlus2,Library,Loader2,Send,SquarePen,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-code.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Check,ClipboardCheck,Copy,FileCode,FilePlus2,Library,Loader2,Send,SquarePen,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Check,ClipboardCheck,Copy,FileCode,FilePlus2,Library,Loader2,Send,SquarePen,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _ai_flows_ai_code_assistance__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/ai/flows/ai-code-assistance */ \"(app-pages-browser)/./src/ai/flows/ai-code-assistance.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n// src/components/AIChatAssistant.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst AIChatAssistant = (param)=>{\n    let { currentCode, projectFiles, onApplyCodeModification, onCreateNewFile, currentFileName } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMentionPopoverOpen, setIsMentionPopoverOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mentionedFilesInfo, setMentionedFilesInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const scrollAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const addMessage = (message)=>{\n        setMessages((prevMessages)=>[\n                ...prevMessages,\n                {\n                    ...message,\n                    id: Date.now().toString() + Math.random().toString()\n                }\n            ]);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIChatAssistant.useEffect\": ()=>{\n            if (scrollAreaRef.current) {\n                const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');\n                if (scrollElement) {\n                    scrollElement.scrollTop = scrollElement.scrollHeight;\n                }\n            }\n        }\n    }[\"AIChatAssistant.useEffect\"], [\n        messages\n    ]);\n    const handleSend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIChatAssistant.useCallback[handleSend]\": async ()=>{\n            let userQuery = input.trim();\n            if (!userQuery && messages.length === 0) {\n                // Default query if input is empty and no messages yet\n                userQuery = \"What can you do?\";\n            } else if (!userQuery && mentionedFilesInfo.length === 0) {\n                // If input is empty but files were mentioned, assume a general query about them\n                userQuery = \"Tell me about the mentioned file(s): \".concat(mentionedFilesInfo.map({\n                    \"AIChatAssistant.useCallback[handleSend]\": (f)=>f.name\n                }[\"AIChatAssistant.useCallback[handleSend]\"]).join(', '));\n            } else if (!userQuery && mentionedFilesInfo.length > 0) {\n                userQuery = \"User mentioned: \".concat(mentionedFilesInfo.map({\n                    \"AIChatAssistant.useCallback[handleSend]\": (f)=>f.name\n                }[\"AIChatAssistant.useCallback[handleSend]\"]).join(', '), \". What should I do with them? (If no specific instruction, provide summary or ask for clarification)\");\n            } else if (!userQuery) {\n                return;\n            }\n            addMessage({\n                role: 'user',\n                text: userQuery\n            });\n            setInput('');\n            const currentMentionedFiles = [\n                ...mentionedFilesInfo\n            ]; // Capture current mentions before clearing\n            setMentionedFilesInfo([]); // Clear after sending\n            setIsLoading(true);\n            try {\n                let codeContext = \"Current open file (\".concat(currentFileName, \"):\\n```\\n\").concat(currentCode, \"\\n```\\n\\n\");\n                const isProjectSummaryRequest = userQuery.toLowerCase().includes(\"summarize my project\") || userQuery.toLowerCase().includes(\"what each file does\") || userQuery.toLowerCase().includes(\"go through my file code\");\n                if (isProjectSummaryRequest) {\n                    codeContext = \"Project files:\\n\";\n                    projectFiles.forEach({\n                        \"AIChatAssistant.useCallback[handleSend]\": (pf)=>{\n                            codeContext += \"File: \".concat(pf.name, \"\\n```\\n\").concat(pf.content, \"\\n```\\n\\n\");\n                        }\n                    }[\"AIChatAssistant.useCallback[handleSend]\"]);\n                } else if (currentMentionedFiles.length > 0) {\n                    codeContext += \"User also mentioned the following files:\\n\";\n                    currentMentionedFiles.forEach({\n                        \"AIChatAssistant.useCallback[handleSend]\": (mf)=>{\n                            const fileData = projectFiles.find({\n                                \"AIChatAssistant.useCallback[handleSend].fileData\": (pf)=>pf.id === mf.id\n                            }[\"AIChatAssistant.useCallback[handleSend].fileData\"]);\n                            if (fileData) {\n                                codeContext += \"File: \".concat(fileData.name, \"\\n```\\n\").concat(fileData.content, \"\\n```\\n\\n\");\n                            }\n                        }\n                    }[\"AIChatAssistant.useCallback[handleSend]\"]);\n                }\n                const aiInput = {\n                    code: codeContext,\n                    taskDescription: \"User is asking for assistance with their code in an IDE. This could be for modifications, explanations, project summaries, or terminal commands.\",\n                    instructions: userQuery\n                };\n                const aiOutput = await (0,_ai_flows_ai_code_assistance__WEBPACK_IMPORTED_MODULE_9__.getCodeAssistance)(aiInput);\n                addMessage({\n                    role: 'assistant',\n                    aiResponses: aiOutput.responses\n                });\n            } catch (error) {\n                console.error('AI Assistant Error:', error);\n                addMessage({\n                    role: 'assistant',\n                    text: 'Sorry, I encountered an error. Please try again.'\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"AIChatAssistant.useCallback[handleSend]\"], [\n        input,\n        messages.length,\n        currentCode,\n        currentFileName,\n        mentionedFilesInfo,\n        projectFiles\n    ]);\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setInput(value);\n        if (value.endsWith('@')) {\n            setIsMentionPopoverOpen(true);\n        } else {\n            setIsMentionPopoverOpen(false);\n        }\n    };\n    const handleFileMentionSelect = (file)=>{\n        var _inputRef_current;\n        setInput((prev)=>prev.slice(0, prev.lastIndexOf('@')) + \"@\".concat(file.name, \" \"));\n        setMentionedFilesInfo((prev)=>{\n            if (!prev.find((mf)=>mf.id === file.id)) {\n                return [\n                    ...prev,\n                    {\n                        id: file.id,\n                        name: file.name\n                    }\n                ];\n            }\n            return prev;\n        });\n        setIsMentionPopoverOpen(false);\n        (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !isLoading && !isMentionPopoverOpen) {\n            handleSend();\n        }\n    };\n    const [copiedStates, setCopiedStates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleCopyToClipboard = (text, id)=>{\n        navigator.clipboard.writeText(text).then(()=>{\n            setCopiedStates((prev)=>({\n                    ...prev,\n                    [id]: true\n                }));\n            setTimeout(()=>setCopiedStates((prev)=>({\n                        ...prev,\n                        [id]: false\n                    })), 2000);\n            toast({\n                title: \"Copied to clipboard!\"\n            });\n        }).catch((err)=>{\n            console.error('Failed to copy text: ', err);\n            toast({\n                title: \"Failed to copy\",\n                variant: \"destructive\"\n            });\n        });\n    };\n    const renderAiResponseItem = (item, messageId, itemIndex)=>{\n        const uniqueId = \"\".concat(messageId, \"-item-\").concat(itemIndex);\n        switch(item.type){\n            case 'codeModification':\n                const handleApplyMod = ()=>{\n                    const fileExists = projectFiles.some((f)=>f.name === item.fileName);\n                    if (fileExists) {\n                        onApplyCodeModification(item.fileName, item.code);\n                    } else {\n                        // If file doesn't exist, treat as new file creation\n                        onCreateNewFile(item.fileName, item.code);\n                        toast({\n                            title: \"File Created & Code Applied!\",\n                            description: 'New file \"'.concat(item.fileName, '\" was created with the suggested code.')\n                        });\n                    }\n                };\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2 p-3 border rounded-md bg-muted/30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-semibold truncate flex-grow mr-2\",\n                                    title: item.fileName,\n                                    children: [\n                                        \"Code Change for: \",\n                                        item.fileName\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    onClick: handleApplyMod,\n                                    className: \"text-xs flex-shrink-0\",\n                                    \"aria-label\": \"Apply code modification to \".concat(item.fileName),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mr-1 h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" Apply\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                            className: \"max-h-60 w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-xs bg-background p-2 rounded overflow-x-auto relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: item.code\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"icon\",\n                                        variant: \"ghost\",\n                                        className: \"absolute top-1 right-1 h-6 w-6\",\n                                        onClick: ()=>handleCopyToClipboard(item.code, \"\".concat(uniqueId, \"-code\")),\n                                        \"aria-label\": \"Copy code to clipboard\",\n                                        children: copiedStates[\"\".concat(uniqueId, \"-code\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 55\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 96\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, undefined),\n                        item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-muted-foreground mt-2 break-words\",\n                            children: item.description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 34\n                        }, undefined)\n                    ]\n                }, uniqueId, true, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 11\n                }, undefined);\n            case 'newFileCreation':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2 p-3 border rounded-md bg-muted/30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-semibold truncate flex-grow mr-2\",\n                                    title: item.fileName,\n                                    children: [\n                                        \"New File: \",\n                                        item.fileName\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    onClick: ()=>onCreateNewFile(item.fileName, item.content),\n                                    className: \"text-xs flex-shrink-0\",\n                                    \"aria-label\": \"Create new file \".concat(item.fileName),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"mr-1 h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" Create File\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                            className: \"max-h-60 w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-xs bg-background p-2 rounded overflow-x-auto relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: item.content\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"icon\",\n                                        variant: \"ghost\",\n                                        className: \"absolute top-1 right-1 h-6 w-6\",\n                                        onClick: ()=>handleCopyToClipboard(item.content, \"\".concat(uniqueId, \"-newfile\")),\n                                        \"aria-label\": \"Copy content to clipboard\",\n                                        children: copiedStates[\"\".concat(uniqueId, \"-newfile\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 58\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 99\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 18\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 14\n                        }, undefined),\n                        item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-muted-foreground mt-2 break-words\",\n                            children: item.description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 34\n                        }, undefined)\n                    ]\n                }, uniqueId, true, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 11\n                }, undefined);\n            case 'textResponse':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm whitespace-pre-wrap break-words max-w-full\",\n                    children: item.content\n                }, uniqueId, false, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 16\n                }, undefined);\n            case 'terminalCommand':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2 p-3 border rounded-md bg-muted/30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-semibold\",\n                            children: \"Terminal Command:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, undefined),\n                        item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-muted-foreground mb-1 break-words\",\n                            children: item.description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 34\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"text-xs bg-background p-2 rounded font-mono relative overflow-x-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    children: item.command\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"icon\",\n                                    variant: \"ghost\",\n                                    className: \"absolute top-1 right-1 h-6 w-6\",\n                                    onClick: ()=>handleCopyToClipboard(item.command, \"\".concat(uniqueId, \"-cmd\")),\n                                    \"aria-label\": \"Copy command to clipboard\",\n                                    children: copiedStates[\"\".concat(uniqueId, \"-cmd\")] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 52\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 93\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 16\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, uniqueId, true, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 11\n                }, undefined);\n            case 'projectSummary':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2 p-3 border rounded-md bg-muted/30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-semibold\",\n                                    children: \"Project Summary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                            className: \"max-h-60 w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm whitespace-pre-wrap break-words mb-2 max-w-full\",\n                                children: item.summary\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 16\n                        }, undefined),\n                        item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-muted-foreground mt-1 break-words\",\n                            children: item.description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 36\n                        }, undefined)\n                    ]\n                }, uniqueId, true, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 13\n                }, undefined);\n            default:\n                // Ensure exhaustive check for all AiResponseItem types\n                const _exhaustiveCheck = item;\n                console.warn(\"Unhandled AI response type:\", _exhaustiveCheck);\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n        className: \"h-full flex flex-col border-0 shadow-none rounded-none\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                className: \"py-3 px-4 border-b bg-muted/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                    className: \"text-base font-semibold flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"mr-2 h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, undefined),\n                        \" AI Assistant\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                className: \"p-0 flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                    className: \"h-full px-2 py-4\",\n                    ref: scrollAreaRef,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)('flex items-start space-x-2', message.role === 'user' ? 'justify-end' : 'justify-start'),\n                                    children: [\n                                        message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-primary flex-shrink-0 mt-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)('p-3 rounded-lg max-w-[calc(100%-2.5rem)]', message.role === 'user' ? 'bg-secondary text-secondary-foreground' : 'bg-card border'),\n                                            children: [\n                                                message.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm whitespace-pre-wrap break-words max-w-full\",\n                                                    children: message.text\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 36\n                                                }, undefined),\n                                                message.aiResponses && message.aiResponses.map((item, index)=>renderAiResponseItem(item, message.id, index))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground flex-shrink-0 mt-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, message.id, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, undefined)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-2 justify-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary flex-shrink-0 mt-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-lg bg-card border flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm ml-2 text-muted-foreground\",\n                                                children: \"Thinking...\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 15\n                            }, undefined),\n                            messages.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-muted-foreground text-sm py-8 px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"break-words\",\n                                        children: \"Ask me anything about your code, or type '@' to reference files.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"break-words\",\n                                        children: [\n                                            'For example: \"Refactor the main function in ',\n                                            currentFileName || 'your_file.js',\n                                            '\", \"Create a new CSS file for styles.\", or \"Summarize my project.\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardFooter, {\n                className: \"p-6 border-t bg-background/95 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex w-full items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_6__.Popover, {\n                            open: isMentionPopoverOpen,\n                            onOpenChange: setIsMentionPopoverOpen,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_6__.PopoverTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        ref: inputRef,\n                                        type: \"text\",\n                                        placeholder: \"Ask AI or type @ to mention files...\",\n                                        value: input,\n                                        onChange: handleInputChange,\n                                        onKeyPress: handleKeyPress,\n                                        disabled: isLoading,\n                                        className: \"flex-1\",\n                                        \"aria-label\": \"Chat input\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_6__.PopoverContent, {\n                                    className: \"w-[300px] p-0\",\n                                    align: \"start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_command__WEBPACK_IMPORTED_MODULE_7__.Command, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_command__WEBPACK_IMPORTED_MODULE_7__.CommandInput, {\n                                                placeholder: \"Search files to mention...\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_command__WEBPACK_IMPORTED_MODULE_7__.CommandList, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_command__WEBPACK_IMPORTED_MODULE_7__.CommandEmpty, {\n                                                        children: \"No files found.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_command__WEBPACK_IMPORTED_MODULE_7__.CommandGroup, {\n                                                        heading: \"Project Files\",\n                                                        children: projectFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_command__WEBPACK_IMPORTED_MODULE_7__.CommandItem, {\n                                                                onSelect: ()=>handleFileMentionSelect(file),\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                                                                lineNumber: 372,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: file.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                                                                lineNumber: 373,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                                                        lineNumber: 371,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    mentionedFilesInfo.some((mf)=>mf.id === file.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                                                        lineNumber: 375,\n                                                                        columnNumber: 78\n                                                                    }, undefined)\n                                                                ]\n                                                            }, file.id, true, {\n                                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"button\",\n                            size: \"icon\",\n                            onClick: handleSend,\n                            disabled: isLoading || !input.trim() && mentionedFilesInfo.length === 0 && messages.length > 0,\n                            \"aria-label\": \"Send message\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 26\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Check_ClipboardCheck_Copy_FileCode_FilePlus2_Library_Loader2_Send_SquarePen_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 73\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\ViceIde\\\\src\\\\components\\\\AIChatAssistant.tsx\",\n        lineNumber: 290,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AIChatAssistant, \"RXicQZW4KdH0Ezi8eJW3S3ps/PY=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = AIChatAssistant;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AIChatAssistant);\nvar _c;\n$RefreshReg$(_c, \"AIChatAssistant\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AIChatAssistant.tsx\n"));

/***/ })

});