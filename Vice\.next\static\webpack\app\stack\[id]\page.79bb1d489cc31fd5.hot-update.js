"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/stack/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/create-project-modal.tsx":
/*!*********************************************************!*\
  !*** ./src/components/project/create-project-modal.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateProjectModal: () => (/* binding */ CreateProjectModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* __next_internal_client_entry_do_not_use__ CreateProjectModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CreateProjectModal(param) {\n    let { open, onOpenChange, stackName, projectTitle = '' } = param;\n    var _startingCommands_stackName;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [projectName, setProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(projectTitle);\n    const [projectPath, setProjectPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\ViceProjects\\\\\".concat(stackName.toLowerCase()));\n    const [projectDescription, setProjectDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCommands, setShowCommands] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLoading, setShowLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentCommand, setCurrentCommand] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [commandProgress, setCommandProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCommandRunning, setIsCommandRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Update path when stackName changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateProjectModal.useEffect\": ()=>{\n            setProjectPath(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\ViceProjects\\\\\".concat(stackName.toLowerCase()));\n        }\n    }[\"CreateProjectModal.useEffect\"], [\n        stackName\n    ]);\n    // Validate and format project name (no spaces, no caps, use hyphens)\n    const validateProjectName = (name)=>{\n        return name.trim().toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n    };\n    // Handle project name input with real-time validation\n    const handleProjectNameChange = (e)=>{\n        const value = e.target.value;\n        setProjectName(value);\n    };\n    // Listen for command progress events from Electron\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateProjectModal.useEffect\": ()=>{\n            if ( true && window.electronAPI) {\n                const handleCommandProgress = {\n                    \"CreateProjectModal.useEffect.handleCommandProgress\": (data)=>{\n                        console.log('[COMMAND PROGRESS] 📨 Received:', data);\n                        if (data.status === 'running') {\n                            setCurrentCommand(data.command);\n                            setIsCommandRunning(true);\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"▶️ Running: \".concat(data.command)\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                        } else if (data.status === 'completed') {\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"✅ Completed: \".concat(data.command)\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            setIsCommandRunning(false);\n                        } else if (data.status === 'error') {\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"❌ Error: \".concat(data.command, \" - \").concat(data.error || 'Unknown error')\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            setIsCommandRunning(false);\n                        } else if (data.status === 'starting-ide') {\n                            setCurrentCommand(\"Starting IDE for \".concat(data.projectName || 'project', \"...\"));\n                            setIsCommandRunning(true);\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"\\uD83D\\uDE80 Starting IDE and returning to dashboard...\"\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            // Close loading screen and return to dashboard\n                            setTimeout({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": ()=>{\n                                    handleLoadingComplete();\n                                }\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"], 3000);\n                        }\n                    }\n                }[\"CreateProjectModal.useEffect.handleCommandProgress\"];\n                // Listen for the IPC event\n                if (window.electronAPI.onCommandProgress) {\n                    window.electronAPI.onCommandProgress(handleCommandProgress);\n                }\n                return ({\n                    \"CreateProjectModal.useEffect\": ()=>{\n                        console.log('[PROJECT] Cleaning up command progress listener');\n                    }\n                })[\"CreateProjectModal.useEffect\"];\n            }\n        }\n    }[\"CreateProjectModal.useEffect\"], []);\n    const handleLoadingComplete = ()=>{\n        setShowLoading(false);\n        setIsCreating(false);\n        setCurrentCommand('');\n        setCommandProgress([]);\n        setIsCommandRunning(false);\n        toast({\n            title: 'Project Ready',\n            description: \"\".concat(projectName, \" has been created and opened in the IDE.\")\n        });\n    };\n    // Starting commands for different stacks\n    const startingCommands = {\n        'Mern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express mongoose cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/models/User.js server/routes/api.js'\n        ],\n        'Pern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express pg cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/db.js server/routes/api.js'\n        ],\n        'NextJS': [\n            'npx create-next-app@latest my-app',\n            'cd my-app',\n            'npm install'\n        ],\n        'Django': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install django djangorestframework',\n            'django-admin startproject myproject .',\n            'python manage.py startapp myapp'\n        ],\n        'Flask': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install flask flask-sqlalchemy flask-cors',\n            'mkdir app',\n            'touch app/__init__.py app/routes.py app/models.py'\n        ],\n        'Bash': [\n            'mkdir my-script',\n            'cd my-script',\n            'touch script.sh',\n            'chmod +x script.sh',\n            'echo \"#!/bin/bash\" > script.sh'\n        ]\n    };\n    const handleCreate = async ()=>{\n        if (!projectName.trim()) {\n            toast({\n                title: 'Project name required',\n                description: 'Please enter a name for your project.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        // Validate and format project name\n        const formattedProjectName = validateProjectName(projectName);\n        if (!formattedProjectName) {\n            toast({\n                title: 'Invalid project name',\n                description: 'Project name must contain at least one letter or number.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        if (formattedProjectName !== projectName.trim().toLowerCase().replace(/\\s+/g, '-')) {\n            toast({\n                title: 'Project name formatted',\n                description: \"Project name changed to: \".concat(formattedProjectName)\n            });\n            setProjectName(formattedProjectName);\n        }\n        setIsCreating(true);\n        setShowLoading(true);\n        try {\n            // Create project using Electron API\n            if (window.electronAPI && window.electronAPI.createProjectFiles) {\n                const result = await window.electronAPI.createProjectFiles({\n                    projectName: formattedProjectName,\n                    projectPath,\n                    stackName,\n                    description: projectDescription\n                });\n                if (result && result.success) {\n                    const finalProjectPath = result.actualPath || projectPath;\n                    // Load project in IDE using Electron (not browser)\n                    if (window.electronAPI && window.electronAPI.loadProjectInIde) {\n                        console.log('[PROJECT] Loading project in IDE via Electron API');\n                        window.electronAPI.loadProjectInIde({\n                            projectName: formattedProjectName,\n                            projectPath: finalProjectPath,\n                            files: [],\n                            stackName\n                        }).catch((error)=>{\n                            console.error('[PROJECT] Error loading project in IDE:', error);\n                        });\n                    }\n                    toast({\n                        title: 'Project Created Successfully',\n                        description: \"\".concat(formattedProjectName, \" has been created and is opening in the IDE.\")\n                    });\n                    // Reset form\n                    setProjectName('');\n                    setProjectDescription('');\n                } else {\n                    throw new Error((result === null || result === void 0 ? void 0 : result.message) || 'Failed to create project');\n                }\n            } else {\n                throw new Error('Electron API not available');\n            }\n        } catch (error) {\n            console.error('[PROJECT] Error creating project:', error);\n            setIsCreating(false);\n            setShowLoading(false);\n            toast({\n                title: 'Failed to create project',\n                description: error instanceof Error ? error.message : 'An error occurred while creating the project.',\n                variant: 'destructive'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"sm:max-w-[550px] md:max-w-[600px] rounded-xl overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-20 w-40\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/logo.png\",\n                            alt: \"VICE Logo\",\n                            fill: true,\n                            style: {\n                                objectFit: 'contain'\n                            },\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-[-100%]' : 'translate-x-0'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                    className: \"mb-[5] pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                            className: \"text-center\",\n                                            children: \"Create New Project\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                            className: \"text-center text-sm text-muted-foreground\",\n                                            children: [\n                                                \"Create a new \",\n                                                stackName,\n                                                \" project. Fill in the details below to get started.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4 py-4 px-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"name\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"name\",\n                                                    value: projectName,\n                                                    onChange: handleProjectNameChange,\n                                                    className: \"w-full\",\n                                                    placeholder: \"my-awesome-project\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: [\n                                                        \"No spaces or caps allowed. Use hyphens for multi-word projects.\",\n                                                        projectName && projectName !== validateProjectName(projectName) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-orange-600 ml-1\",\n                                                            children: [\n                                                                \"→ Will be: \",\n                                                                validateProjectName(projectName)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"path\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Path\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"path\",\n                                                    value: projectPath,\n                                                    onChange: (e)=>setProjectPath(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"Documents/ViceProjects/\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"description\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                    id: \"description\",\n                                                    value: projectDescription,\n                                                    onChange: (e)=>setProjectDescription(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"A brief description of your project\",\n                                                    rows: 3\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-2 mb-4 ml-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-xs text-muted-foreground flex items-center gap-1 p-0 h-auto\",\n                                                onClick: ()=>setShowCommands(true),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3.5 w-3.5 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"See Starting Commands\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-3.5 w-3.5 ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                            className: \"flex justify-end items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>onOpenChange(false),\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        onClick: handleCreate,\n                                                        disabled: isCreating,\n                                                        children: isCreating ? 'Creating...' : 'Create Project'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-0' : 'translate-x-[100%]'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 ml-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"pl-0 flex items-center gap-1\",\n                                        onClick: ()=>setShowCommands(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Project Form\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                            children: [\n                                                \"Starting Commands for \",\n                                                stackName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                            children: [\n                                                \"Use these commands to set up your \",\n                                                stackName,\n                                                \" project manually.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-4 px-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-muted p-4 rounded-md font-mono text-xs sm:text-sm overflow-auto max-h-[250px]\",\n                                        children: ((_startingCommands_stackName = startingCommands[stackName]) === null || _startingCommands_stackName === void 0 ? void 0 : _startingCommands_stackName.map((command, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground mr-2 flex-shrink-0\",\n                                                        children: \"$\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"break-all\",\n                                                        children: command\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 19\n                                            }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                \"No commands available for \",\n                                                stackName,\n                                                \".\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>onOpenChange(false),\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n            lineNumber: 248,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateProjectModal, \"+Wy90OJPGbFq7Wx8RMMP1TbpegU=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = CreateProjectModal;\nvar _c;\n$RefreshReg$(_c, \"CreateProjectModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/create-project-modal.tsx\n"));

/***/ })

});