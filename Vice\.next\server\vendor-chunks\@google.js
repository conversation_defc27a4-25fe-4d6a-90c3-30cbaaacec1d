"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@google";
exports.ids = ["vendor-chunks/@google"];
exports.modules = {

/***/ "(action-browser)/./node_modules/@google/generative-ai/dist/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/@google/generative-ai/dist/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\n/**\n * Contains the list of OpenAPI data types\n * as defined by https://swagger.io/docs/specification/data-models/data-types/\n * @public\n */\nexports.SchemaType = void 0;\n(function (SchemaType) {\n    /** String type. */\n    SchemaType[\"STRING\"] = \"string\";\n    /** Number type. */\n    SchemaType[\"NUMBER\"] = \"number\";\n    /** Integer type. */\n    SchemaType[\"INTEGER\"] = \"integer\";\n    /** Boolean type. */\n    SchemaType[\"BOOLEAN\"] = \"boolean\";\n    /** Array type. */\n    SchemaType[\"ARRAY\"] = \"array\";\n    /** Object type. */\n    SchemaType[\"OBJECT\"] = \"object\";\n})(exports.SchemaType || (exports.SchemaType = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @public\n */\nexports.ExecutableCodeLanguage = void 0;\n(function (ExecutableCodeLanguage) {\n    ExecutableCodeLanguage[\"LANGUAGE_UNSPECIFIED\"] = \"language_unspecified\";\n    ExecutableCodeLanguage[\"PYTHON\"] = \"python\";\n})(exports.ExecutableCodeLanguage || (exports.ExecutableCodeLanguage = {}));\n/**\n * Possible outcomes of code execution.\n * @public\n */\nexports.Outcome = void 0;\n(function (Outcome) {\n    /**\n     * Unspecified status. This value should not be used.\n     */\n    Outcome[\"OUTCOME_UNSPECIFIED\"] = \"outcome_unspecified\";\n    /**\n     * Code execution completed successfully.\n     */\n    Outcome[\"OUTCOME_OK\"] = \"outcome_ok\";\n    /**\n     * Code execution finished but with a failure. `stderr` should contain the\n     * reason.\n     */\n    Outcome[\"OUTCOME_FAILED\"] = \"outcome_failed\";\n    /**\n     * Code execution ran for too long, and was cancelled. There may or may not\n     * be a partial output present.\n     */\n    Outcome[\"OUTCOME_DEADLINE_EXCEEDED\"] = \"outcome_deadline_exceeded\";\n})(exports.Outcome || (exports.Outcome = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Possible roles.\n * @public\n */\nconst POSSIBLE_ROLES = [\"user\", \"model\", \"function\", \"system\"];\n/**\n * Harm categories that would cause prompts or candidates to be blocked.\n * @public\n */\nexports.HarmCategory = void 0;\n(function (HarmCategory) {\n    HarmCategory[\"HARM_CATEGORY_UNSPECIFIED\"] = \"HARM_CATEGORY_UNSPECIFIED\";\n    HarmCategory[\"HARM_CATEGORY_HATE_SPEECH\"] = \"HARM_CATEGORY_HATE_SPEECH\";\n    HarmCategory[\"HARM_CATEGORY_SEXUALLY_EXPLICIT\"] = \"HARM_CATEGORY_SEXUALLY_EXPLICIT\";\n    HarmCategory[\"HARM_CATEGORY_HARASSMENT\"] = \"HARM_CATEGORY_HARASSMENT\";\n    HarmCategory[\"HARM_CATEGORY_DANGEROUS_CONTENT\"] = \"HARM_CATEGORY_DANGEROUS_CONTENT\";\n})(exports.HarmCategory || (exports.HarmCategory = {}));\n/**\n * Threshold above which a prompt or candidate will be blocked.\n * @public\n */\nexports.HarmBlockThreshold = void 0;\n(function (HarmBlockThreshold) {\n    // Threshold is unspecified.\n    HarmBlockThreshold[\"HARM_BLOCK_THRESHOLD_UNSPECIFIED\"] = \"HARM_BLOCK_THRESHOLD_UNSPECIFIED\";\n    // Content with NEGLIGIBLE will be allowed.\n    HarmBlockThreshold[\"BLOCK_LOW_AND_ABOVE\"] = \"BLOCK_LOW_AND_ABOVE\";\n    // Content with NEGLIGIBLE and LOW will be allowed.\n    HarmBlockThreshold[\"BLOCK_MEDIUM_AND_ABOVE\"] = \"BLOCK_MEDIUM_AND_ABOVE\";\n    // Content with NEGLIGIBLE, LOW, and MEDIUM will be allowed.\n    HarmBlockThreshold[\"BLOCK_ONLY_HIGH\"] = \"BLOCK_ONLY_HIGH\";\n    // All content will be allowed.\n    HarmBlockThreshold[\"BLOCK_NONE\"] = \"BLOCK_NONE\";\n})(exports.HarmBlockThreshold || (exports.HarmBlockThreshold = {}));\n/**\n * Probability that a prompt or candidate matches a harm category.\n * @public\n */\nexports.HarmProbability = void 0;\n(function (HarmProbability) {\n    // Probability is unspecified.\n    HarmProbability[\"HARM_PROBABILITY_UNSPECIFIED\"] = \"HARM_PROBABILITY_UNSPECIFIED\";\n    // Content has a negligible chance of being unsafe.\n    HarmProbability[\"NEGLIGIBLE\"] = \"NEGLIGIBLE\";\n    // Content has a low chance of being unsafe.\n    HarmProbability[\"LOW\"] = \"LOW\";\n    // Content has a medium chance of being unsafe.\n    HarmProbability[\"MEDIUM\"] = \"MEDIUM\";\n    // Content has a high chance of being unsafe.\n    HarmProbability[\"HIGH\"] = \"HIGH\";\n})(exports.HarmProbability || (exports.HarmProbability = {}));\n/**\n * Reason that a prompt was blocked.\n * @public\n */\nexports.BlockReason = void 0;\n(function (BlockReason) {\n    // A blocked reason was not specified.\n    BlockReason[\"BLOCKED_REASON_UNSPECIFIED\"] = \"BLOCKED_REASON_UNSPECIFIED\";\n    // Content was blocked by safety settings.\n    BlockReason[\"SAFETY\"] = \"SAFETY\";\n    // Content was blocked, but the reason is uncategorized.\n    BlockReason[\"OTHER\"] = \"OTHER\";\n})(exports.BlockReason || (exports.BlockReason = {}));\n/**\n * Reason that a candidate finished.\n * @public\n */\nexports.FinishReason = void 0;\n(function (FinishReason) {\n    // Default value. This value is unused.\n    FinishReason[\"FINISH_REASON_UNSPECIFIED\"] = \"FINISH_REASON_UNSPECIFIED\";\n    // Natural stop point of the model or provided stop sequence.\n    FinishReason[\"STOP\"] = \"STOP\";\n    // The maximum number of tokens as specified in the request was reached.\n    FinishReason[\"MAX_TOKENS\"] = \"MAX_TOKENS\";\n    // The candidate content was flagged for safety reasons.\n    FinishReason[\"SAFETY\"] = \"SAFETY\";\n    // The candidate content was flagged for recitation reasons.\n    FinishReason[\"RECITATION\"] = \"RECITATION\";\n    // The candidate content was flagged for using an unsupported language.\n    FinishReason[\"LANGUAGE\"] = \"LANGUAGE\";\n    // Unknown reason.\n    FinishReason[\"OTHER\"] = \"OTHER\";\n})(exports.FinishReason || (exports.FinishReason = {}));\n/**\n * Task type for embedding content.\n * @public\n */\nexports.TaskType = void 0;\n(function (TaskType) {\n    TaskType[\"TASK_TYPE_UNSPECIFIED\"] = \"TASK_TYPE_UNSPECIFIED\";\n    TaskType[\"RETRIEVAL_QUERY\"] = \"RETRIEVAL_QUERY\";\n    TaskType[\"RETRIEVAL_DOCUMENT\"] = \"RETRIEVAL_DOCUMENT\";\n    TaskType[\"SEMANTIC_SIMILARITY\"] = \"SEMANTIC_SIMILARITY\";\n    TaskType[\"CLASSIFICATION\"] = \"CLASSIFICATION\";\n    TaskType[\"CLUSTERING\"] = \"CLUSTERING\";\n})(exports.TaskType || (exports.TaskType = {}));\n/**\n * @public\n */\nexports.FunctionCallingMode = void 0;\n(function (FunctionCallingMode) {\n    // Unspecified function calling mode. This value should not be used.\n    FunctionCallingMode[\"MODE_UNSPECIFIED\"] = \"MODE_UNSPECIFIED\";\n    // Default model behavior, model decides to predict either a function call\n    // or a natural language repspose.\n    FunctionCallingMode[\"AUTO\"] = \"AUTO\";\n    // Model is constrained to always predicting a function call only.\n    // If \"allowed_function_names\" are set, the predicted function call will be\n    // limited to any one of \"allowed_function_names\", else the predicted\n    // function call will be any one of the provided \"function_declarations\".\n    FunctionCallingMode[\"ANY\"] = \"ANY\";\n    // Model will not predict any function call. Model behavior is same as when\n    // not passing any function declarations.\n    FunctionCallingMode[\"NONE\"] = \"NONE\";\n})(exports.FunctionCallingMode || (exports.FunctionCallingMode = {}));\n/**\n * The mode of the predictor to be used in dynamic retrieval.\n * @public\n */\nexports.DynamicRetrievalMode = void 0;\n(function (DynamicRetrievalMode) {\n    // Unspecified function calling mode. This value should not be used.\n    DynamicRetrievalMode[\"MODE_UNSPECIFIED\"] = \"MODE_UNSPECIFIED\";\n    // Run retrieval only when system decides it is necessary.\n    DynamicRetrievalMode[\"MODE_DYNAMIC\"] = \"MODE_DYNAMIC\";\n})(exports.DynamicRetrievalMode || (exports.DynamicRetrievalMode = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Basic error type for this SDK.\n * @public\n */\nclass GoogleGenerativeAIError extends Error {\n    constructor(message) {\n        super(`[GoogleGenerativeAI Error]: ${message}`);\n    }\n}\n/**\n * Errors in the contents of a response from the model. This includes parsing\n * errors, or responses including a safety block reason.\n * @public\n */\nclass GoogleGenerativeAIResponseError extends GoogleGenerativeAIError {\n    constructor(message, response) {\n        super(message);\n        this.response = response;\n    }\n}\n/**\n * Error class covering HTTP errors when calling the server. Includes HTTP\n * status, statusText, and optional details, if provided in the server response.\n * @public\n */\nclass GoogleGenerativeAIFetchError extends GoogleGenerativeAIError {\n    constructor(message, status, statusText, errorDetails) {\n        super(message);\n        this.status = status;\n        this.statusText = statusText;\n        this.errorDetails = errorDetails;\n    }\n}\n/**\n * Errors in the contents of a request originating from user input.\n * @public\n */\nclass GoogleGenerativeAIRequestInputError extends GoogleGenerativeAIError {\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_BASE_URL = \"https://generativelanguage.googleapis.com\";\nconst DEFAULT_API_VERSION = \"v1beta\";\n/**\n * We can't `require` package.json if this runs on web. We will use rollup to\n * swap in the version number here at build time.\n */\nconst PACKAGE_VERSION = \"0.21.0\";\nconst PACKAGE_LOG_HEADER = \"genai-js\";\nvar Task;\n(function (Task) {\n    Task[\"GENERATE_CONTENT\"] = \"generateContent\";\n    Task[\"STREAM_GENERATE_CONTENT\"] = \"streamGenerateContent\";\n    Task[\"COUNT_TOKENS\"] = \"countTokens\";\n    Task[\"EMBED_CONTENT\"] = \"embedContent\";\n    Task[\"BATCH_EMBED_CONTENTS\"] = \"batchEmbedContents\";\n})(Task || (Task = {}));\nclass RequestUrl {\n    constructor(model, task, apiKey, stream, requestOptions) {\n        this.model = model;\n        this.task = task;\n        this.apiKey = apiKey;\n        this.stream = stream;\n        this.requestOptions = requestOptions;\n    }\n    toString() {\n        var _a, _b;\n        const apiVersion = ((_a = this.requestOptions) === null || _a === void 0 ? void 0 : _a.apiVersion) || DEFAULT_API_VERSION;\n        const baseUrl = ((_b = this.requestOptions) === null || _b === void 0 ? void 0 : _b.baseUrl) || DEFAULT_BASE_URL;\n        let url = `${baseUrl}/${apiVersion}/${this.model}:${this.task}`;\n        if (this.stream) {\n            url += \"?alt=sse\";\n        }\n        return url;\n    }\n}\n/**\n * Simple, but may become more complex if we add more versions to log.\n */\nfunction getClientHeaders(requestOptions) {\n    const clientHeaders = [];\n    if (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiClient) {\n        clientHeaders.push(requestOptions.apiClient);\n    }\n    clientHeaders.push(`${PACKAGE_LOG_HEADER}/${PACKAGE_VERSION}`);\n    return clientHeaders.join(\" \");\n}\nasync function getHeaders(url) {\n    var _a;\n    const headers = new Headers();\n    headers.append(\"Content-Type\", \"application/json\");\n    headers.append(\"x-goog-api-client\", getClientHeaders(url.requestOptions));\n    headers.append(\"x-goog-api-key\", url.apiKey);\n    let customHeaders = (_a = url.requestOptions) === null || _a === void 0 ? void 0 : _a.customHeaders;\n    if (customHeaders) {\n        if (!(customHeaders instanceof Headers)) {\n            try {\n                customHeaders = new Headers(customHeaders);\n            }\n            catch (e) {\n                throw new GoogleGenerativeAIRequestInputError(`unable to convert customHeaders value ${JSON.stringify(customHeaders)} to Headers: ${e.message}`);\n            }\n        }\n        for (const [headerName, headerValue] of customHeaders.entries()) {\n            if (headerName === \"x-goog-api-key\") {\n                throw new GoogleGenerativeAIRequestInputError(`Cannot set reserved header name ${headerName}`);\n            }\n            else if (headerName === \"x-goog-api-client\") {\n                throw new GoogleGenerativeAIRequestInputError(`Header name ${headerName} can only be set using the apiClient field`);\n            }\n            headers.append(headerName, headerValue);\n        }\n    }\n    return headers;\n}\nasync function constructModelRequest(model, task, apiKey, stream, body, requestOptions) {\n    const url = new RequestUrl(model, task, apiKey, stream, requestOptions);\n    return {\n        url: url.toString(),\n        fetchOptions: Object.assign(Object.assign({}, buildFetchOptions(requestOptions)), { method: \"POST\", headers: await getHeaders(url), body }),\n    };\n}\nasync function makeModelRequest(model, task, apiKey, stream, body, requestOptions = {}, \n// Allows this to be stubbed for tests\nfetchFn = fetch) {\n    const { url, fetchOptions } = await constructModelRequest(model, task, apiKey, stream, body, requestOptions);\n    return makeRequest(url, fetchOptions, fetchFn);\n}\nasync function makeRequest(url, fetchOptions, fetchFn = fetch) {\n    let response;\n    try {\n        response = await fetchFn(url, fetchOptions);\n    }\n    catch (e) {\n        handleResponseError(e, url);\n    }\n    if (!response.ok) {\n        await handleResponseNotOk(response, url);\n    }\n    return response;\n}\nfunction handleResponseError(e, url) {\n    let err = e;\n    if (!(e instanceof GoogleGenerativeAIFetchError ||\n        e instanceof GoogleGenerativeAIRequestInputError)) {\n        err = new GoogleGenerativeAIError(`Error fetching from ${url.toString()}: ${e.message}`);\n        err.stack = e.stack;\n    }\n    throw err;\n}\nasync function handleResponseNotOk(response, url) {\n    let message = \"\";\n    let errorDetails;\n    try {\n        const json = await response.json();\n        message = json.error.message;\n        if (json.error.details) {\n            message += ` ${JSON.stringify(json.error.details)}`;\n            errorDetails = json.error.details;\n        }\n    }\n    catch (e) {\n        // ignored\n    }\n    throw new GoogleGenerativeAIFetchError(`Error fetching from ${url.toString()}: [${response.status} ${response.statusText}] ${message}`, response.status, response.statusText, errorDetails);\n}\n/**\n * Generates the request options to be passed to the fetch API.\n * @param requestOptions - The user-defined request options.\n * @returns The generated request options.\n */\nfunction buildFetchOptions(requestOptions) {\n    const fetchOptions = {};\n    if ((requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.signal) !== undefined || (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeout) >= 0) {\n        const controller = new AbortController();\n        if ((requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeout) >= 0) {\n            setTimeout(() => controller.abort(), requestOptions.timeout);\n        }\n        if (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.signal) {\n            requestOptions.signal.addEventListener(\"abort\", () => {\n                controller.abort();\n            });\n        }\n        fetchOptions.signal = controller.signal;\n    }\n    return fetchOptions;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Adds convenience helper methods to a response object, including stream\n * chunks (as long as each chunk is a complete GenerateContentResponse JSON).\n */\nfunction addHelpers(response) {\n    response.text = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning text from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            return getText(response);\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Text not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return \"\";\n    };\n    /**\n     * TODO: remove at next major version\n     */\n    response.functionCall = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning function calls from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            console.warn(`response.functionCall() is deprecated. ` +\n                `Use response.functionCalls() instead.`);\n            return getFunctionCalls(response)[0];\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Function call not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return undefined;\n    };\n    response.functionCalls = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning function calls from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            return getFunctionCalls(response);\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Function call not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return undefined;\n    };\n    return response;\n}\n/**\n * Returns all text found in all parts of first candidate.\n */\nfunction getText(response) {\n    var _a, _b, _c, _d;\n    const textStrings = [];\n    if ((_b = (_a = response.candidates) === null || _a === void 0 ? void 0 : _a[0].content) === null || _b === void 0 ? void 0 : _b.parts) {\n        for (const part of (_d = (_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0].content) === null || _d === void 0 ? void 0 : _d.parts) {\n            if (part.text) {\n                textStrings.push(part.text);\n            }\n            if (part.executableCode) {\n                textStrings.push(\"\\n```\" +\n                    part.executableCode.language +\n                    \"\\n\" +\n                    part.executableCode.code +\n                    \"\\n```\\n\");\n            }\n            if (part.codeExecutionResult) {\n                textStrings.push(\"\\n```\\n\" + part.codeExecutionResult.output + \"\\n```\\n\");\n            }\n        }\n    }\n    if (textStrings.length > 0) {\n        return textStrings.join(\"\");\n    }\n    else {\n        return \"\";\n    }\n}\n/**\n * Returns functionCall of first candidate.\n */\nfunction getFunctionCalls(response) {\n    var _a, _b, _c, _d;\n    const functionCalls = [];\n    if ((_b = (_a = response.candidates) === null || _a === void 0 ? void 0 : _a[0].content) === null || _b === void 0 ? void 0 : _b.parts) {\n        for (const part of (_d = (_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0].content) === null || _d === void 0 ? void 0 : _d.parts) {\n            if (part.functionCall) {\n                functionCalls.push(part.functionCall);\n            }\n        }\n    }\n    if (functionCalls.length > 0) {\n        return functionCalls;\n    }\n    else {\n        return undefined;\n    }\n}\nconst badFinishReasons = [\n    exports.FinishReason.RECITATION,\n    exports.FinishReason.SAFETY,\n    exports.FinishReason.LANGUAGE,\n];\nfunction hadBadFinishReason(candidate) {\n    return (!!candidate.finishReason &&\n        badFinishReasons.includes(candidate.finishReason));\n}\nfunction formatBlockErrorMessage(response) {\n    var _a, _b, _c;\n    let message = \"\";\n    if ((!response.candidates || response.candidates.length === 0) &&\n        response.promptFeedback) {\n        message += \"Response was blocked\";\n        if ((_a = response.promptFeedback) === null || _a === void 0 ? void 0 : _a.blockReason) {\n            message += ` due to ${response.promptFeedback.blockReason}`;\n        }\n        if ((_b = response.promptFeedback) === null || _b === void 0 ? void 0 : _b.blockReasonMessage) {\n            message += `: ${response.promptFeedback.blockReasonMessage}`;\n        }\n    }\n    else if ((_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0]) {\n        const firstCandidate = response.candidates[0];\n        if (hadBadFinishReason(firstCandidate)) {\n            message += `Candidate was blocked due to ${firstCandidate.finishReason}`;\n            if (firstCandidate.finishMessage) {\n                message += `: ${firstCandidate.finishMessage}`;\n            }\n        }\n    }\n    return message;\n}\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\n\r\nfunction __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nfunction __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst responseLineRE = /^data\\: (.*)(?:\\n\\n|\\r\\r|\\r\\n\\r\\n)/;\n/**\n * Process a response.body stream from the backend and return an\n * iterator that provides one complete GenerateContentResponse at a time\n * and a promise that resolves with a single aggregated\n * GenerateContentResponse.\n *\n * @param response - Response from a fetch call\n */\nfunction processStream(response) {\n    const inputStream = response.body.pipeThrough(new TextDecoderStream(\"utf8\", { fatal: true }));\n    const responseStream = getResponseStream(inputStream);\n    const [stream1, stream2] = responseStream.tee();\n    return {\n        stream: generateResponseSequence(stream1),\n        response: getResponsePromise(stream2),\n    };\n}\nasync function getResponsePromise(stream) {\n    const allResponses = [];\n    const reader = stream.getReader();\n    while (true) {\n        const { done, value } = await reader.read();\n        if (done) {\n            return addHelpers(aggregateResponses(allResponses));\n        }\n        allResponses.push(value);\n    }\n}\nfunction generateResponseSequence(stream) {\n    return __asyncGenerator(this, arguments, function* generateResponseSequence_1() {\n        const reader = stream.getReader();\n        while (true) {\n            const { value, done } = yield __await(reader.read());\n            if (done) {\n                break;\n            }\n            yield yield __await(addHelpers(value));\n        }\n    });\n}\n/**\n * Reads a raw stream from the fetch response and join incomplete\n * chunks, returning a new stream that provides a single complete\n * GenerateContentResponse in each iteration.\n */\nfunction getResponseStream(inputStream) {\n    const reader = inputStream.getReader();\n    const stream = new ReadableStream({\n        start(controller) {\n            let currentText = \"\";\n            return pump();\n            function pump() {\n                return reader.read().then(({ value, done }) => {\n                    if (done) {\n                        if (currentText.trim()) {\n                            controller.error(new GoogleGenerativeAIError(\"Failed to parse stream\"));\n                            return;\n                        }\n                        controller.close();\n                        return;\n                    }\n                    currentText += value;\n                    let match = currentText.match(responseLineRE);\n                    let parsedResponse;\n                    while (match) {\n                        try {\n                            parsedResponse = JSON.parse(match[1]);\n                        }\n                        catch (e) {\n                            controller.error(new GoogleGenerativeAIError(`Error parsing JSON response: \"${match[1]}\"`));\n                            return;\n                        }\n                        controller.enqueue(parsedResponse);\n                        currentText = currentText.substring(match[0].length);\n                        match = currentText.match(responseLineRE);\n                    }\n                    return pump();\n                });\n            }\n        },\n    });\n    return stream;\n}\n/**\n * Aggregates an array of `GenerateContentResponse`s into a single\n * GenerateContentResponse.\n */\nfunction aggregateResponses(responses) {\n    const lastResponse = responses[responses.length - 1];\n    const aggregatedResponse = {\n        promptFeedback: lastResponse === null || lastResponse === void 0 ? void 0 : lastResponse.promptFeedback,\n    };\n    for (const response of responses) {\n        if (response.candidates) {\n            for (const candidate of response.candidates) {\n                const i = candidate.index;\n                if (!aggregatedResponse.candidates) {\n                    aggregatedResponse.candidates = [];\n                }\n                if (!aggregatedResponse.candidates[i]) {\n                    aggregatedResponse.candidates[i] = {\n                        index: candidate.index,\n                    };\n                }\n                // Keep overwriting, the last one will be final\n                aggregatedResponse.candidates[i].citationMetadata =\n                    candidate.citationMetadata;\n                aggregatedResponse.candidates[i].groundingMetadata =\n                    candidate.groundingMetadata;\n                aggregatedResponse.candidates[i].finishReason = candidate.finishReason;\n                aggregatedResponse.candidates[i].finishMessage =\n                    candidate.finishMessage;\n                aggregatedResponse.candidates[i].safetyRatings =\n                    candidate.safetyRatings;\n                /**\n                 * Candidates should always have content and parts, but this handles\n                 * possible malformed responses.\n                 */\n                if (candidate.content && candidate.content.parts) {\n                    if (!aggregatedResponse.candidates[i].content) {\n                        aggregatedResponse.candidates[i].content = {\n                            role: candidate.content.role || \"user\",\n                            parts: [],\n                        };\n                    }\n                    const newPart = {};\n                    for (const part of candidate.content.parts) {\n                        if (part.text) {\n                            newPart.text = part.text;\n                        }\n                        if (part.functionCall) {\n                            newPart.functionCall = part.functionCall;\n                        }\n                        if (part.executableCode) {\n                            newPart.executableCode = part.executableCode;\n                        }\n                        if (part.codeExecutionResult) {\n                            newPart.codeExecutionResult = part.codeExecutionResult;\n                        }\n                        if (Object.keys(newPart).length === 0) {\n                            newPart.text = \"\";\n                        }\n                        aggregatedResponse.candidates[i].content.parts.push(newPart);\n                    }\n                }\n            }\n        }\n        if (response.usageMetadata) {\n            aggregatedResponse.usageMetadata = response.usageMetadata;\n        }\n    }\n    return aggregatedResponse;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function generateContentStream(apiKey, model, params, requestOptions) {\n    const response = await makeModelRequest(model, Task.STREAM_GENERATE_CONTENT, apiKey, \n    /* stream */ true, JSON.stringify(params), requestOptions);\n    return processStream(response);\n}\nasync function generateContent(apiKey, model, params, requestOptions) {\n    const response = await makeModelRequest(model, Task.GENERATE_CONTENT, apiKey, \n    /* stream */ false, JSON.stringify(params), requestOptions);\n    const responseJson = await response.json();\n    const enhancedResponse = addHelpers(responseJson);\n    return {\n        response: enhancedResponse,\n    };\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction formatSystemInstruction(input) {\n    // null or undefined\n    if (input == null) {\n        return undefined;\n    }\n    else if (typeof input === \"string\") {\n        return { role: \"system\", parts: [{ text: input }] };\n    }\n    else if (input.text) {\n        return { role: \"system\", parts: [input] };\n    }\n    else if (input.parts) {\n        if (!input.role) {\n            return { role: \"system\", parts: input.parts };\n        }\n        else {\n            return input;\n        }\n    }\n}\nfunction formatNewContent(request) {\n    let newParts = [];\n    if (typeof request === \"string\") {\n        newParts = [{ text: request }];\n    }\n    else {\n        for (const partOrString of request) {\n            if (typeof partOrString === \"string\") {\n                newParts.push({ text: partOrString });\n            }\n            else {\n                newParts.push(partOrString);\n            }\n        }\n    }\n    return assignRoleToPartsAndValidateSendMessageRequest(newParts);\n}\n/**\n * When multiple Part types (i.e. FunctionResponsePart and TextPart) are\n * passed in a single Part array, we may need to assign different roles to each\n * part. Currently only FunctionResponsePart requires a role other than 'user'.\n * @private\n * @param parts Array of parts to pass to the model\n * @returns Array of content items\n */\nfunction assignRoleToPartsAndValidateSendMessageRequest(parts) {\n    const userContent = { role: \"user\", parts: [] };\n    const functionContent = { role: \"function\", parts: [] };\n    let hasUserContent = false;\n    let hasFunctionContent = false;\n    for (const part of parts) {\n        if (\"functionResponse\" in part) {\n            functionContent.parts.push(part);\n            hasFunctionContent = true;\n        }\n        else {\n            userContent.parts.push(part);\n            hasUserContent = true;\n        }\n    }\n    if (hasUserContent && hasFunctionContent) {\n        throw new GoogleGenerativeAIError(\"Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.\");\n    }\n    if (!hasUserContent && !hasFunctionContent) {\n        throw new GoogleGenerativeAIError(\"No content is provided for sending chat message.\");\n    }\n    if (hasUserContent) {\n        return userContent;\n    }\n    return functionContent;\n}\nfunction formatCountTokensInput(params, modelParams) {\n    var _a;\n    let formattedGenerateContentRequest = {\n        model: modelParams === null || modelParams === void 0 ? void 0 : modelParams.model,\n        generationConfig: modelParams === null || modelParams === void 0 ? void 0 : modelParams.generationConfig,\n        safetySettings: modelParams === null || modelParams === void 0 ? void 0 : modelParams.safetySettings,\n        tools: modelParams === null || modelParams === void 0 ? void 0 : modelParams.tools,\n        toolConfig: modelParams === null || modelParams === void 0 ? void 0 : modelParams.toolConfig,\n        systemInstruction: modelParams === null || modelParams === void 0 ? void 0 : modelParams.systemInstruction,\n        cachedContent: (_a = modelParams === null || modelParams === void 0 ? void 0 : modelParams.cachedContent) === null || _a === void 0 ? void 0 : _a.name,\n        contents: [],\n    };\n    const containsGenerateContentRequest = params.generateContentRequest != null;\n    if (params.contents) {\n        if (containsGenerateContentRequest) {\n            throw new GoogleGenerativeAIRequestInputError(\"CountTokensRequest must have one of contents or generateContentRequest, not both.\");\n        }\n        formattedGenerateContentRequest.contents = params.contents;\n    }\n    else if (containsGenerateContentRequest) {\n        formattedGenerateContentRequest = Object.assign(Object.assign({}, formattedGenerateContentRequest), params.generateContentRequest);\n    }\n    else {\n        // Array or string\n        const content = formatNewContent(params);\n        formattedGenerateContentRequest.contents = [content];\n    }\n    return { generateContentRequest: formattedGenerateContentRequest };\n}\nfunction formatGenerateContentInput(params) {\n    let formattedRequest;\n    if (params.contents) {\n        formattedRequest = params;\n    }\n    else {\n        // Array or string\n        const content = formatNewContent(params);\n        formattedRequest = { contents: [content] };\n    }\n    if (params.systemInstruction) {\n        formattedRequest.systemInstruction = formatSystemInstruction(params.systemInstruction);\n    }\n    return formattedRequest;\n}\nfunction formatEmbedContentInput(params) {\n    if (typeof params === \"string\" || Array.isArray(params)) {\n        const content = formatNewContent(params);\n        return { content };\n    }\n    return params;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// https://ai.google.dev/api/rest/v1beta/Content#part\nconst VALID_PART_FIELDS = [\n    \"text\",\n    \"inlineData\",\n    \"functionCall\",\n    \"functionResponse\",\n    \"executableCode\",\n    \"codeExecutionResult\",\n];\nconst VALID_PARTS_PER_ROLE = {\n    user: [\"text\", \"inlineData\"],\n    function: [\"functionResponse\"],\n    model: [\"text\", \"functionCall\", \"executableCode\", \"codeExecutionResult\"],\n    // System instructions shouldn't be in history anyway.\n    system: [\"text\"],\n};\nfunction validateChatHistory(history) {\n    let prevContent = false;\n    for (const currContent of history) {\n        const { role, parts } = currContent;\n        if (!prevContent && role !== \"user\") {\n            throw new GoogleGenerativeAIError(`First content should be with role 'user', got ${role}`);\n        }\n        if (!POSSIBLE_ROLES.includes(role)) {\n            throw new GoogleGenerativeAIError(`Each item should include role field. Got ${role} but valid roles are: ${JSON.stringify(POSSIBLE_ROLES)}`);\n        }\n        if (!Array.isArray(parts)) {\n            throw new GoogleGenerativeAIError(\"Content should have 'parts' property with an array of Parts\");\n        }\n        if (parts.length === 0) {\n            throw new GoogleGenerativeAIError(\"Each Content should have at least one part\");\n        }\n        const countFields = {\n            text: 0,\n            inlineData: 0,\n            functionCall: 0,\n            functionResponse: 0,\n            fileData: 0,\n            executableCode: 0,\n            codeExecutionResult: 0,\n        };\n        for (const part of parts) {\n            for (const key of VALID_PART_FIELDS) {\n                if (key in part) {\n                    countFields[key] += 1;\n                }\n            }\n        }\n        const validParts = VALID_PARTS_PER_ROLE[role];\n        for (const key of VALID_PART_FIELDS) {\n            if (!validParts.includes(key) && countFields[key] > 0) {\n                throw new GoogleGenerativeAIError(`Content with role '${role}' can't contain '${key}' part`);\n            }\n        }\n        prevContent = true;\n    }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Do not log a message for this error.\n */\nconst SILENT_ERROR = \"SILENT_ERROR\";\n/**\n * ChatSession class that enables sending chat messages and stores\n * history of sent and received messages so far.\n *\n * @public\n */\nclass ChatSession {\n    constructor(apiKey, model, params, _requestOptions = {}) {\n        this.model = model;\n        this.params = params;\n        this._requestOptions = _requestOptions;\n        this._history = [];\n        this._sendPromise = Promise.resolve();\n        this._apiKey = apiKey;\n        if (params === null || params === void 0 ? void 0 : params.history) {\n            validateChatHistory(params.history);\n            this._history = params.history;\n        }\n    }\n    /**\n     * Gets the chat history so far. Blocked prompts are not added to history.\n     * Blocked candidates are not added to history, nor are the prompts that\n     * generated them.\n     */\n    async getHistory() {\n        await this._sendPromise;\n        return this._history;\n    }\n    /**\n     * Sends a chat message and receives a non-streaming\n     * {@link GenerateContentResult}.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async sendMessage(request, requestOptions = {}) {\n        var _a, _b, _c, _d, _e, _f;\n        await this._sendPromise;\n        const newContent = formatNewContent(request);\n        const generateContentRequest = {\n            safetySettings: (_a = this.params) === null || _a === void 0 ? void 0 : _a.safetySettings,\n            generationConfig: (_b = this.params) === null || _b === void 0 ? void 0 : _b.generationConfig,\n            tools: (_c = this.params) === null || _c === void 0 ? void 0 : _c.tools,\n            toolConfig: (_d = this.params) === null || _d === void 0 ? void 0 : _d.toolConfig,\n            systemInstruction: (_e = this.params) === null || _e === void 0 ? void 0 : _e.systemInstruction,\n            cachedContent: (_f = this.params) === null || _f === void 0 ? void 0 : _f.cachedContent,\n            contents: [...this._history, newContent],\n        };\n        const chatSessionRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        let finalResult;\n        // Add onto the chain.\n        this._sendPromise = this._sendPromise\n            .then(() => generateContent(this._apiKey, this.model, generateContentRequest, chatSessionRequestOptions))\n            .then((result) => {\n            var _a;\n            if (result.response.candidates &&\n                result.response.candidates.length > 0) {\n                this._history.push(newContent);\n                const responseContent = Object.assign({ parts: [], \n                    // Response seems to come back without a role set.\n                    role: \"model\" }, (_a = result.response.candidates) === null || _a === void 0 ? void 0 : _a[0].content);\n                this._history.push(responseContent);\n            }\n            else {\n                const blockErrorMessage = formatBlockErrorMessage(result.response);\n                if (blockErrorMessage) {\n                    console.warn(`sendMessage() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`);\n                }\n            }\n            finalResult = result;\n        });\n        await this._sendPromise;\n        return finalResult;\n    }\n    /**\n     * Sends a chat message and receives the response as a\n     * {@link GenerateContentStreamResult} containing an iterable stream\n     * and a response promise.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async sendMessageStream(request, requestOptions = {}) {\n        var _a, _b, _c, _d, _e, _f;\n        await this._sendPromise;\n        const newContent = formatNewContent(request);\n        const generateContentRequest = {\n            safetySettings: (_a = this.params) === null || _a === void 0 ? void 0 : _a.safetySettings,\n            generationConfig: (_b = this.params) === null || _b === void 0 ? void 0 : _b.generationConfig,\n            tools: (_c = this.params) === null || _c === void 0 ? void 0 : _c.tools,\n            toolConfig: (_d = this.params) === null || _d === void 0 ? void 0 : _d.toolConfig,\n            systemInstruction: (_e = this.params) === null || _e === void 0 ? void 0 : _e.systemInstruction,\n            cachedContent: (_f = this.params) === null || _f === void 0 ? void 0 : _f.cachedContent,\n            contents: [...this._history, newContent],\n        };\n        const chatSessionRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        const streamPromise = generateContentStream(this._apiKey, this.model, generateContentRequest, chatSessionRequestOptions);\n        // Add onto the chain.\n        this._sendPromise = this._sendPromise\n            .then(() => streamPromise)\n            // This must be handled to avoid unhandled rejection, but jump\n            // to the final catch block with a label to not log this error.\n            .catch((_ignored) => {\n            throw new Error(SILENT_ERROR);\n        })\n            .then((streamResult) => streamResult.response)\n            .then((response) => {\n            if (response.candidates && response.candidates.length > 0) {\n                this._history.push(newContent);\n                const responseContent = Object.assign({}, response.candidates[0].content);\n                // Response seems to come back without a role set.\n                if (!responseContent.role) {\n                    responseContent.role = \"model\";\n                }\n                this._history.push(responseContent);\n            }\n            else {\n                const blockErrorMessage = formatBlockErrorMessage(response);\n                if (blockErrorMessage) {\n                    console.warn(`sendMessageStream() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`);\n                }\n            }\n        })\n            .catch((e) => {\n            // Errors in streamPromise are already catchable by the user as\n            // streamPromise is returned.\n            // Avoid duplicating the error message in logs.\n            if (e.message !== SILENT_ERROR) {\n                // Users do not have access to _sendPromise to catch errors\n                // downstream from streamPromise, so they should not throw.\n                console.error(e);\n            }\n        });\n        return streamPromise;\n    }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function countTokens(apiKey, model, params, singleRequestOptions) {\n    const response = await makeModelRequest(model, Task.COUNT_TOKENS, apiKey, false, JSON.stringify(params), singleRequestOptions);\n    return response.json();\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function embedContent(apiKey, model, params, requestOptions) {\n    const response = await makeModelRequest(model, Task.EMBED_CONTENT, apiKey, false, JSON.stringify(params), requestOptions);\n    return response.json();\n}\nasync function batchEmbedContents(apiKey, model, params, requestOptions) {\n    const requestsWithModel = params.requests.map((request) => {\n        return Object.assign(Object.assign({}, request), { model });\n    });\n    const response = await makeModelRequest(model, Task.BATCH_EMBED_CONTENTS, apiKey, false, JSON.stringify({ requests: requestsWithModel }), requestOptions);\n    return response.json();\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Class for generative model APIs.\n * @public\n */\nclass GenerativeModel {\n    constructor(apiKey, modelParams, _requestOptions = {}) {\n        this.apiKey = apiKey;\n        this._requestOptions = _requestOptions;\n        if (modelParams.model.includes(\"/\")) {\n            // Models may be named \"models/model-name\" or \"tunedModels/model-name\"\n            this.model = modelParams.model;\n        }\n        else {\n            // If path is not included, assume it's a non-tuned model.\n            this.model = `models/${modelParams.model}`;\n        }\n        this.generationConfig = modelParams.generationConfig || {};\n        this.safetySettings = modelParams.safetySettings || [];\n        this.tools = modelParams.tools;\n        this.toolConfig = modelParams.toolConfig;\n        this.systemInstruction = formatSystemInstruction(modelParams.systemInstruction);\n        this.cachedContent = modelParams.cachedContent;\n    }\n    /**\n     * Makes a single non-streaming call to the model\n     * and returns an object containing a single {@link GenerateContentResponse}.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async generateContent(request, requestOptions = {}) {\n        var _a;\n        const formattedParams = formatGenerateContentInput(request);\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return generateContent(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings, tools: this.tools, toolConfig: this.toolConfig, systemInstruction: this.systemInstruction, cachedContent: (_a = this.cachedContent) === null || _a === void 0 ? void 0 : _a.name }, formattedParams), generativeModelRequestOptions);\n    }\n    /**\n     * Makes a single streaming call to the model and returns an object\n     * containing an iterable stream that iterates over all chunks in the\n     * streaming response as well as a promise that returns the final\n     * aggregated response.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async generateContentStream(request, requestOptions = {}) {\n        var _a;\n        const formattedParams = formatGenerateContentInput(request);\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return generateContentStream(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings, tools: this.tools, toolConfig: this.toolConfig, systemInstruction: this.systemInstruction, cachedContent: (_a = this.cachedContent) === null || _a === void 0 ? void 0 : _a.name }, formattedParams), generativeModelRequestOptions);\n    }\n    /**\n     * Gets a new {@link ChatSession} instance which can be used for\n     * multi-turn chats.\n     */\n    startChat(startChatParams) {\n        var _a;\n        return new ChatSession(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings, tools: this.tools, toolConfig: this.toolConfig, systemInstruction: this.systemInstruction, cachedContent: (_a = this.cachedContent) === null || _a === void 0 ? void 0 : _a.name }, startChatParams), this._requestOptions);\n    }\n    /**\n     * Counts the tokens in the provided request.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async countTokens(request, requestOptions = {}) {\n        const formattedParams = formatCountTokensInput(request, {\n            model: this.model,\n            generationConfig: this.generationConfig,\n            safetySettings: this.safetySettings,\n            tools: this.tools,\n            toolConfig: this.toolConfig,\n            systemInstruction: this.systemInstruction,\n            cachedContent: this.cachedContent,\n        });\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return countTokens(this.apiKey, this.model, formattedParams, generativeModelRequestOptions);\n    }\n    /**\n     * Embeds the provided content.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async embedContent(request, requestOptions = {}) {\n        const formattedParams = formatEmbedContentInput(request);\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return embedContent(this.apiKey, this.model, formattedParams, generativeModelRequestOptions);\n    }\n    /**\n     * Embeds an array of {@link EmbedContentRequest}s.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async batchEmbedContents(batchEmbedContentRequest, requestOptions = {}) {\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return batchEmbedContents(this.apiKey, this.model, batchEmbedContentRequest, generativeModelRequestOptions);\n    }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Top-level class for this SDK\n * @public\n */\nclass GoogleGenerativeAI {\n    constructor(apiKey) {\n        this.apiKey = apiKey;\n    }\n    /**\n     * Gets a {@link GenerativeModel} instance for the provided model name.\n     */\n    getGenerativeModel(modelParams, requestOptions) {\n        if (!modelParams.model) {\n            throw new GoogleGenerativeAIError(`Must provide a model name. ` +\n                `Example: genai.getGenerativeModel({ model: 'my-model-name' })`);\n        }\n        return new GenerativeModel(this.apiKey, modelParams, requestOptions);\n    }\n    /**\n     * Creates a {@link GenerativeModel} instance from provided content cache.\n     */\n    getGenerativeModelFromCachedContent(cachedContent, modelParams, requestOptions) {\n        if (!cachedContent.name) {\n            throw new GoogleGenerativeAIRequestInputError(\"Cached content must contain a `name` field.\");\n        }\n        if (!cachedContent.model) {\n            throw new GoogleGenerativeAIRequestInputError(\"Cached content must contain a `model` field.\");\n        }\n        /**\n         * Not checking tools and toolConfig for now as it would require a deep\n         * equality comparison and isn't likely to be a common case.\n         */\n        const disallowedDuplicates = [\"model\", \"systemInstruction\"];\n        for (const key of disallowedDuplicates) {\n            if ((modelParams === null || modelParams === void 0 ? void 0 : modelParams[key]) &&\n                cachedContent[key] &&\n                (modelParams === null || modelParams === void 0 ? void 0 : modelParams[key]) !== cachedContent[key]) {\n                if (key === \"model\") {\n                    const modelParamsComp = modelParams.model.startsWith(\"models/\")\n                        ? modelParams.model.replace(\"models/\", \"\")\n                        : modelParams.model;\n                    const cachedContentComp = cachedContent.model.startsWith(\"models/\")\n                        ? cachedContent.model.replace(\"models/\", \"\")\n                        : cachedContent.model;\n                    if (modelParamsComp === cachedContentComp) {\n                        continue;\n                    }\n                }\n                throw new GoogleGenerativeAIRequestInputError(`Different value for \"${key}\" specified in modelParams` +\n                    ` (${modelParams[key]}) and cachedContent (${cachedContent[key]})`);\n            }\n        }\n        const modelParamsFromCache = Object.assign(Object.assign({}, modelParams), { model: cachedContent.model, tools: cachedContent.tools, toolConfig: cachedContent.toolConfig, systemInstruction: cachedContent.systemInstruction, cachedContent });\n        return new GenerativeModel(this.apiKey, modelParamsFromCache, requestOptions);\n    }\n}\n\nexports.ChatSession = ChatSession;\nexports.GenerativeModel = GenerativeModel;\nexports.GoogleGenerativeAI = GoogleGenerativeAI;\nexports.GoogleGenerativeAIError = GoogleGenerativeAIError;\nexports.GoogleGenerativeAIFetchError = GoogleGenerativeAIFetchError;\nexports.GoogleGenerativeAIRequestInputError = GoogleGenerativeAIRequestInputError;\nexports.GoogleGenerativeAIResponseError = GoogleGenerativeAIResponseError;\nexports.POSSIBLE_ROLES = POSSIBLE_ROLES;\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZ29vZ2xlL2dlbmVyYXRpdmUtYWkvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyx5QkFBeUIsa0JBQWtCLEtBQUs7O0FBRWpEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBLENBQUMscUNBQXFDLDhCQUE4QixLQUFLO0FBQ3pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxzQkFBc0IsZUFBZSxLQUFLOztBQUUzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQjtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDJCQUEyQixvQkFBb0IsS0FBSztBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxpQ0FBaUMsMEJBQTBCLEtBQUs7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsOEJBQThCLHVCQUF1QixLQUFLO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywwQkFBMEIsbUJBQW1CLEtBQUs7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywyQkFBMkIsb0JBQW9CLEtBQUs7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHVCQUF1QixnQkFBZ0IsS0FBSztBQUM3QztBQUNBO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsa0NBQWtDLDJCQUEyQixLQUFLO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLG1DQUFtQyw0QkFBNEIsS0FBSzs7QUFFckU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsUUFBUTtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxvQkFBb0I7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLFFBQVEsR0FBRyxXQUFXLEdBQUcsV0FBVyxHQUFHLFVBQVU7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixtQkFBbUIsR0FBRyxnQkFBZ0I7QUFDaEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUdBQXVHLCtCQUErQixjQUFjLFVBQVU7QUFDOUo7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpR0FBaUcsV0FBVztBQUM1RztBQUNBO0FBQ0EsNkVBQTZFLFlBQVk7QUFDekY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvREFBb0Qsd0NBQXdDLHNEQUFzRDtBQUNsSjtBQUNBO0FBQ0Esc0ZBQXNGO0FBQ3RGO0FBQ0E7QUFDQSxZQUFZLG9CQUFvQjtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFpRSxlQUFlLElBQUksVUFBVTtBQUM5RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLG1DQUFtQztBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrRUFBa0UsZUFBZSxLQUFLLGlCQUFpQixFQUFFLG9CQUFvQixJQUFJLFFBQVE7QUFDekk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrREFBa0QsNEJBQTRCO0FBQzlFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkRBQTZELGtDQUFrQztBQUMvRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZFQUE2RSxrQ0FBa0M7QUFDL0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0RBQWtELDRCQUE0QjtBQUM5RTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZEQUE2RCxrQ0FBa0M7QUFDL0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0ZBQXNGLGtDQUFrQztBQUN4SDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrREFBa0QsNEJBQTRCO0FBQzlFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkRBQTZELGtDQUFrQztBQUMvRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNGQUFzRixrQ0FBa0M7QUFDeEg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxvQ0FBb0M7QUFDdEU7QUFDQTtBQUNBLDRCQUE0QiwyQ0FBMkM7QUFDdkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVEQUF1RCw0QkFBNEI7QUFDbkY7QUFDQSxnQ0FBZ0MsNkJBQTZCO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLHVGQUF1RixjQUFjO0FBQ3RILHVCQUF1QixnQ0FBZ0MscUNBQXFDLDJDQUEyQztBQUN2SSw0QkFBNEIsTUFBTSxpQkFBaUIsWUFBWTtBQUMvRCx1QkFBdUI7QUFDdkIsOEJBQThCO0FBQzlCLDZCQUE2QjtBQUM3Qiw0QkFBNEI7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrRkFBa0YsYUFBYTtBQUMvRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGNBQWM7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsY0FBYztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLGFBQWE7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwR0FBMEcsU0FBUztBQUNuSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQiwwQkFBMEIsYUFBYTtBQUN4RDtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsZUFBZTtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxvQkFBb0I7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQiw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3RUFBd0U7QUFDeEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsY0FBYztBQUM5QjtBQUNBLCtGQUErRixLQUFLO0FBQ3BHO0FBQ0E7QUFDQSwwRkFBMEYsTUFBTSx1QkFBdUIsK0JBQStCO0FBQ3RKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3RUFBd0UsS0FBSyxtQkFBbUIsSUFBSTtBQUNwRztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJEQUEyRDtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsNEJBQTRCO0FBQ3BDO0FBQ0EsbUNBQW1DLDRCQUE0QjtBQUMvRCxpQ0FBaUMsc0JBQXNCO0FBQ3ZELFFBQVEsNkNBQTZDO0FBQ3JEO0FBQ0Esa0RBQWtEO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdFQUF3RTtBQUN4RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3REFBd0Q7QUFDeEQ7QUFDQSxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9FQUFvRSxrQkFBa0I7QUFDdEY7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLG1DQUFtQztBQUMzQztBQUNBO0FBQ0EsbUNBQW1DLDRCQUE0QjtBQUMvRCxpQ0FBaUMsc0JBQXNCO0FBQ3ZELFFBQVEsNkNBQTZDO0FBQ3JEO0FBQ0Esd0RBQXdEO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdFQUF3RTtBQUN4RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0RBQXdEO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBFQUEwRSxrQkFBa0I7QUFDNUY7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsY0FBYyxPQUFPO0FBQ2xFLEtBQUs7QUFDTCw4R0FBOEcsNkJBQTZCO0FBQzNJO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseURBQXlEO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsa0JBQWtCO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0RBQWtELDhCQUE4QjtBQUNoRjtBQUNBLG1DQUFtQyw0QkFBNEI7QUFDL0QsaUNBQWlDLHNCQUFzQjtBQUN2RCxRQUFRLDZDQUE2QztBQUNyRDtBQUNBLHNEQUFzRDtBQUN0RDtBQUNBO0FBQ0EsNEVBQTRFO0FBQzVFLHdFQUF3RSxnUUFBZ1E7QUFDeFU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsNEJBQTRCO0FBQy9ELGlDQUFpQyxzQkFBc0I7QUFDdkQsUUFBUSw2Q0FBNkM7QUFDckQ7QUFDQSw0REFBNEQ7QUFDNUQ7QUFDQTtBQUNBLDRFQUE0RTtBQUM1RSw4RUFBOEUsZ1FBQWdRO0FBQzlVO0FBQ0E7QUFDQSxtQkFBbUIsbUJBQW1CO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0VBQXdFLGdRQUFnUTtBQUN4VTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyw0QkFBNEI7QUFDL0QsaUNBQWlDLHNCQUFzQjtBQUN2RCxRQUFRLDZDQUE2QztBQUNyRDtBQUNBLGtEQUFrRDtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULDRFQUE0RTtBQUM1RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLDRCQUE0QjtBQUMvRCxpQ0FBaUMsc0JBQXNCO0FBQ3ZELFFBQVEsNkNBQTZDO0FBQ3JEO0FBQ0EsbURBQW1EO0FBQ25EO0FBQ0EsNEVBQTRFO0FBQzVFO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQiwwQkFBMEI7QUFDckQ7QUFDQSxtQ0FBbUMsNEJBQTRCO0FBQy9ELGlDQUFpQyxzQkFBc0I7QUFDdkQsUUFBUSw2Q0FBNkM7QUFDckQ7QUFDQSwwRUFBMEU7QUFDMUUsNEVBQTRFO0FBQzVFO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsdUJBQXVCO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFELHdCQUF3QjtBQUM3RTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix1QkFBdUI7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzRkFBc0YsSUFBSTtBQUMxRix5QkFBeUIsaUJBQWlCLHVCQUF1QixtQkFBbUI7QUFDcEY7QUFDQTtBQUNBLG1FQUFtRSxrQkFBa0IsaUtBQWlLO0FBQ3RQO0FBQ0E7QUFDQTs7QUFFQSxtQkFBbUI7QUFDbkIsdUJBQXVCO0FBQ3ZCLDBCQUEwQjtBQUMxQiwrQkFBK0I7QUFDL0Isb0NBQW9DO0FBQ3BDLDJDQUEyQztBQUMzQyx1Q0FBdUM7QUFDdkMsc0JBQXNCO0FBQ3RCIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXE5ldyBmb2xkZXJcXFZpY2VcXFZpY2VcXG5vZGVfbW9kdWxlc1xcQGdvb2dsZVxcZ2VuZXJhdGl2ZS1haVxcZGlzdFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG4vKipcbiAqIENvbnRhaW5zIHRoZSBsaXN0IG9mIE9wZW5BUEkgZGF0YSB0eXBlc1xuICogYXMgZGVmaW5lZCBieSBodHRwczovL3N3YWdnZXIuaW8vZG9jcy9zcGVjaWZpY2F0aW9uL2RhdGEtbW9kZWxzL2RhdGEtdHlwZXMvXG4gKiBAcHVibGljXG4gKi9cbmV4cG9ydHMuU2NoZW1hVHlwZSA9IHZvaWQgMDtcbihmdW5jdGlvbiAoU2NoZW1hVHlwZSkge1xuICAgIC8qKiBTdHJpbmcgdHlwZS4gKi9cbiAgICBTY2hlbWFUeXBlW1wiU1RSSU5HXCJdID0gXCJzdHJpbmdcIjtcbiAgICAvKiogTnVtYmVyIHR5cGUuICovXG4gICAgU2NoZW1hVHlwZVtcIk5VTUJFUlwiXSA9IFwibnVtYmVyXCI7XG4gICAgLyoqIEludGVnZXIgdHlwZS4gKi9cbiAgICBTY2hlbWFUeXBlW1wiSU5URUdFUlwiXSA9IFwiaW50ZWdlclwiO1xuICAgIC8qKiBCb29sZWFuIHR5cGUuICovXG4gICAgU2NoZW1hVHlwZVtcIkJPT0xFQU5cIl0gPSBcImJvb2xlYW5cIjtcbiAgICAvKiogQXJyYXkgdHlwZS4gKi9cbiAgICBTY2hlbWFUeXBlW1wiQVJSQVlcIl0gPSBcImFycmF5XCI7XG4gICAgLyoqIE9iamVjdCB0eXBlLiAqL1xuICAgIFNjaGVtYVR5cGVbXCJPQkpFQ1RcIl0gPSBcIm9iamVjdFwiO1xufSkoZXhwb3J0cy5TY2hlbWFUeXBlIHx8IChleHBvcnRzLlNjaGVtYVR5cGUgPSB7fSkpO1xuXG4vKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgMjAyNCBHb29nbGUgTExDXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuLyoqXG4gKiBAcHVibGljXG4gKi9cbmV4cG9ydHMuRXhlY3V0YWJsZUNvZGVMYW5ndWFnZSA9IHZvaWQgMDtcbihmdW5jdGlvbiAoRXhlY3V0YWJsZUNvZGVMYW5ndWFnZSkge1xuICAgIEV4ZWN1dGFibGVDb2RlTGFuZ3VhZ2VbXCJMQU5HVUFHRV9VTlNQRUNJRklFRFwiXSA9IFwibGFuZ3VhZ2VfdW5zcGVjaWZpZWRcIjtcbiAgICBFeGVjdXRhYmxlQ29kZUxhbmd1YWdlW1wiUFlUSE9OXCJdID0gXCJweXRob25cIjtcbn0pKGV4cG9ydHMuRXhlY3V0YWJsZUNvZGVMYW5ndWFnZSB8fCAoZXhwb3J0cy5FeGVjdXRhYmxlQ29kZUxhbmd1YWdlID0ge30pKTtcbi8qKlxuICogUG9zc2libGUgb3V0Y29tZXMgb2YgY29kZSBleGVjdXRpb24uXG4gKiBAcHVibGljXG4gKi9cbmV4cG9ydHMuT3V0Y29tZSA9IHZvaWQgMDtcbihmdW5jdGlvbiAoT3V0Y29tZSkge1xuICAgIC8qKlxuICAgICAqIFVuc3BlY2lmaWVkIHN0YXR1cy4gVGhpcyB2YWx1ZSBzaG91bGQgbm90IGJlIHVzZWQuXG4gICAgICovXG4gICAgT3V0Y29tZVtcIk9VVENPTUVfVU5TUEVDSUZJRURcIl0gPSBcIm91dGNvbWVfdW5zcGVjaWZpZWRcIjtcbiAgICAvKipcbiAgICAgKiBDb2RlIGV4ZWN1dGlvbiBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5LlxuICAgICAqL1xuICAgIE91dGNvbWVbXCJPVVRDT01FX09LXCJdID0gXCJvdXRjb21lX29rXCI7XG4gICAgLyoqXG4gICAgICogQ29kZSBleGVjdXRpb24gZmluaXNoZWQgYnV0IHdpdGggYSBmYWlsdXJlLiBgc3RkZXJyYCBzaG91bGQgY29udGFpbiB0aGVcbiAgICAgKiByZWFzb24uXG4gICAgICovXG4gICAgT3V0Y29tZVtcIk9VVENPTUVfRkFJTEVEXCJdID0gXCJvdXRjb21lX2ZhaWxlZFwiO1xuICAgIC8qKlxuICAgICAqIENvZGUgZXhlY3V0aW9uIHJhbiBmb3IgdG9vIGxvbmcsIGFuZCB3YXMgY2FuY2VsbGVkLiBUaGVyZSBtYXkgb3IgbWF5IG5vdFxuICAgICAqIGJlIGEgcGFydGlhbCBvdXRwdXQgcHJlc2VudC5cbiAgICAgKi9cbiAgICBPdXRjb21lW1wiT1VUQ09NRV9ERUFETElORV9FWENFRURFRFwiXSA9IFwib3V0Y29tZV9kZWFkbGluZV9leGNlZWRlZFwiO1xufSkoZXhwb3J0cy5PdXRjb21lIHx8IChleHBvcnRzLk91dGNvbWUgPSB7fSkpO1xuXG4vKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgMjAyNCBHb29nbGUgTExDXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuLyoqXG4gKiBQb3NzaWJsZSByb2xlcy5cbiAqIEBwdWJsaWNcbiAqL1xuY29uc3QgUE9TU0lCTEVfUk9MRVMgPSBbXCJ1c2VyXCIsIFwibW9kZWxcIiwgXCJmdW5jdGlvblwiLCBcInN5c3RlbVwiXTtcbi8qKlxuICogSGFybSBjYXRlZ29yaWVzIHRoYXQgd291bGQgY2F1c2UgcHJvbXB0cyBvciBjYW5kaWRhdGVzIHRvIGJlIGJsb2NrZWQuXG4gKiBAcHVibGljXG4gKi9cbmV4cG9ydHMuSGFybUNhdGVnb3J5ID0gdm9pZCAwO1xuKGZ1bmN0aW9uIChIYXJtQ2F0ZWdvcnkpIHtcbiAgICBIYXJtQ2F0ZWdvcnlbXCJIQVJNX0NBVEVHT1JZX1VOU1BFQ0lGSUVEXCJdID0gXCJIQVJNX0NBVEVHT1JZX1VOU1BFQ0lGSUVEXCI7XG4gICAgSGFybUNhdGVnb3J5W1wiSEFSTV9DQVRFR09SWV9IQVRFX1NQRUVDSFwiXSA9IFwiSEFSTV9DQVRFR09SWV9IQVRFX1NQRUVDSFwiO1xuICAgIEhhcm1DYXRlZ29yeVtcIkhBUk1fQ0FURUdPUllfU0VYVUFMTFlfRVhQTElDSVRcIl0gPSBcIkhBUk1fQ0FURUdPUllfU0VYVUFMTFlfRVhQTElDSVRcIjtcbiAgICBIYXJtQ2F0ZWdvcnlbXCJIQVJNX0NBVEVHT1JZX0hBUkFTU01FTlRcIl0gPSBcIkhBUk1fQ0FURUdPUllfSEFSQVNTTUVOVFwiO1xuICAgIEhhcm1DYXRlZ29yeVtcIkhBUk1fQ0FURUdPUllfREFOR0VST1VTX0NPTlRFTlRcIl0gPSBcIkhBUk1fQ0FURUdPUllfREFOR0VST1VTX0NPTlRFTlRcIjtcbn0pKGV4cG9ydHMuSGFybUNhdGVnb3J5IHx8IChleHBvcnRzLkhhcm1DYXRlZ29yeSA9IHt9KSk7XG4vKipcbiAqIFRocmVzaG9sZCBhYm92ZSB3aGljaCBhIHByb21wdCBvciBjYW5kaWRhdGUgd2lsbCBiZSBibG9ja2VkLlxuICogQHB1YmxpY1xuICovXG5leHBvcnRzLkhhcm1CbG9ja1RocmVzaG9sZCA9IHZvaWQgMDtcbihmdW5jdGlvbiAoSGFybUJsb2NrVGhyZXNob2xkKSB7XG4gICAgLy8gVGhyZXNob2xkIGlzIHVuc3BlY2lmaWVkLlxuICAgIEhhcm1CbG9ja1RocmVzaG9sZFtcIkhBUk1fQkxPQ0tfVEhSRVNIT0xEX1VOU1BFQ0lGSUVEXCJdID0gXCJIQVJNX0JMT0NLX1RIUkVTSE9MRF9VTlNQRUNJRklFRFwiO1xuICAgIC8vIENvbnRlbnQgd2l0aCBORUdMSUdJQkxFIHdpbGwgYmUgYWxsb3dlZC5cbiAgICBIYXJtQmxvY2tUaHJlc2hvbGRbXCJCTE9DS19MT1dfQU5EX0FCT1ZFXCJdID0gXCJCTE9DS19MT1dfQU5EX0FCT1ZFXCI7XG4gICAgLy8gQ29udGVudCB3aXRoIE5FR0xJR0lCTEUgYW5kIExPVyB3aWxsIGJlIGFsbG93ZWQuXG4gICAgSGFybUJsb2NrVGhyZXNob2xkW1wiQkxPQ0tfTUVESVVNX0FORF9BQk9WRVwiXSA9IFwiQkxPQ0tfTUVESVVNX0FORF9BQk9WRVwiO1xuICAgIC8vIENvbnRlbnQgd2l0aCBORUdMSUdJQkxFLCBMT1csIGFuZCBNRURJVU0gd2lsbCBiZSBhbGxvd2VkLlxuICAgIEhhcm1CbG9ja1RocmVzaG9sZFtcIkJMT0NLX09OTFlfSElHSFwiXSA9IFwiQkxPQ0tfT05MWV9ISUdIXCI7XG4gICAgLy8gQWxsIGNvbnRlbnQgd2lsbCBiZSBhbGxvd2VkLlxuICAgIEhhcm1CbG9ja1RocmVzaG9sZFtcIkJMT0NLX05PTkVcIl0gPSBcIkJMT0NLX05PTkVcIjtcbn0pKGV4cG9ydHMuSGFybUJsb2NrVGhyZXNob2xkIHx8IChleHBvcnRzLkhhcm1CbG9ja1RocmVzaG9sZCA9IHt9KSk7XG4vKipcbiAqIFByb2JhYmlsaXR5IHRoYXQgYSBwcm9tcHQgb3IgY2FuZGlkYXRlIG1hdGNoZXMgYSBoYXJtIGNhdGVnb3J5LlxuICogQHB1YmxpY1xuICovXG5leHBvcnRzLkhhcm1Qcm9iYWJpbGl0eSA9IHZvaWQgMDtcbihmdW5jdGlvbiAoSGFybVByb2JhYmlsaXR5KSB7XG4gICAgLy8gUHJvYmFiaWxpdHkgaXMgdW5zcGVjaWZpZWQuXG4gICAgSGFybVByb2JhYmlsaXR5W1wiSEFSTV9QUk9CQUJJTElUWV9VTlNQRUNJRklFRFwiXSA9IFwiSEFSTV9QUk9CQUJJTElUWV9VTlNQRUNJRklFRFwiO1xuICAgIC8vIENvbnRlbnQgaGFzIGEgbmVnbGlnaWJsZSBjaGFuY2Ugb2YgYmVpbmcgdW5zYWZlLlxuICAgIEhhcm1Qcm9iYWJpbGl0eVtcIk5FR0xJR0lCTEVcIl0gPSBcIk5FR0xJR0lCTEVcIjtcbiAgICAvLyBDb250ZW50IGhhcyBhIGxvdyBjaGFuY2Ugb2YgYmVpbmcgdW5zYWZlLlxuICAgIEhhcm1Qcm9iYWJpbGl0eVtcIkxPV1wiXSA9IFwiTE9XXCI7XG4gICAgLy8gQ29udGVudCBoYXMgYSBtZWRpdW0gY2hhbmNlIG9mIGJlaW5nIHVuc2FmZS5cbiAgICBIYXJtUHJvYmFiaWxpdHlbXCJNRURJVU1cIl0gPSBcIk1FRElVTVwiO1xuICAgIC8vIENvbnRlbnQgaGFzIGEgaGlnaCBjaGFuY2Ugb2YgYmVpbmcgdW5zYWZlLlxuICAgIEhhcm1Qcm9iYWJpbGl0eVtcIkhJR0hcIl0gPSBcIkhJR0hcIjtcbn0pKGV4cG9ydHMuSGFybVByb2JhYmlsaXR5IHx8IChleHBvcnRzLkhhcm1Qcm9iYWJpbGl0eSA9IHt9KSk7XG4vKipcbiAqIFJlYXNvbiB0aGF0IGEgcHJvbXB0IHdhcyBibG9ja2VkLlxuICogQHB1YmxpY1xuICovXG5leHBvcnRzLkJsb2NrUmVhc29uID0gdm9pZCAwO1xuKGZ1bmN0aW9uIChCbG9ja1JlYXNvbikge1xuICAgIC8vIEEgYmxvY2tlZCByZWFzb24gd2FzIG5vdCBzcGVjaWZpZWQuXG4gICAgQmxvY2tSZWFzb25bXCJCTE9DS0VEX1JFQVNPTl9VTlNQRUNJRklFRFwiXSA9IFwiQkxPQ0tFRF9SRUFTT05fVU5TUEVDSUZJRURcIjtcbiAgICAvLyBDb250ZW50IHdhcyBibG9ja2VkIGJ5IHNhZmV0eSBzZXR0aW5ncy5cbiAgICBCbG9ja1JlYXNvbltcIlNBRkVUWVwiXSA9IFwiU0FGRVRZXCI7XG4gICAgLy8gQ29udGVudCB3YXMgYmxvY2tlZCwgYnV0IHRoZSByZWFzb24gaXMgdW5jYXRlZ29yaXplZC5cbiAgICBCbG9ja1JlYXNvbltcIk9USEVSXCJdID0gXCJPVEhFUlwiO1xufSkoZXhwb3J0cy5CbG9ja1JlYXNvbiB8fCAoZXhwb3J0cy5CbG9ja1JlYXNvbiA9IHt9KSk7XG4vKipcbiAqIFJlYXNvbiB0aGF0IGEgY2FuZGlkYXRlIGZpbmlzaGVkLlxuICogQHB1YmxpY1xuICovXG5leHBvcnRzLkZpbmlzaFJlYXNvbiA9IHZvaWQgMDtcbihmdW5jdGlvbiAoRmluaXNoUmVhc29uKSB7XG4gICAgLy8gRGVmYXVsdCB2YWx1ZS4gVGhpcyB2YWx1ZSBpcyB1bnVzZWQuXG4gICAgRmluaXNoUmVhc29uW1wiRklOSVNIX1JFQVNPTl9VTlNQRUNJRklFRFwiXSA9IFwiRklOSVNIX1JFQVNPTl9VTlNQRUNJRklFRFwiO1xuICAgIC8vIE5hdHVyYWwgc3RvcCBwb2ludCBvZiB0aGUgbW9kZWwgb3IgcHJvdmlkZWQgc3RvcCBzZXF1ZW5jZS5cbiAgICBGaW5pc2hSZWFzb25bXCJTVE9QXCJdID0gXCJTVE9QXCI7XG4gICAgLy8gVGhlIG1heGltdW0gbnVtYmVyIG9mIHRva2VucyBhcyBzcGVjaWZpZWQgaW4gdGhlIHJlcXVlc3Qgd2FzIHJlYWNoZWQuXG4gICAgRmluaXNoUmVhc29uW1wiTUFYX1RPS0VOU1wiXSA9IFwiTUFYX1RPS0VOU1wiO1xuICAgIC8vIFRoZSBjYW5kaWRhdGUgY29udGVudCB3YXMgZmxhZ2dlZCBmb3Igc2FmZXR5IHJlYXNvbnMuXG4gICAgRmluaXNoUmVhc29uW1wiU0FGRVRZXCJdID0gXCJTQUZFVFlcIjtcbiAgICAvLyBUaGUgY2FuZGlkYXRlIGNvbnRlbnQgd2FzIGZsYWdnZWQgZm9yIHJlY2l0YXRpb24gcmVhc29ucy5cbiAgICBGaW5pc2hSZWFzb25bXCJSRUNJVEFUSU9OXCJdID0gXCJSRUNJVEFUSU9OXCI7XG4gICAgLy8gVGhlIGNhbmRpZGF0ZSBjb250ZW50IHdhcyBmbGFnZ2VkIGZvciB1c2luZyBhbiB1bnN1cHBvcnRlZCBsYW5ndWFnZS5cbiAgICBGaW5pc2hSZWFzb25bXCJMQU5HVUFHRVwiXSA9IFwiTEFOR1VBR0VcIjtcbiAgICAvLyBVbmtub3duIHJlYXNvbi5cbiAgICBGaW5pc2hSZWFzb25bXCJPVEhFUlwiXSA9IFwiT1RIRVJcIjtcbn0pKGV4cG9ydHMuRmluaXNoUmVhc29uIHx8IChleHBvcnRzLkZpbmlzaFJlYXNvbiA9IHt9KSk7XG4vKipcbiAqIFRhc2sgdHlwZSBmb3IgZW1iZWRkaW5nIGNvbnRlbnQuXG4gKiBAcHVibGljXG4gKi9cbmV4cG9ydHMuVGFza1R5cGUgPSB2b2lkIDA7XG4oZnVuY3Rpb24gKFRhc2tUeXBlKSB7XG4gICAgVGFza1R5cGVbXCJUQVNLX1RZUEVfVU5TUEVDSUZJRURcIl0gPSBcIlRBU0tfVFlQRV9VTlNQRUNJRklFRFwiO1xuICAgIFRhc2tUeXBlW1wiUkVUUklFVkFMX1FVRVJZXCJdID0gXCJSRVRSSUVWQUxfUVVFUllcIjtcbiAgICBUYXNrVHlwZVtcIlJFVFJJRVZBTF9ET0NVTUVOVFwiXSA9IFwiUkVUUklFVkFMX0RPQ1VNRU5UXCI7XG4gICAgVGFza1R5cGVbXCJTRU1BTlRJQ19TSU1JTEFSSVRZXCJdID0gXCJTRU1BTlRJQ19TSU1JTEFSSVRZXCI7XG4gICAgVGFza1R5cGVbXCJDTEFTU0lGSUNBVElPTlwiXSA9IFwiQ0xBU1NJRklDQVRJT05cIjtcbiAgICBUYXNrVHlwZVtcIkNMVVNURVJJTkdcIl0gPSBcIkNMVVNURVJJTkdcIjtcbn0pKGV4cG9ydHMuVGFza1R5cGUgfHwgKGV4cG9ydHMuVGFza1R5cGUgPSB7fSkpO1xuLyoqXG4gKiBAcHVibGljXG4gKi9cbmV4cG9ydHMuRnVuY3Rpb25DYWxsaW5nTW9kZSA9IHZvaWQgMDtcbihmdW5jdGlvbiAoRnVuY3Rpb25DYWxsaW5nTW9kZSkge1xuICAgIC8vIFVuc3BlY2lmaWVkIGZ1bmN0aW9uIGNhbGxpbmcgbW9kZS4gVGhpcyB2YWx1ZSBzaG91bGQgbm90IGJlIHVzZWQuXG4gICAgRnVuY3Rpb25DYWxsaW5nTW9kZVtcIk1PREVfVU5TUEVDSUZJRURcIl0gPSBcIk1PREVfVU5TUEVDSUZJRURcIjtcbiAgICAvLyBEZWZhdWx0IG1vZGVsIGJlaGF2aW9yLCBtb2RlbCBkZWNpZGVzIHRvIHByZWRpY3QgZWl0aGVyIGEgZnVuY3Rpb24gY2FsbFxuICAgIC8vIG9yIGEgbmF0dXJhbCBsYW5ndWFnZSByZXBzcG9zZS5cbiAgICBGdW5jdGlvbkNhbGxpbmdNb2RlW1wiQVVUT1wiXSA9IFwiQVVUT1wiO1xuICAgIC8vIE1vZGVsIGlzIGNvbnN0cmFpbmVkIHRvIGFsd2F5cyBwcmVkaWN0aW5nIGEgZnVuY3Rpb24gY2FsbCBvbmx5LlxuICAgIC8vIElmIFwiYWxsb3dlZF9mdW5jdGlvbl9uYW1lc1wiIGFyZSBzZXQsIHRoZSBwcmVkaWN0ZWQgZnVuY3Rpb24gY2FsbCB3aWxsIGJlXG4gICAgLy8gbGltaXRlZCB0byBhbnkgb25lIG9mIFwiYWxsb3dlZF9mdW5jdGlvbl9uYW1lc1wiLCBlbHNlIHRoZSBwcmVkaWN0ZWRcbiAgICAvLyBmdW5jdGlvbiBjYWxsIHdpbGwgYmUgYW55IG9uZSBvZiB0aGUgcHJvdmlkZWQgXCJmdW5jdGlvbl9kZWNsYXJhdGlvbnNcIi5cbiAgICBGdW5jdGlvbkNhbGxpbmdNb2RlW1wiQU5ZXCJdID0gXCJBTllcIjtcbiAgICAvLyBNb2RlbCB3aWxsIG5vdCBwcmVkaWN0IGFueSBmdW5jdGlvbiBjYWxsLiBNb2RlbCBiZWhhdmlvciBpcyBzYW1lIGFzIHdoZW5cbiAgICAvLyBub3QgcGFzc2luZyBhbnkgZnVuY3Rpb24gZGVjbGFyYXRpb25zLlxuICAgIEZ1bmN0aW9uQ2FsbGluZ01vZGVbXCJOT05FXCJdID0gXCJOT05FXCI7XG59KShleHBvcnRzLkZ1bmN0aW9uQ2FsbGluZ01vZGUgfHwgKGV4cG9ydHMuRnVuY3Rpb25DYWxsaW5nTW9kZSA9IHt9KSk7XG4vKipcbiAqIFRoZSBtb2RlIG9mIHRoZSBwcmVkaWN0b3IgdG8gYmUgdXNlZCBpbiBkeW5hbWljIHJldHJpZXZhbC5cbiAqIEBwdWJsaWNcbiAqL1xuZXhwb3J0cy5EeW5hbWljUmV0cmlldmFsTW9kZSA9IHZvaWQgMDtcbihmdW5jdGlvbiAoRHluYW1pY1JldHJpZXZhbE1vZGUpIHtcbiAgICAvLyBVbnNwZWNpZmllZCBmdW5jdGlvbiBjYWxsaW5nIG1vZGUuIFRoaXMgdmFsdWUgc2hvdWxkIG5vdCBiZSB1c2VkLlxuICAgIER5bmFtaWNSZXRyaWV2YWxNb2RlW1wiTU9ERV9VTlNQRUNJRklFRFwiXSA9IFwiTU9ERV9VTlNQRUNJRklFRFwiO1xuICAgIC8vIFJ1biByZXRyaWV2YWwgb25seSB3aGVuIHN5c3RlbSBkZWNpZGVzIGl0IGlzIG5lY2Vzc2FyeS5cbiAgICBEeW5hbWljUmV0cmlldmFsTW9kZVtcIk1PREVfRFlOQU1JQ1wiXSA9IFwiTU9ERV9EWU5BTUlDXCI7XG59KShleHBvcnRzLkR5bmFtaWNSZXRyaWV2YWxNb2RlIHx8IChleHBvcnRzLkR5bmFtaWNSZXRyaWV2YWxNb2RlID0ge30pKTtcblxuLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IDIwMjQgR29vZ2xlIExMQ1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8qKlxuICogQmFzaWMgZXJyb3IgdHlwZSBmb3IgdGhpcyBTREsuXG4gKiBAcHVibGljXG4gKi9cbmNsYXNzIEdvb2dsZUdlbmVyYXRpdmVBSUVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UpIHtcbiAgICAgICAgc3VwZXIoYFtHb29nbGVHZW5lcmF0aXZlQUkgRXJyb3JdOiAke21lc3NhZ2V9YCk7XG4gICAgfVxufVxuLyoqXG4gKiBFcnJvcnMgaW4gdGhlIGNvbnRlbnRzIG9mIGEgcmVzcG9uc2UgZnJvbSB0aGUgbW9kZWwuIFRoaXMgaW5jbHVkZXMgcGFyc2luZ1xuICogZXJyb3JzLCBvciByZXNwb25zZXMgaW5jbHVkaW5nIGEgc2FmZXR5IGJsb2NrIHJlYXNvbi5cbiAqIEBwdWJsaWNcbiAqL1xuY2xhc3MgR29vZ2xlR2VuZXJhdGl2ZUFJUmVzcG9uc2VFcnJvciBleHRlbmRzIEdvb2dsZUdlbmVyYXRpdmVBSUVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcihtZXNzYWdlLCByZXNwb25zZSkge1xuICAgICAgICBzdXBlcihtZXNzYWdlKTtcbiAgICAgICAgdGhpcy5yZXNwb25zZSA9IHJlc3BvbnNlO1xuICAgIH1cbn1cbi8qKlxuICogRXJyb3IgY2xhc3MgY292ZXJpbmcgSFRUUCBlcnJvcnMgd2hlbiBjYWxsaW5nIHRoZSBzZXJ2ZXIuIEluY2x1ZGVzIEhUVFBcbiAqIHN0YXR1cywgc3RhdHVzVGV4dCwgYW5kIG9wdGlvbmFsIGRldGFpbHMsIGlmIHByb3ZpZGVkIGluIHRoZSBzZXJ2ZXIgcmVzcG9uc2UuXG4gKiBAcHVibGljXG4gKi9cbmNsYXNzIEdvb2dsZUdlbmVyYXRpdmVBSUZldGNoRXJyb3IgZXh0ZW5kcyBHb29nbGVHZW5lcmF0aXZlQUlFcnJvciB7XG4gICAgY29uc3RydWN0b3IobWVzc2FnZSwgc3RhdHVzLCBzdGF0dXNUZXh0LCBlcnJvckRldGFpbHMpIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgICAgIHRoaXMuc3RhdHVzID0gc3RhdHVzO1xuICAgICAgICB0aGlzLnN0YXR1c1RleHQgPSBzdGF0dXNUZXh0O1xuICAgICAgICB0aGlzLmVycm9yRGV0YWlscyA9IGVycm9yRGV0YWlscztcbiAgICB9XG59XG4vKipcbiAqIEVycm9ycyBpbiB0aGUgY29udGVudHMgb2YgYSByZXF1ZXN0IG9yaWdpbmF0aW5nIGZyb20gdXNlciBpbnB1dC5cbiAqIEBwdWJsaWNcbiAqL1xuY2xhc3MgR29vZ2xlR2VuZXJhdGl2ZUFJUmVxdWVzdElucHV0RXJyb3IgZXh0ZW5kcyBHb29nbGVHZW5lcmF0aXZlQUlFcnJvciB7XG59XG5cbi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCAyMDI0IEdvb2dsZSBMTENcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5jb25zdCBERUZBVUxUX0JBU0VfVVJMID0gXCJodHRwczovL2dlbmVyYXRpdmVsYW5ndWFnZS5nb29nbGVhcGlzLmNvbVwiO1xuY29uc3QgREVGQVVMVF9BUElfVkVSU0lPTiA9IFwidjFiZXRhXCI7XG4vKipcbiAqIFdlIGNhbid0IGByZXF1aXJlYCBwYWNrYWdlLmpzb24gaWYgdGhpcyBydW5zIG9uIHdlYi4gV2Ugd2lsbCB1c2Ugcm9sbHVwIHRvXG4gKiBzd2FwIGluIHRoZSB2ZXJzaW9uIG51bWJlciBoZXJlIGF0IGJ1aWxkIHRpbWUuXG4gKi9cbmNvbnN0IFBBQ0tBR0VfVkVSU0lPTiA9IFwiMC4yMS4wXCI7XG5jb25zdCBQQUNLQUdFX0xPR19IRUFERVIgPSBcImdlbmFpLWpzXCI7XG52YXIgVGFzaztcbihmdW5jdGlvbiAoVGFzaykge1xuICAgIFRhc2tbXCJHRU5FUkFURV9DT05URU5UXCJdID0gXCJnZW5lcmF0ZUNvbnRlbnRcIjtcbiAgICBUYXNrW1wiU1RSRUFNX0dFTkVSQVRFX0NPTlRFTlRcIl0gPSBcInN0cmVhbUdlbmVyYXRlQ29udGVudFwiO1xuICAgIFRhc2tbXCJDT1VOVF9UT0tFTlNcIl0gPSBcImNvdW50VG9rZW5zXCI7XG4gICAgVGFza1tcIkVNQkVEX0NPTlRFTlRcIl0gPSBcImVtYmVkQ29udGVudFwiO1xuICAgIFRhc2tbXCJCQVRDSF9FTUJFRF9DT05URU5UU1wiXSA9IFwiYmF0Y2hFbWJlZENvbnRlbnRzXCI7XG59KShUYXNrIHx8IChUYXNrID0ge30pKTtcbmNsYXNzIFJlcXVlc3RVcmwge1xuICAgIGNvbnN0cnVjdG9yKG1vZGVsLCB0YXNrLCBhcGlLZXksIHN0cmVhbSwgcmVxdWVzdE9wdGlvbnMpIHtcbiAgICAgICAgdGhpcy5tb2RlbCA9IG1vZGVsO1xuICAgICAgICB0aGlzLnRhc2sgPSB0YXNrO1xuICAgICAgICB0aGlzLmFwaUtleSA9IGFwaUtleTtcbiAgICAgICAgdGhpcy5zdHJlYW0gPSBzdHJlYW07XG4gICAgICAgIHRoaXMucmVxdWVzdE9wdGlvbnMgPSByZXF1ZXN0T3B0aW9ucztcbiAgICB9XG4gICAgdG9TdHJpbmcoKSB7XG4gICAgICAgIHZhciBfYSwgX2I7XG4gICAgICAgIGNvbnN0IGFwaVZlcnNpb24gPSAoKF9hID0gdGhpcy5yZXF1ZXN0T3B0aW9ucykgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmFwaVZlcnNpb24pIHx8IERFRkFVTFRfQVBJX1ZFUlNJT047XG4gICAgICAgIGNvbnN0IGJhc2VVcmwgPSAoKF9iID0gdGhpcy5yZXF1ZXN0T3B0aW9ucykgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLmJhc2VVcmwpIHx8IERFRkFVTFRfQkFTRV9VUkw7XG4gICAgICAgIGxldCB1cmwgPSBgJHtiYXNlVXJsfS8ke2FwaVZlcnNpb259LyR7dGhpcy5tb2RlbH06JHt0aGlzLnRhc2t9YDtcbiAgICAgICAgaWYgKHRoaXMuc3RyZWFtKSB7XG4gICAgICAgICAgICB1cmwgKz0gXCI/YWx0PXNzZVwiO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1cmw7XG4gICAgfVxufVxuLyoqXG4gKiBTaW1wbGUsIGJ1dCBtYXkgYmVjb21lIG1vcmUgY29tcGxleCBpZiB3ZSBhZGQgbW9yZSB2ZXJzaW9ucyB0byBsb2cuXG4gKi9cbmZ1bmN0aW9uIGdldENsaWVudEhlYWRlcnMocmVxdWVzdE9wdGlvbnMpIHtcbiAgICBjb25zdCBjbGllbnRIZWFkZXJzID0gW107XG4gICAgaWYgKHJlcXVlc3RPcHRpb25zID09PSBudWxsIHx8IHJlcXVlc3RPcHRpb25zID09PSB2b2lkIDAgPyB2b2lkIDAgOiByZXF1ZXN0T3B0aW9ucy5hcGlDbGllbnQpIHtcbiAgICAgICAgY2xpZW50SGVhZGVycy5wdXNoKHJlcXVlc3RPcHRpb25zLmFwaUNsaWVudCk7XG4gICAgfVxuICAgIGNsaWVudEhlYWRlcnMucHVzaChgJHtQQUNLQUdFX0xPR19IRUFERVJ9LyR7UEFDS0FHRV9WRVJTSU9OfWApO1xuICAgIHJldHVybiBjbGllbnRIZWFkZXJzLmpvaW4oXCIgXCIpO1xufVxuYXN5bmMgZnVuY3Rpb24gZ2V0SGVhZGVycyh1cmwpIHtcbiAgICB2YXIgX2E7XG4gICAgY29uc3QgaGVhZGVycyA9IG5ldyBIZWFkZXJzKCk7XG4gICAgaGVhZGVycy5hcHBlbmQoXCJDb250ZW50LVR5cGVcIiwgXCJhcHBsaWNhdGlvbi9qc29uXCIpO1xuICAgIGhlYWRlcnMuYXBwZW5kKFwieC1nb29nLWFwaS1jbGllbnRcIiwgZ2V0Q2xpZW50SGVhZGVycyh1cmwucmVxdWVzdE9wdGlvbnMpKTtcbiAgICBoZWFkZXJzLmFwcGVuZChcIngtZ29vZy1hcGkta2V5XCIsIHVybC5hcGlLZXkpO1xuICAgIGxldCBjdXN0b21IZWFkZXJzID0gKF9hID0gdXJsLnJlcXVlc3RPcHRpb25zKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY3VzdG9tSGVhZGVycztcbiAgICBpZiAoY3VzdG9tSGVhZGVycykge1xuICAgICAgICBpZiAoIShjdXN0b21IZWFkZXJzIGluc3RhbmNlb2YgSGVhZGVycykpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY3VzdG9tSGVhZGVycyA9IG5ldyBIZWFkZXJzKGN1c3RvbUhlYWRlcnMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJUmVxdWVzdElucHV0RXJyb3IoYHVuYWJsZSB0byBjb252ZXJ0IGN1c3RvbUhlYWRlcnMgdmFsdWUgJHtKU09OLnN0cmluZ2lmeShjdXN0b21IZWFkZXJzKX0gdG8gSGVhZGVyczogJHtlLm1lc3NhZ2V9YCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZm9yIChjb25zdCBbaGVhZGVyTmFtZSwgaGVhZGVyVmFsdWVdIG9mIGN1c3RvbUhlYWRlcnMuZW50cmllcygpKSB7XG4gICAgICAgICAgICBpZiAoaGVhZGVyTmFtZSA9PT0gXCJ4LWdvb2ctYXBpLWtleVwiKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEdvb2dsZUdlbmVyYXRpdmVBSVJlcXVlc3RJbnB1dEVycm9yKGBDYW5ub3Qgc2V0IHJlc2VydmVkIGhlYWRlciBuYW1lICR7aGVhZGVyTmFtZX1gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKGhlYWRlck5hbWUgPT09IFwieC1nb29nLWFwaS1jbGllbnRcIikge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBHb29nbGVHZW5lcmF0aXZlQUlSZXF1ZXN0SW5wdXRFcnJvcihgSGVhZGVyIG5hbWUgJHtoZWFkZXJOYW1lfSBjYW4gb25seSBiZSBzZXQgdXNpbmcgdGhlIGFwaUNsaWVudCBmaWVsZGApO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaGVhZGVycy5hcHBlbmQoaGVhZGVyTmFtZSwgaGVhZGVyVmFsdWUpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBoZWFkZXJzO1xufVxuYXN5bmMgZnVuY3Rpb24gY29uc3RydWN0TW9kZWxSZXF1ZXN0KG1vZGVsLCB0YXNrLCBhcGlLZXksIHN0cmVhbSwgYm9keSwgcmVxdWVzdE9wdGlvbnMpIHtcbiAgICBjb25zdCB1cmwgPSBuZXcgUmVxdWVzdFVybChtb2RlbCwgdGFzaywgYXBpS2V5LCBzdHJlYW0sIHJlcXVlc3RPcHRpb25zKTtcbiAgICByZXR1cm4ge1xuICAgICAgICB1cmw6IHVybC50b1N0cmluZygpLFxuICAgICAgICBmZXRjaE9wdGlvbnM6IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgYnVpbGRGZXRjaE9wdGlvbnMocmVxdWVzdE9wdGlvbnMpKSwgeyBtZXRob2Q6IFwiUE9TVFwiLCBoZWFkZXJzOiBhd2FpdCBnZXRIZWFkZXJzKHVybCksIGJvZHkgfSksXG4gICAgfTtcbn1cbmFzeW5jIGZ1bmN0aW9uIG1ha2VNb2RlbFJlcXVlc3QobW9kZWwsIHRhc2ssIGFwaUtleSwgc3RyZWFtLCBib2R5LCByZXF1ZXN0T3B0aW9ucyA9IHt9LCBcbi8vIEFsbG93cyB0aGlzIHRvIGJlIHN0dWJiZWQgZm9yIHRlc3RzXG5mZXRjaEZuID0gZmV0Y2gpIHtcbiAgICBjb25zdCB7IHVybCwgZmV0Y2hPcHRpb25zIH0gPSBhd2FpdCBjb25zdHJ1Y3RNb2RlbFJlcXVlc3QobW9kZWwsIHRhc2ssIGFwaUtleSwgc3RyZWFtLCBib2R5LCByZXF1ZXN0T3B0aW9ucyk7XG4gICAgcmV0dXJuIG1ha2VSZXF1ZXN0KHVybCwgZmV0Y2hPcHRpb25zLCBmZXRjaEZuKTtcbn1cbmFzeW5jIGZ1bmN0aW9uIG1ha2VSZXF1ZXN0KHVybCwgZmV0Y2hPcHRpb25zLCBmZXRjaEZuID0gZmV0Y2gpIHtcbiAgICBsZXQgcmVzcG9uc2U7XG4gICAgdHJ5IHtcbiAgICAgICAgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaEZuKHVybCwgZmV0Y2hPcHRpb25zKTtcbiAgICB9XG4gICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgaGFuZGxlUmVzcG9uc2VFcnJvcihlLCB1cmwpO1xuICAgIH1cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGF3YWl0IGhhbmRsZVJlc3BvbnNlTm90T2socmVzcG9uc2UsIHVybCk7XG4gICAgfVxuICAgIHJldHVybiByZXNwb25zZTtcbn1cbmZ1bmN0aW9uIGhhbmRsZVJlc3BvbnNlRXJyb3IoZSwgdXJsKSB7XG4gICAgbGV0IGVyciA9IGU7XG4gICAgaWYgKCEoZSBpbnN0YW5jZW9mIEdvb2dsZUdlbmVyYXRpdmVBSUZldGNoRXJyb3IgfHxcbiAgICAgICAgZSBpbnN0YW5jZW9mIEdvb2dsZUdlbmVyYXRpdmVBSVJlcXVlc3RJbnB1dEVycm9yKSkge1xuICAgICAgICBlcnIgPSBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJRXJyb3IoYEVycm9yIGZldGNoaW5nIGZyb20gJHt1cmwudG9TdHJpbmcoKX06ICR7ZS5tZXNzYWdlfWApO1xuICAgICAgICBlcnIuc3RhY2sgPSBlLnN0YWNrO1xuICAgIH1cbiAgICB0aHJvdyBlcnI7XG59XG5hc3luYyBmdW5jdGlvbiBoYW5kbGVSZXNwb25zZU5vdE9rKHJlc3BvbnNlLCB1cmwpIHtcbiAgICBsZXQgbWVzc2FnZSA9IFwiXCI7XG4gICAgbGV0IGVycm9yRGV0YWlscztcbiAgICB0cnkge1xuICAgICAgICBjb25zdCBqc29uID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBtZXNzYWdlID0ganNvbi5lcnJvci5tZXNzYWdlO1xuICAgICAgICBpZiAoanNvbi5lcnJvci5kZXRhaWxzKSB7XG4gICAgICAgICAgICBtZXNzYWdlICs9IGAgJHtKU09OLnN0cmluZ2lmeShqc29uLmVycm9yLmRldGFpbHMpfWA7XG4gICAgICAgICAgICBlcnJvckRldGFpbHMgPSBqc29uLmVycm9yLmRldGFpbHM7XG4gICAgICAgIH1cbiAgICB9XG4gICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgLy8gaWdub3JlZFxuICAgIH1cbiAgICB0aHJvdyBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJRmV0Y2hFcnJvcihgRXJyb3IgZmV0Y2hpbmcgZnJvbSAke3VybC50b1N0cmluZygpfTogWyR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9XSAke21lc3NhZ2V9YCwgcmVzcG9uc2Uuc3RhdHVzLCByZXNwb25zZS5zdGF0dXNUZXh0LCBlcnJvckRldGFpbHMpO1xufVxuLyoqXG4gKiBHZW5lcmF0ZXMgdGhlIHJlcXVlc3Qgb3B0aW9ucyB0byBiZSBwYXNzZWQgdG8gdGhlIGZldGNoIEFQSS5cbiAqIEBwYXJhbSByZXF1ZXN0T3B0aW9ucyAtIFRoZSB1c2VyLWRlZmluZWQgcmVxdWVzdCBvcHRpb25zLlxuICogQHJldHVybnMgVGhlIGdlbmVyYXRlZCByZXF1ZXN0IG9wdGlvbnMuXG4gKi9cbmZ1bmN0aW9uIGJ1aWxkRmV0Y2hPcHRpb25zKHJlcXVlc3RPcHRpb25zKSB7XG4gICAgY29uc3QgZmV0Y2hPcHRpb25zID0ge307XG4gICAgaWYgKChyZXF1ZXN0T3B0aW9ucyA9PT0gbnVsbCB8fCByZXF1ZXN0T3B0aW9ucyA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVxdWVzdE9wdGlvbnMuc2lnbmFsKSAhPT0gdW5kZWZpbmVkIHx8IChyZXF1ZXN0T3B0aW9ucyA9PT0gbnVsbCB8fCByZXF1ZXN0T3B0aW9ucyA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVxdWVzdE9wdGlvbnMudGltZW91dCkgPj0gMCkge1xuICAgICAgICBjb25zdCBjb250cm9sbGVyID0gbmV3IEFib3J0Q29udHJvbGxlcigpO1xuICAgICAgICBpZiAoKHJlcXVlc3RPcHRpb25zID09PSBudWxsIHx8IHJlcXVlc3RPcHRpb25zID09PSB2b2lkIDAgPyB2b2lkIDAgOiByZXF1ZXN0T3B0aW9ucy50aW1lb3V0KSA+PSAwKSB7XG4gICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IGNvbnRyb2xsZXIuYWJvcnQoKSwgcmVxdWVzdE9wdGlvbnMudGltZW91dCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHJlcXVlc3RPcHRpb25zID09PSBudWxsIHx8IHJlcXVlc3RPcHRpb25zID09PSB2b2lkIDAgPyB2b2lkIDAgOiByZXF1ZXN0T3B0aW9ucy5zaWduYWwpIHtcbiAgICAgICAgICAgIHJlcXVlc3RPcHRpb25zLnNpZ25hbC5hZGRFdmVudExpc3RlbmVyKFwiYWJvcnRcIiwgKCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnRyb2xsZXIuYWJvcnQoKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGZldGNoT3B0aW9ucy5zaWduYWwgPSBjb250cm9sbGVyLnNpZ25hbDtcbiAgICB9XG4gICAgcmV0dXJuIGZldGNoT3B0aW9ucztcbn1cblxuLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IDIwMjQgR29vZ2xlIExMQ1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8qKlxuICogQWRkcyBjb252ZW5pZW5jZSBoZWxwZXIgbWV0aG9kcyB0byBhIHJlc3BvbnNlIG9iamVjdCwgaW5jbHVkaW5nIHN0cmVhbVxuICogY2h1bmtzIChhcyBsb25nIGFzIGVhY2ggY2h1bmsgaXMgYSBjb21wbGV0ZSBHZW5lcmF0ZUNvbnRlbnRSZXNwb25zZSBKU09OKS5cbiAqL1xuZnVuY3Rpb24gYWRkSGVscGVycyhyZXNwb25zZSkge1xuICAgIHJlc3BvbnNlLnRleHQgPSAoKSA9PiB7XG4gICAgICAgIGlmIChyZXNwb25zZS5jYW5kaWRhdGVzICYmIHJlc3BvbnNlLmNhbmRpZGF0ZXMubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNhbmRpZGF0ZXMubGVuZ3RoID4gMSkge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihgVGhpcyByZXNwb25zZSBoYWQgJHtyZXNwb25zZS5jYW5kaWRhdGVzLmxlbmd0aH0gYCArXG4gICAgICAgICAgICAgICAgICAgIGBjYW5kaWRhdGVzLiBSZXR1cm5pbmcgdGV4dCBmcm9tIHRoZSBmaXJzdCBjYW5kaWRhdGUgb25seS4gYCArXG4gICAgICAgICAgICAgICAgICAgIGBBY2Nlc3MgcmVzcG9uc2UuY2FuZGlkYXRlcyBkaXJlY3RseSB0byB1c2UgdGhlIG90aGVyIGNhbmRpZGF0ZXMuYCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaGFkQmFkRmluaXNoUmVhc29uKHJlc3BvbnNlLmNhbmRpZGF0ZXNbMF0pKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEdvb2dsZUdlbmVyYXRpdmVBSVJlc3BvbnNlRXJyb3IoYCR7Zm9ybWF0QmxvY2tFcnJvck1lc3NhZ2UocmVzcG9uc2UpfWAsIHJlc3BvbnNlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBnZXRUZXh0KHJlc3BvbnNlKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChyZXNwb25zZS5wcm9tcHRGZWVkYmFjaykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEdvb2dsZUdlbmVyYXRpdmVBSVJlc3BvbnNlRXJyb3IoYFRleHQgbm90IGF2YWlsYWJsZS4gJHtmb3JtYXRCbG9ja0Vycm9yTWVzc2FnZShyZXNwb25zZSl9YCwgcmVzcG9uc2UpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBcIlwiO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogVE9ETzogcmVtb3ZlIGF0IG5leHQgbWFqb3IgdmVyc2lvblxuICAgICAqL1xuICAgIHJlc3BvbnNlLmZ1bmN0aW9uQ2FsbCA9ICgpID0+IHtcbiAgICAgICAgaWYgKHJlc3BvbnNlLmNhbmRpZGF0ZXMgJiYgcmVzcG9uc2UuY2FuZGlkYXRlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICBpZiAocmVzcG9uc2UuY2FuZGlkYXRlcy5sZW5ndGggPiAxKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKGBUaGlzIHJlc3BvbnNlIGhhZCAke3Jlc3BvbnNlLmNhbmRpZGF0ZXMubGVuZ3RofSBgICtcbiAgICAgICAgICAgICAgICAgICAgYGNhbmRpZGF0ZXMuIFJldHVybmluZyBmdW5jdGlvbiBjYWxscyBmcm9tIHRoZSBmaXJzdCBjYW5kaWRhdGUgb25seS4gYCArXG4gICAgICAgICAgICAgICAgICAgIGBBY2Nlc3MgcmVzcG9uc2UuY2FuZGlkYXRlcyBkaXJlY3RseSB0byB1c2UgdGhlIG90aGVyIGNhbmRpZGF0ZXMuYCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaGFkQmFkRmluaXNoUmVhc29uKHJlc3BvbnNlLmNhbmRpZGF0ZXNbMF0pKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEdvb2dsZUdlbmVyYXRpdmVBSVJlc3BvbnNlRXJyb3IoYCR7Zm9ybWF0QmxvY2tFcnJvck1lc3NhZ2UocmVzcG9uc2UpfWAsIHJlc3BvbnNlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnNvbGUud2FybihgcmVzcG9uc2UuZnVuY3Rpb25DYWxsKCkgaXMgZGVwcmVjYXRlZC4gYCArXG4gICAgICAgICAgICAgICAgYFVzZSByZXNwb25zZS5mdW5jdGlvbkNhbGxzKCkgaW5zdGVhZC5gKTtcbiAgICAgICAgICAgIHJldHVybiBnZXRGdW5jdGlvbkNhbGxzKHJlc3BvbnNlKVswXTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChyZXNwb25zZS5wcm9tcHRGZWVkYmFjaykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEdvb2dsZUdlbmVyYXRpdmVBSVJlc3BvbnNlRXJyb3IoYEZ1bmN0aW9uIGNhbGwgbm90IGF2YWlsYWJsZS4gJHtmb3JtYXRCbG9ja0Vycm9yTWVzc2FnZShyZXNwb25zZSl9YCwgcmVzcG9uc2UpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfTtcbiAgICByZXNwb25zZS5mdW5jdGlvbkNhbGxzID0gKCkgPT4ge1xuICAgICAgICBpZiAocmVzcG9uc2UuY2FuZGlkYXRlcyAmJiByZXNwb25zZS5jYW5kaWRhdGVzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIGlmIChyZXNwb25zZS5jYW5kaWRhdGVzLmxlbmd0aCA+IDEpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oYFRoaXMgcmVzcG9uc2UgaGFkICR7cmVzcG9uc2UuY2FuZGlkYXRlcy5sZW5ndGh9IGAgK1xuICAgICAgICAgICAgICAgICAgICBgY2FuZGlkYXRlcy4gUmV0dXJuaW5nIGZ1bmN0aW9uIGNhbGxzIGZyb20gdGhlIGZpcnN0IGNhbmRpZGF0ZSBvbmx5LiBgICtcbiAgICAgICAgICAgICAgICAgICAgYEFjY2VzcyByZXNwb25zZS5jYW5kaWRhdGVzIGRpcmVjdGx5IHRvIHVzZSB0aGUgb3RoZXIgY2FuZGlkYXRlcy5gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChoYWRCYWRGaW5pc2hSZWFzb24ocmVzcG9uc2UuY2FuZGlkYXRlc1swXSkpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJUmVzcG9uc2VFcnJvcihgJHtmb3JtYXRCbG9ja0Vycm9yTWVzc2FnZShyZXNwb25zZSl9YCwgcmVzcG9uc2UpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGdldEZ1bmN0aW9uQ2FsbHMocmVzcG9uc2UpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHJlc3BvbnNlLnByb21wdEZlZWRiYWNrKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJUmVzcG9uc2VFcnJvcihgRnVuY3Rpb24gY2FsbCBub3QgYXZhaWxhYmxlLiAke2Zvcm1hdEJsb2NrRXJyb3JNZXNzYWdlKHJlc3BvbnNlKX1gLCByZXNwb25zZSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9O1xuICAgIHJldHVybiByZXNwb25zZTtcbn1cbi8qKlxuICogUmV0dXJucyBhbGwgdGV4dCBmb3VuZCBpbiBhbGwgcGFydHMgb2YgZmlyc3QgY2FuZGlkYXRlLlxuICovXG5mdW5jdGlvbiBnZXRUZXh0KHJlc3BvbnNlKSB7XG4gICAgdmFyIF9hLCBfYiwgX2MsIF9kO1xuICAgIGNvbnN0IHRleHRTdHJpbmdzID0gW107XG4gICAgaWYgKChfYiA9IChfYSA9IHJlc3BvbnNlLmNhbmRpZGF0ZXMpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYVswXS5jb250ZW50KSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2IucGFydHMpIHtcbiAgICAgICAgZm9yIChjb25zdCBwYXJ0IG9mIChfZCA9IChfYyA9IHJlc3BvbnNlLmNhbmRpZGF0ZXMpID09PSBudWxsIHx8IF9jID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfY1swXS5jb250ZW50KSA9PT0gbnVsbCB8fCBfZCA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2QucGFydHMpIHtcbiAgICAgICAgICAgIGlmIChwYXJ0LnRleHQpIHtcbiAgICAgICAgICAgICAgICB0ZXh0U3RyaW5ncy5wdXNoKHBhcnQudGV4dCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAocGFydC5leGVjdXRhYmxlQ29kZSkge1xuICAgICAgICAgICAgICAgIHRleHRTdHJpbmdzLnB1c2goXCJcXG5gYGBcIiArXG4gICAgICAgICAgICAgICAgICAgIHBhcnQuZXhlY3V0YWJsZUNvZGUubGFuZ3VhZ2UgK1xuICAgICAgICAgICAgICAgICAgICBcIlxcblwiICtcbiAgICAgICAgICAgICAgICAgICAgcGFydC5leGVjdXRhYmxlQ29kZS5jb2RlICtcbiAgICAgICAgICAgICAgICAgICAgXCJcXG5gYGBcXG5cIik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAocGFydC5jb2RlRXhlY3V0aW9uUmVzdWx0KSB7XG4gICAgICAgICAgICAgICAgdGV4dFN0cmluZ3MucHVzaChcIlxcbmBgYFxcblwiICsgcGFydC5jb2RlRXhlY3V0aW9uUmVzdWx0Lm91dHB1dCArIFwiXFxuYGBgXFxuXCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGlmICh0ZXh0U3RyaW5ncy5sZW5ndGggPiAwKSB7XG4gICAgICAgIHJldHVybiB0ZXh0U3RyaW5ncy5qb2luKFwiXCIpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIFwiXCI7XG4gICAgfVxufVxuLyoqXG4gKiBSZXR1cm5zIGZ1bmN0aW9uQ2FsbCBvZiBmaXJzdCBjYW5kaWRhdGUuXG4gKi9cbmZ1bmN0aW9uIGdldEZ1bmN0aW9uQ2FsbHMocmVzcG9uc2UpIHtcbiAgICB2YXIgX2EsIF9iLCBfYywgX2Q7XG4gICAgY29uc3QgZnVuY3Rpb25DYWxscyA9IFtdO1xuICAgIGlmICgoX2IgPSAoX2EgPSByZXNwb25zZS5jYW5kaWRhdGVzKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2FbMF0uY29udGVudCkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLnBhcnRzKSB7XG4gICAgICAgIGZvciAoY29uc3QgcGFydCBvZiAoX2QgPSAoX2MgPSByZXNwb25zZS5jYW5kaWRhdGVzKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2NbMF0uY29udGVudCkgPT09IG51bGwgfHwgX2QgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9kLnBhcnRzKSB7XG4gICAgICAgICAgICBpZiAocGFydC5mdW5jdGlvbkNhbGwpIHtcbiAgICAgICAgICAgICAgICBmdW5jdGlvbkNhbGxzLnB1c2gocGFydC5mdW5jdGlvbkNhbGwpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGlmIChmdW5jdGlvbkNhbGxzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgcmV0dXJuIGZ1bmN0aW9uQ2FsbHM7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgIH1cbn1cbmNvbnN0IGJhZEZpbmlzaFJlYXNvbnMgPSBbXG4gICAgZXhwb3J0cy5GaW5pc2hSZWFzb24uUkVDSVRBVElPTixcbiAgICBleHBvcnRzLkZpbmlzaFJlYXNvbi5TQUZFVFksXG4gICAgZXhwb3J0cy5GaW5pc2hSZWFzb24uTEFOR1VBR0UsXG5dO1xuZnVuY3Rpb24gaGFkQmFkRmluaXNoUmVhc29uKGNhbmRpZGF0ZSkge1xuICAgIHJldHVybiAoISFjYW5kaWRhdGUuZmluaXNoUmVhc29uICYmXG4gICAgICAgIGJhZEZpbmlzaFJlYXNvbnMuaW5jbHVkZXMoY2FuZGlkYXRlLmZpbmlzaFJlYXNvbikpO1xufVxuZnVuY3Rpb24gZm9ybWF0QmxvY2tFcnJvck1lc3NhZ2UocmVzcG9uc2UpIHtcbiAgICB2YXIgX2EsIF9iLCBfYztcbiAgICBsZXQgbWVzc2FnZSA9IFwiXCI7XG4gICAgaWYgKCghcmVzcG9uc2UuY2FuZGlkYXRlcyB8fCByZXNwb25zZS5jYW5kaWRhdGVzLmxlbmd0aCA9PT0gMCkgJiZcbiAgICAgICAgcmVzcG9uc2UucHJvbXB0RmVlZGJhY2spIHtcbiAgICAgICAgbWVzc2FnZSArPSBcIlJlc3BvbnNlIHdhcyBibG9ja2VkXCI7XG4gICAgICAgIGlmICgoX2EgPSByZXNwb25zZS5wcm9tcHRGZWVkYmFjaykgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmJsb2NrUmVhc29uKSB7XG4gICAgICAgICAgICBtZXNzYWdlICs9IGAgZHVlIHRvICR7cmVzcG9uc2UucHJvbXB0RmVlZGJhY2suYmxvY2tSZWFzb259YDtcbiAgICAgICAgfVxuICAgICAgICBpZiAoKF9iID0gcmVzcG9uc2UucHJvbXB0RmVlZGJhY2spID09PSBudWxsIHx8IF9iID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYi5ibG9ja1JlYXNvbk1lc3NhZ2UpIHtcbiAgICAgICAgICAgIG1lc3NhZ2UgKz0gYDogJHtyZXNwb25zZS5wcm9tcHRGZWVkYmFjay5ibG9ja1JlYXNvbk1lc3NhZ2V9YDtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIGlmICgoX2MgPSByZXNwb25zZS5jYW5kaWRhdGVzKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2NbMF0pIHtcbiAgICAgICAgY29uc3QgZmlyc3RDYW5kaWRhdGUgPSByZXNwb25zZS5jYW5kaWRhdGVzWzBdO1xuICAgICAgICBpZiAoaGFkQmFkRmluaXNoUmVhc29uKGZpcnN0Q2FuZGlkYXRlKSkge1xuICAgICAgICAgICAgbWVzc2FnZSArPSBgQ2FuZGlkYXRlIHdhcyBibG9ja2VkIGR1ZSB0byAke2ZpcnN0Q2FuZGlkYXRlLmZpbmlzaFJlYXNvbn1gO1xuICAgICAgICAgICAgaWYgKGZpcnN0Q2FuZGlkYXRlLmZpbmlzaE1lc3NhZ2UpIHtcbiAgICAgICAgICAgICAgICBtZXNzYWdlICs9IGA6ICR7Zmlyc3RDYW5kaWRhdGUuZmluaXNoTWVzc2FnZX1gO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBtZXNzYWdlO1xufVxuXG4vKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqXHJcbkNvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLlxyXG5cclxuUGVybWlzc2lvbiB0byB1c2UsIGNvcHksIG1vZGlmeSwgYW5kL29yIGRpc3RyaWJ1dGUgdGhpcyBzb2Z0d2FyZSBmb3IgYW55XHJcbnB1cnBvc2Ugd2l0aCBvciB3aXRob3V0IGZlZSBpcyBoZXJlYnkgZ3JhbnRlZC5cclxuXHJcblRIRSBTT0ZUV0FSRSBJUyBQUk9WSURFRCBcIkFTIElTXCIgQU5EIFRIRSBBVVRIT1IgRElTQ0xBSU1TIEFMTCBXQVJSQU5USUVTIFdJVEhcclxuUkVHQVJEIFRPIFRISVMgU09GVFdBUkUgSU5DTFVESU5HIEFMTCBJTVBMSUVEIFdBUlJBTlRJRVMgT0YgTUVSQ0hBTlRBQklMSVRZXHJcbkFORCBGSVRORVNTLiBJTiBOTyBFVkVOVCBTSEFMTCBUSEUgQVVUSE9SIEJFIExJQUJMRSBGT1IgQU5ZIFNQRUNJQUwsIERJUkVDVCxcclxuSU5ESVJFQ1QsIE9SIENPTlNFUVVFTlRJQUwgREFNQUdFUyBPUiBBTlkgREFNQUdFUyBXSEFUU09FVkVSIFJFU1VMVElORyBGUk9NXHJcbkxPU1MgT0YgVVNFLCBEQVRBIE9SIFBST0ZJVFMsIFdIRVRIRVIgSU4gQU4gQUNUSU9OIE9GIENPTlRSQUNULCBORUdMSUdFTkNFIE9SXHJcbk9USEVSIFRPUlRJT1VTIEFDVElPTiwgQVJJU0lORyBPVVQgT0YgT1IgSU4gQ09OTkVDVElPTiBXSVRIIFRIRSBVU0UgT1JcclxuUEVSRk9STUFOQ0UgT0YgVEhJUyBTT0ZUV0FSRS5cclxuKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKiogKi9cclxuLyogZ2xvYmFsIFJlZmxlY3QsIFByb21pc2UsIFN1cHByZXNzZWRFcnJvciwgU3ltYm9sICovXHJcblxyXG5cclxuZnVuY3Rpb24gX19hd2FpdCh2KSB7XHJcbiAgICByZXR1cm4gdGhpcyBpbnN0YW5jZW9mIF9fYXdhaXQgPyAodGhpcy52ID0gdiwgdGhpcykgOiBuZXcgX19hd2FpdCh2KTtcclxufVxyXG5cclxuZnVuY3Rpb24gX19hc3luY0dlbmVyYXRvcih0aGlzQXJnLCBfYXJndW1lbnRzLCBnZW5lcmF0b3IpIHtcclxuICAgIGlmICghU3ltYm9sLmFzeW5jSXRlcmF0b3IpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJTeW1ib2wuYXN5bmNJdGVyYXRvciBpcyBub3QgZGVmaW5lZC5cIik7XHJcbiAgICB2YXIgZyA9IGdlbmVyYXRvci5hcHBseSh0aGlzQXJnLCBfYXJndW1lbnRzIHx8IFtdKSwgaSwgcSA9IFtdO1xyXG4gICAgcmV0dXJuIGkgPSB7fSwgdmVyYihcIm5leHRcIiksIHZlcmIoXCJ0aHJvd1wiKSwgdmVyYihcInJldHVyblwiKSwgaVtTeW1ib2wuYXN5bmNJdGVyYXRvcl0gPSBmdW5jdGlvbiAoKSB7IHJldHVybiB0aGlzOyB9LCBpO1xyXG4gICAgZnVuY3Rpb24gdmVyYihuKSB7IGlmIChnW25dKSBpW25dID0gZnVuY3Rpb24gKHYpIHsgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChhLCBiKSB7IHEucHVzaChbbiwgdiwgYSwgYl0pID4gMSB8fCByZXN1bWUobiwgdik7IH0pOyB9OyB9XHJcbiAgICBmdW5jdGlvbiByZXN1bWUobiwgdikgeyB0cnkgeyBzdGVwKGdbbl0odikpOyB9IGNhdGNoIChlKSB7IHNldHRsZShxWzBdWzNdLCBlKTsgfSB9XHJcbiAgICBmdW5jdGlvbiBzdGVwKHIpIHsgci52YWx1ZSBpbnN0YW5jZW9mIF9fYXdhaXQgPyBQcm9taXNlLnJlc29sdmUoci52YWx1ZS52KS50aGVuKGZ1bGZpbGwsIHJlamVjdCkgOiBzZXR0bGUocVswXVsyXSwgcik7IH1cclxuICAgIGZ1bmN0aW9uIGZ1bGZpbGwodmFsdWUpIHsgcmVzdW1lKFwibmV4dFwiLCB2YWx1ZSk7IH1cclxuICAgIGZ1bmN0aW9uIHJlamVjdCh2YWx1ZSkgeyByZXN1bWUoXCJ0aHJvd1wiLCB2YWx1ZSk7IH1cclxuICAgIGZ1bmN0aW9uIHNldHRsZShmLCB2KSB7IGlmIChmKHYpLCBxLnNoaWZ0KCksIHEubGVuZ3RoKSByZXN1bWUocVswXVswXSwgcVswXVsxXSk7IH1cclxufVxyXG5cclxudHlwZW9mIFN1cHByZXNzZWRFcnJvciA9PT0gXCJmdW5jdGlvblwiID8gU3VwcHJlc3NlZEVycm9yIDogZnVuY3Rpb24gKGVycm9yLCBzdXBwcmVzc2VkLCBtZXNzYWdlKSB7XHJcbiAgICB2YXIgZSA9IG5ldyBFcnJvcihtZXNzYWdlKTtcclxuICAgIHJldHVybiBlLm5hbWUgPSBcIlN1cHByZXNzZWRFcnJvclwiLCBlLmVycm9yID0gZXJyb3IsIGUuc3VwcHJlc3NlZCA9IHN1cHByZXNzZWQsIGU7XHJcbn07XG5cbi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCAyMDI0IEdvb2dsZSBMTENcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5jb25zdCByZXNwb25zZUxpbmVSRSA9IC9eZGF0YVxcOiAoLiopKD86XFxuXFxufFxcclxccnxcXHJcXG5cXHJcXG4pLztcbi8qKlxuICogUHJvY2VzcyBhIHJlc3BvbnNlLmJvZHkgc3RyZWFtIGZyb20gdGhlIGJhY2tlbmQgYW5kIHJldHVybiBhblxuICogaXRlcmF0b3IgdGhhdCBwcm92aWRlcyBvbmUgY29tcGxldGUgR2VuZXJhdGVDb250ZW50UmVzcG9uc2UgYXQgYSB0aW1lXG4gKiBhbmQgYSBwcm9taXNlIHRoYXQgcmVzb2x2ZXMgd2l0aCBhIHNpbmdsZSBhZ2dyZWdhdGVkXG4gKiBHZW5lcmF0ZUNvbnRlbnRSZXNwb25zZS5cbiAqXG4gKiBAcGFyYW0gcmVzcG9uc2UgLSBSZXNwb25zZSBmcm9tIGEgZmV0Y2ggY2FsbFxuICovXG5mdW5jdGlvbiBwcm9jZXNzU3RyZWFtKHJlc3BvbnNlKSB7XG4gICAgY29uc3QgaW5wdXRTdHJlYW0gPSByZXNwb25zZS5ib2R5LnBpcGVUaHJvdWdoKG5ldyBUZXh0RGVjb2RlclN0cmVhbShcInV0ZjhcIiwgeyBmYXRhbDogdHJ1ZSB9KSk7XG4gICAgY29uc3QgcmVzcG9uc2VTdHJlYW0gPSBnZXRSZXNwb25zZVN0cmVhbShpbnB1dFN0cmVhbSk7XG4gICAgY29uc3QgW3N0cmVhbTEsIHN0cmVhbTJdID0gcmVzcG9uc2VTdHJlYW0udGVlKCk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgc3RyZWFtOiBnZW5lcmF0ZVJlc3BvbnNlU2VxdWVuY2Uoc3RyZWFtMSksXG4gICAgICAgIHJlc3BvbnNlOiBnZXRSZXNwb25zZVByb21pc2Uoc3RyZWFtMiksXG4gICAgfTtcbn1cbmFzeW5jIGZ1bmN0aW9uIGdldFJlc3BvbnNlUHJvbWlzZShzdHJlYW0pIHtcbiAgICBjb25zdCBhbGxSZXNwb25zZXMgPSBbXTtcbiAgICBjb25zdCByZWFkZXIgPSBzdHJlYW0uZ2V0UmVhZGVyKCk7XG4gICAgd2hpbGUgKHRydWUpIHtcbiAgICAgICAgY29uc3QgeyBkb25lLCB2YWx1ZSB9ID0gYXdhaXQgcmVhZGVyLnJlYWQoKTtcbiAgICAgICAgaWYgKGRvbmUpIHtcbiAgICAgICAgICAgIHJldHVybiBhZGRIZWxwZXJzKGFnZ3JlZ2F0ZVJlc3BvbnNlcyhhbGxSZXNwb25zZXMpKTtcbiAgICAgICAgfVxuICAgICAgICBhbGxSZXNwb25zZXMucHVzaCh2YWx1ZSk7XG4gICAgfVxufVxuZnVuY3Rpb24gZ2VuZXJhdGVSZXNwb25zZVNlcXVlbmNlKHN0cmVhbSkge1xuICAgIHJldHVybiBfX2FzeW5jR2VuZXJhdG9yKHRoaXMsIGFyZ3VtZW50cywgZnVuY3Rpb24qIGdlbmVyYXRlUmVzcG9uc2VTZXF1ZW5jZV8xKCkge1xuICAgICAgICBjb25zdCByZWFkZXIgPSBzdHJlYW0uZ2V0UmVhZGVyKCk7XG4gICAgICAgIHdoaWxlICh0cnVlKSB7XG4gICAgICAgICAgICBjb25zdCB7IHZhbHVlLCBkb25lIH0gPSB5aWVsZCBfX2F3YWl0KHJlYWRlci5yZWFkKCkpO1xuICAgICAgICAgICAgaWYgKGRvbmUpIHtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHlpZWxkIHlpZWxkIF9fYXdhaXQoYWRkSGVscGVycyh2YWx1ZSkpO1xuICAgICAgICB9XG4gICAgfSk7XG59XG4vKipcbiAqIFJlYWRzIGEgcmF3IHN0cmVhbSBmcm9tIHRoZSBmZXRjaCByZXNwb25zZSBhbmQgam9pbiBpbmNvbXBsZXRlXG4gKiBjaHVua3MsIHJldHVybmluZyBhIG5ldyBzdHJlYW0gdGhhdCBwcm92aWRlcyBhIHNpbmdsZSBjb21wbGV0ZVxuICogR2VuZXJhdGVDb250ZW50UmVzcG9uc2UgaW4gZWFjaCBpdGVyYXRpb24uXG4gKi9cbmZ1bmN0aW9uIGdldFJlc3BvbnNlU3RyZWFtKGlucHV0U3RyZWFtKSB7XG4gICAgY29uc3QgcmVhZGVyID0gaW5wdXRTdHJlYW0uZ2V0UmVhZGVyKCk7XG4gICAgY29uc3Qgc3RyZWFtID0gbmV3IFJlYWRhYmxlU3RyZWFtKHtcbiAgICAgICAgc3RhcnQoY29udHJvbGxlcikge1xuICAgICAgICAgICAgbGV0IGN1cnJlbnRUZXh0ID0gXCJcIjtcbiAgICAgICAgICAgIHJldHVybiBwdW1wKCk7XG4gICAgICAgICAgICBmdW5jdGlvbiBwdW1wKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiByZWFkZXIucmVhZCgpLnRoZW4oKHsgdmFsdWUsIGRvbmUgfSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBpZiAoZG9uZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGN1cnJlbnRUZXh0LnRyaW0oKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRyb2xsZXIuZXJyb3IobmV3IEdvb2dsZUdlbmVyYXRpdmVBSUVycm9yKFwiRmFpbGVkIHRvIHBhcnNlIHN0cmVhbVwiKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgY29udHJvbGxlci5jbG9zZSgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRUZXh0ICs9IHZhbHVlO1xuICAgICAgICAgICAgICAgICAgICBsZXQgbWF0Y2ggPSBjdXJyZW50VGV4dC5tYXRjaChyZXNwb25zZUxpbmVSRSk7XG4gICAgICAgICAgICAgICAgICAgIGxldCBwYXJzZWRSZXNwb25zZTtcbiAgICAgICAgICAgICAgICAgICAgd2hpbGUgKG1hdGNoKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhcnNlZFJlc3BvbnNlID0gSlNPTi5wYXJzZShtYXRjaFsxXSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRyb2xsZXIuZXJyb3IobmV3IEdvb2dsZUdlbmVyYXRpdmVBSUVycm9yKGBFcnJvciBwYXJzaW5nIEpTT04gcmVzcG9uc2U6IFwiJHttYXRjaFsxXX1cImApKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjb250cm9sbGVyLmVucXVldWUocGFyc2VkUmVzcG9uc2UpO1xuICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudFRleHQgPSBjdXJyZW50VGV4dC5zdWJzdHJpbmcobWF0Y2hbMF0ubGVuZ3RoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIG1hdGNoID0gY3VycmVudFRleHQubWF0Y2gocmVzcG9uc2VMaW5lUkUpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBwdW1wKCk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgfSk7XG4gICAgcmV0dXJuIHN0cmVhbTtcbn1cbi8qKlxuICogQWdncmVnYXRlcyBhbiBhcnJheSBvZiBgR2VuZXJhdGVDb250ZW50UmVzcG9uc2VgcyBpbnRvIGEgc2luZ2xlXG4gKiBHZW5lcmF0ZUNvbnRlbnRSZXNwb25zZS5cbiAqL1xuZnVuY3Rpb24gYWdncmVnYXRlUmVzcG9uc2VzKHJlc3BvbnNlcykge1xuICAgIGNvbnN0IGxhc3RSZXNwb25zZSA9IHJlc3BvbnNlc1tyZXNwb25zZXMubGVuZ3RoIC0gMV07XG4gICAgY29uc3QgYWdncmVnYXRlZFJlc3BvbnNlID0ge1xuICAgICAgICBwcm9tcHRGZWVkYmFjazogbGFzdFJlc3BvbnNlID09PSBudWxsIHx8IGxhc3RSZXNwb25zZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogbGFzdFJlc3BvbnNlLnByb21wdEZlZWRiYWNrLFxuICAgIH07XG4gICAgZm9yIChjb25zdCByZXNwb25zZSBvZiByZXNwb25zZXMpIHtcbiAgICAgICAgaWYgKHJlc3BvbnNlLmNhbmRpZGF0ZXMpIHtcbiAgICAgICAgICAgIGZvciAoY29uc3QgY2FuZGlkYXRlIG9mIHJlc3BvbnNlLmNhbmRpZGF0ZXMpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBpID0gY2FuZGlkYXRlLmluZGV4O1xuICAgICAgICAgICAgICAgIGlmICghYWdncmVnYXRlZFJlc3BvbnNlLmNhbmRpZGF0ZXMpIHtcbiAgICAgICAgICAgICAgICAgICAgYWdncmVnYXRlZFJlc3BvbnNlLmNhbmRpZGF0ZXMgPSBbXTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKCFhZ2dyZWdhdGVkUmVzcG9uc2UuY2FuZGlkYXRlc1tpXSkge1xuICAgICAgICAgICAgICAgICAgICBhZ2dyZWdhdGVkUmVzcG9uc2UuY2FuZGlkYXRlc1tpXSA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4OiBjYW5kaWRhdGUuaW5kZXgsXG4gICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIEtlZXAgb3ZlcndyaXRpbmcsIHRoZSBsYXN0IG9uZSB3aWxsIGJlIGZpbmFsXG4gICAgICAgICAgICAgICAgYWdncmVnYXRlZFJlc3BvbnNlLmNhbmRpZGF0ZXNbaV0uY2l0YXRpb25NZXRhZGF0YSA9XG4gICAgICAgICAgICAgICAgICAgIGNhbmRpZGF0ZS5jaXRhdGlvbk1ldGFkYXRhO1xuICAgICAgICAgICAgICAgIGFnZ3JlZ2F0ZWRSZXNwb25zZS5jYW5kaWRhdGVzW2ldLmdyb3VuZGluZ01ldGFkYXRhID1cbiAgICAgICAgICAgICAgICAgICAgY2FuZGlkYXRlLmdyb3VuZGluZ01ldGFkYXRhO1xuICAgICAgICAgICAgICAgIGFnZ3JlZ2F0ZWRSZXNwb25zZS5jYW5kaWRhdGVzW2ldLmZpbmlzaFJlYXNvbiA9IGNhbmRpZGF0ZS5maW5pc2hSZWFzb247XG4gICAgICAgICAgICAgICAgYWdncmVnYXRlZFJlc3BvbnNlLmNhbmRpZGF0ZXNbaV0uZmluaXNoTWVzc2FnZSA9XG4gICAgICAgICAgICAgICAgICAgIGNhbmRpZGF0ZS5maW5pc2hNZXNzYWdlO1xuICAgICAgICAgICAgICAgIGFnZ3JlZ2F0ZWRSZXNwb25zZS5jYW5kaWRhdGVzW2ldLnNhZmV0eVJhdGluZ3MgPVxuICAgICAgICAgICAgICAgICAgICBjYW5kaWRhdGUuc2FmZXR5UmF0aW5ncztcbiAgICAgICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAgICAgKiBDYW5kaWRhdGVzIHNob3VsZCBhbHdheXMgaGF2ZSBjb250ZW50IGFuZCBwYXJ0cywgYnV0IHRoaXMgaGFuZGxlc1xuICAgICAgICAgICAgICAgICAqIHBvc3NpYmxlIG1hbGZvcm1lZCByZXNwb25zZXMuXG4gICAgICAgICAgICAgICAgICovXG4gICAgICAgICAgICAgICAgaWYgKGNhbmRpZGF0ZS5jb250ZW50ICYmIGNhbmRpZGF0ZS5jb250ZW50LnBhcnRzKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmICghYWdncmVnYXRlZFJlc3BvbnNlLmNhbmRpZGF0ZXNbaV0uY29udGVudCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgYWdncmVnYXRlZFJlc3BvbnNlLmNhbmRpZGF0ZXNbaV0uY29udGVudCA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByb2xlOiBjYW5kaWRhdGUuY29udGVudC5yb2xlIHx8IFwidXNlclwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhcnRzOiBbXSxcbiAgICAgICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV3UGFydCA9IHt9O1xuICAgICAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IHBhcnQgb2YgY2FuZGlkYXRlLmNvbnRlbnQucGFydHMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChwYXJ0LnRleHQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuZXdQYXJ0LnRleHQgPSBwYXJ0LnRleHQ7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAocGFydC5mdW5jdGlvbkNhbGwpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuZXdQYXJ0LmZ1bmN0aW9uQ2FsbCA9IHBhcnQuZnVuY3Rpb25DYWxsO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHBhcnQuZXhlY3V0YWJsZUNvZGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuZXdQYXJ0LmV4ZWN1dGFibGVDb2RlID0gcGFydC5leGVjdXRhYmxlQ29kZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChwYXJ0LmNvZGVFeGVjdXRpb25SZXN1bHQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuZXdQYXJ0LmNvZGVFeGVjdXRpb25SZXN1bHQgPSBwYXJ0LmNvZGVFeGVjdXRpb25SZXN1bHQ7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoT2JqZWN0LmtleXMobmV3UGFydCkubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3UGFydC50ZXh0ID0gXCJcIjtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGFnZ3JlZ2F0ZWRSZXNwb25zZS5jYW5kaWRhdGVzW2ldLmNvbnRlbnQucGFydHMucHVzaChuZXdQYXJ0KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAocmVzcG9uc2UudXNhZ2VNZXRhZGF0YSkge1xuICAgICAgICAgICAgYWdncmVnYXRlZFJlc3BvbnNlLnVzYWdlTWV0YWRhdGEgPSByZXNwb25zZS51c2FnZU1ldGFkYXRhO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBhZ2dyZWdhdGVkUmVzcG9uc2U7XG59XG5cbi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCAyMDI0IEdvb2dsZSBMTENcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5hc3luYyBmdW5jdGlvbiBnZW5lcmF0ZUNvbnRlbnRTdHJlYW0oYXBpS2V5LCBtb2RlbCwgcGFyYW1zLCByZXF1ZXN0T3B0aW9ucykge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbWFrZU1vZGVsUmVxdWVzdChtb2RlbCwgVGFzay5TVFJFQU1fR0VORVJBVEVfQ09OVEVOVCwgYXBpS2V5LCBcbiAgICAvKiBzdHJlYW0gKi8gdHJ1ZSwgSlNPTi5zdHJpbmdpZnkocGFyYW1zKSwgcmVxdWVzdE9wdGlvbnMpO1xuICAgIHJldHVybiBwcm9jZXNzU3RyZWFtKHJlc3BvbnNlKTtcbn1cbmFzeW5jIGZ1bmN0aW9uIGdlbmVyYXRlQ29udGVudChhcGlLZXksIG1vZGVsLCBwYXJhbXMsIHJlcXVlc3RPcHRpb25zKSB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBtYWtlTW9kZWxSZXF1ZXN0KG1vZGVsLCBUYXNrLkdFTkVSQVRFX0NPTlRFTlQsIGFwaUtleSwgXG4gICAgLyogc3RyZWFtICovIGZhbHNlLCBKU09OLnN0cmluZ2lmeShwYXJhbXMpLCByZXF1ZXN0T3B0aW9ucyk7XG4gICAgY29uc3QgcmVzcG9uc2VKc29uID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgIGNvbnN0IGVuaGFuY2VkUmVzcG9uc2UgPSBhZGRIZWxwZXJzKHJlc3BvbnNlSnNvbik7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgcmVzcG9uc2U6IGVuaGFuY2VkUmVzcG9uc2UsXG4gICAgfTtcbn1cblxuLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IDIwMjQgR29vZ2xlIExMQ1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbmZ1bmN0aW9uIGZvcm1hdFN5c3RlbUluc3RydWN0aW9uKGlucHV0KSB7XG4gICAgLy8gbnVsbCBvciB1bmRlZmluZWRcbiAgICBpZiAoaW5wdXQgPT0gbnVsbCkge1xuICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgIH1cbiAgICBlbHNlIGlmICh0eXBlb2YgaW5wdXQgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgcmV0dXJuIHsgcm9sZTogXCJzeXN0ZW1cIiwgcGFydHM6IFt7IHRleHQ6IGlucHV0IH1dIH07XG4gICAgfVxuICAgIGVsc2UgaWYgKGlucHV0LnRleHQpIHtcbiAgICAgICAgcmV0dXJuIHsgcm9sZTogXCJzeXN0ZW1cIiwgcGFydHM6IFtpbnB1dF0gfTtcbiAgICB9XG4gICAgZWxzZSBpZiAoaW5wdXQucGFydHMpIHtcbiAgICAgICAgaWYgKCFpbnB1dC5yb2xlKSB7XG4gICAgICAgICAgICByZXR1cm4geyByb2xlOiBcInN5c3RlbVwiLCBwYXJ0czogaW5wdXQucGFydHMgfTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiBpbnB1dDtcbiAgICAgICAgfVxuICAgIH1cbn1cbmZ1bmN0aW9uIGZvcm1hdE5ld0NvbnRlbnQocmVxdWVzdCkge1xuICAgIGxldCBuZXdQYXJ0cyA9IFtdO1xuICAgIGlmICh0eXBlb2YgcmVxdWVzdCA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICBuZXdQYXJ0cyA9IFt7IHRleHQ6IHJlcXVlc3QgfV07XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICBmb3IgKGNvbnN0IHBhcnRPclN0cmluZyBvZiByZXF1ZXN0KSB7XG4gICAgICAgICAgICBpZiAodHlwZW9mIHBhcnRPclN0cmluZyA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgICAgICAgIG5ld1BhcnRzLnB1c2goeyB0ZXh0OiBwYXJ0T3JTdHJpbmcgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBuZXdQYXJ0cy5wdXNoKHBhcnRPclN0cmluZyk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGFzc2lnblJvbGVUb1BhcnRzQW5kVmFsaWRhdGVTZW5kTWVzc2FnZVJlcXVlc3QobmV3UGFydHMpO1xufVxuLyoqXG4gKiBXaGVuIG11bHRpcGxlIFBhcnQgdHlwZXMgKGkuZS4gRnVuY3Rpb25SZXNwb25zZVBhcnQgYW5kIFRleHRQYXJ0KSBhcmVcbiAqIHBhc3NlZCBpbiBhIHNpbmdsZSBQYXJ0IGFycmF5LCB3ZSBtYXkgbmVlZCB0byBhc3NpZ24gZGlmZmVyZW50IHJvbGVzIHRvIGVhY2hcbiAqIHBhcnQuIEN1cnJlbnRseSBvbmx5IEZ1bmN0aW9uUmVzcG9uc2VQYXJ0IHJlcXVpcmVzIGEgcm9sZSBvdGhlciB0aGFuICd1c2VyJy5cbiAqIEBwcml2YXRlXG4gKiBAcGFyYW0gcGFydHMgQXJyYXkgb2YgcGFydHMgdG8gcGFzcyB0byB0aGUgbW9kZWxcbiAqIEByZXR1cm5zIEFycmF5IG9mIGNvbnRlbnQgaXRlbXNcbiAqL1xuZnVuY3Rpb24gYXNzaWduUm9sZVRvUGFydHNBbmRWYWxpZGF0ZVNlbmRNZXNzYWdlUmVxdWVzdChwYXJ0cykge1xuICAgIGNvbnN0IHVzZXJDb250ZW50ID0geyByb2xlOiBcInVzZXJcIiwgcGFydHM6IFtdIH07XG4gICAgY29uc3QgZnVuY3Rpb25Db250ZW50ID0geyByb2xlOiBcImZ1bmN0aW9uXCIsIHBhcnRzOiBbXSB9O1xuICAgIGxldCBoYXNVc2VyQ29udGVudCA9IGZhbHNlO1xuICAgIGxldCBoYXNGdW5jdGlvbkNvbnRlbnQgPSBmYWxzZTtcbiAgICBmb3IgKGNvbnN0IHBhcnQgb2YgcGFydHMpIHtcbiAgICAgICAgaWYgKFwiZnVuY3Rpb25SZXNwb25zZVwiIGluIHBhcnQpIHtcbiAgICAgICAgICAgIGZ1bmN0aW9uQ29udGVudC5wYXJ0cy5wdXNoKHBhcnQpO1xuICAgICAgICAgICAgaGFzRnVuY3Rpb25Db250ZW50ID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHVzZXJDb250ZW50LnBhcnRzLnB1c2gocGFydCk7XG4gICAgICAgICAgICBoYXNVc2VyQ29udGVudCA9IHRydWU7XG4gICAgICAgIH1cbiAgICB9XG4gICAgaWYgKGhhc1VzZXJDb250ZW50ICYmIGhhc0Z1bmN0aW9uQ29udGVudCkge1xuICAgICAgICB0aHJvdyBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJRXJyb3IoXCJXaXRoaW4gYSBzaW5nbGUgbWVzc2FnZSwgRnVuY3Rpb25SZXNwb25zZSBjYW5ub3QgYmUgbWl4ZWQgd2l0aCBvdGhlciB0eXBlIG9mIHBhcnQgaW4gdGhlIHJlcXVlc3QgZm9yIHNlbmRpbmcgY2hhdCBtZXNzYWdlLlwiKTtcbiAgICB9XG4gICAgaWYgKCFoYXNVc2VyQ29udGVudCAmJiAhaGFzRnVuY3Rpb25Db250ZW50KSB7XG4gICAgICAgIHRocm93IG5ldyBHb29nbGVHZW5lcmF0aXZlQUlFcnJvcihcIk5vIGNvbnRlbnQgaXMgcHJvdmlkZWQgZm9yIHNlbmRpbmcgY2hhdCBtZXNzYWdlLlwiKTtcbiAgICB9XG4gICAgaWYgKGhhc1VzZXJDb250ZW50KSB7XG4gICAgICAgIHJldHVybiB1c2VyQ29udGVudDtcbiAgICB9XG4gICAgcmV0dXJuIGZ1bmN0aW9uQ29udGVudDtcbn1cbmZ1bmN0aW9uIGZvcm1hdENvdW50VG9rZW5zSW5wdXQocGFyYW1zLCBtb2RlbFBhcmFtcykge1xuICAgIHZhciBfYTtcbiAgICBsZXQgZm9ybWF0dGVkR2VuZXJhdGVDb250ZW50UmVxdWVzdCA9IHtcbiAgICAgICAgbW9kZWw6IG1vZGVsUGFyYW1zID09PSBudWxsIHx8IG1vZGVsUGFyYW1zID09PSB2b2lkIDAgPyB2b2lkIDAgOiBtb2RlbFBhcmFtcy5tb2RlbCxcbiAgICAgICAgZ2VuZXJhdGlvbkNvbmZpZzogbW9kZWxQYXJhbXMgPT09IG51bGwgfHwgbW9kZWxQYXJhbXMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG1vZGVsUGFyYW1zLmdlbmVyYXRpb25Db25maWcsXG4gICAgICAgIHNhZmV0eVNldHRpbmdzOiBtb2RlbFBhcmFtcyA9PT0gbnVsbCB8fCBtb2RlbFBhcmFtcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogbW9kZWxQYXJhbXMuc2FmZXR5U2V0dGluZ3MsXG4gICAgICAgIHRvb2xzOiBtb2RlbFBhcmFtcyA9PT0gbnVsbCB8fCBtb2RlbFBhcmFtcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogbW9kZWxQYXJhbXMudG9vbHMsXG4gICAgICAgIHRvb2xDb25maWc6IG1vZGVsUGFyYW1zID09PSBudWxsIHx8IG1vZGVsUGFyYW1zID09PSB2b2lkIDAgPyB2b2lkIDAgOiBtb2RlbFBhcmFtcy50b29sQ29uZmlnLFxuICAgICAgICBzeXN0ZW1JbnN0cnVjdGlvbjogbW9kZWxQYXJhbXMgPT09IG51bGwgfHwgbW9kZWxQYXJhbXMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG1vZGVsUGFyYW1zLnN5c3RlbUluc3RydWN0aW9uLFxuICAgICAgICBjYWNoZWRDb250ZW50OiAoX2EgPSBtb2RlbFBhcmFtcyA9PT0gbnVsbCB8fCBtb2RlbFBhcmFtcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogbW9kZWxQYXJhbXMuY2FjaGVkQ29udGVudCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLm5hbWUsXG4gICAgICAgIGNvbnRlbnRzOiBbXSxcbiAgICB9O1xuICAgIGNvbnN0IGNvbnRhaW5zR2VuZXJhdGVDb250ZW50UmVxdWVzdCA9IHBhcmFtcy5nZW5lcmF0ZUNvbnRlbnRSZXF1ZXN0ICE9IG51bGw7XG4gICAgaWYgKHBhcmFtcy5jb250ZW50cykge1xuICAgICAgICBpZiAoY29udGFpbnNHZW5lcmF0ZUNvbnRlbnRSZXF1ZXN0KSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJUmVxdWVzdElucHV0RXJyb3IoXCJDb3VudFRva2Vuc1JlcXVlc3QgbXVzdCBoYXZlIG9uZSBvZiBjb250ZW50cyBvciBnZW5lcmF0ZUNvbnRlbnRSZXF1ZXN0LCBub3QgYm90aC5cIik7XG4gICAgICAgIH1cbiAgICAgICAgZm9ybWF0dGVkR2VuZXJhdGVDb250ZW50UmVxdWVzdC5jb250ZW50cyA9IHBhcmFtcy5jb250ZW50cztcbiAgICB9XG4gICAgZWxzZSBpZiAoY29udGFpbnNHZW5lcmF0ZUNvbnRlbnRSZXF1ZXN0KSB7XG4gICAgICAgIGZvcm1hdHRlZEdlbmVyYXRlQ29udGVudFJlcXVlc3QgPSBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIGZvcm1hdHRlZEdlbmVyYXRlQ29udGVudFJlcXVlc3QpLCBwYXJhbXMuZ2VuZXJhdGVDb250ZW50UmVxdWVzdCk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICAvLyBBcnJheSBvciBzdHJpbmdcbiAgICAgICAgY29uc3QgY29udGVudCA9IGZvcm1hdE5ld0NvbnRlbnQocGFyYW1zKTtcbiAgICAgICAgZm9ybWF0dGVkR2VuZXJhdGVDb250ZW50UmVxdWVzdC5jb250ZW50cyA9IFtjb250ZW50XTtcbiAgICB9XG4gICAgcmV0dXJuIHsgZ2VuZXJhdGVDb250ZW50UmVxdWVzdDogZm9ybWF0dGVkR2VuZXJhdGVDb250ZW50UmVxdWVzdCB9O1xufVxuZnVuY3Rpb24gZm9ybWF0R2VuZXJhdGVDb250ZW50SW5wdXQocGFyYW1zKSB7XG4gICAgbGV0IGZvcm1hdHRlZFJlcXVlc3Q7XG4gICAgaWYgKHBhcmFtcy5jb250ZW50cykge1xuICAgICAgICBmb3JtYXR0ZWRSZXF1ZXN0ID0gcGFyYW1zO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgLy8gQXJyYXkgb3Igc3RyaW5nXG4gICAgICAgIGNvbnN0IGNvbnRlbnQgPSBmb3JtYXROZXdDb250ZW50KHBhcmFtcyk7XG4gICAgICAgIGZvcm1hdHRlZFJlcXVlc3QgPSB7IGNvbnRlbnRzOiBbY29udGVudF0gfTtcbiAgICB9XG4gICAgaWYgKHBhcmFtcy5zeXN0ZW1JbnN0cnVjdGlvbikge1xuICAgICAgICBmb3JtYXR0ZWRSZXF1ZXN0LnN5c3RlbUluc3RydWN0aW9uID0gZm9ybWF0U3lzdGVtSW5zdHJ1Y3Rpb24ocGFyYW1zLnN5c3RlbUluc3RydWN0aW9uKTtcbiAgICB9XG4gICAgcmV0dXJuIGZvcm1hdHRlZFJlcXVlc3Q7XG59XG5mdW5jdGlvbiBmb3JtYXRFbWJlZENvbnRlbnRJbnB1dChwYXJhbXMpIHtcbiAgICBpZiAodHlwZW9mIHBhcmFtcyA9PT0gXCJzdHJpbmdcIiB8fCBBcnJheS5pc0FycmF5KHBhcmFtcykpIHtcbiAgICAgICAgY29uc3QgY29udGVudCA9IGZvcm1hdE5ld0NvbnRlbnQocGFyYW1zKTtcbiAgICAgICAgcmV0dXJuIHsgY29udGVudCB9O1xuICAgIH1cbiAgICByZXR1cm4gcGFyYW1zO1xufVxuXG4vKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgMjAyNCBHb29nbGUgTExDXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuLy8gaHR0cHM6Ly9haS5nb29nbGUuZGV2L2FwaS9yZXN0L3YxYmV0YS9Db250ZW50I3BhcnRcbmNvbnN0IFZBTElEX1BBUlRfRklFTERTID0gW1xuICAgIFwidGV4dFwiLFxuICAgIFwiaW5saW5lRGF0YVwiLFxuICAgIFwiZnVuY3Rpb25DYWxsXCIsXG4gICAgXCJmdW5jdGlvblJlc3BvbnNlXCIsXG4gICAgXCJleGVjdXRhYmxlQ29kZVwiLFxuICAgIFwiY29kZUV4ZWN1dGlvblJlc3VsdFwiLFxuXTtcbmNvbnN0IFZBTElEX1BBUlRTX1BFUl9ST0xFID0ge1xuICAgIHVzZXI6IFtcInRleHRcIiwgXCJpbmxpbmVEYXRhXCJdLFxuICAgIGZ1bmN0aW9uOiBbXCJmdW5jdGlvblJlc3BvbnNlXCJdLFxuICAgIG1vZGVsOiBbXCJ0ZXh0XCIsIFwiZnVuY3Rpb25DYWxsXCIsIFwiZXhlY3V0YWJsZUNvZGVcIiwgXCJjb2RlRXhlY3V0aW9uUmVzdWx0XCJdLFxuICAgIC8vIFN5c3RlbSBpbnN0cnVjdGlvbnMgc2hvdWxkbid0IGJlIGluIGhpc3RvcnkgYW55d2F5LlxuICAgIHN5c3RlbTogW1widGV4dFwiXSxcbn07XG5mdW5jdGlvbiB2YWxpZGF0ZUNoYXRIaXN0b3J5KGhpc3RvcnkpIHtcbiAgICBsZXQgcHJldkNvbnRlbnQgPSBmYWxzZTtcbiAgICBmb3IgKGNvbnN0IGN1cnJDb250ZW50IG9mIGhpc3RvcnkpIHtcbiAgICAgICAgY29uc3QgeyByb2xlLCBwYXJ0cyB9ID0gY3VyckNvbnRlbnQ7XG4gICAgICAgIGlmICghcHJldkNvbnRlbnQgJiYgcm9sZSAhPT0gXCJ1c2VyXCIpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBHb29nbGVHZW5lcmF0aXZlQUlFcnJvcihgRmlyc3QgY29udGVudCBzaG91bGQgYmUgd2l0aCByb2xlICd1c2VyJywgZ290ICR7cm9sZX1gKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIVBPU1NJQkxFX1JPTEVTLmluY2x1ZGVzKHJvbGUpKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJRXJyb3IoYEVhY2ggaXRlbSBzaG91bGQgaW5jbHVkZSByb2xlIGZpZWxkLiBHb3QgJHtyb2xlfSBidXQgdmFsaWQgcm9sZXMgYXJlOiAke0pTT04uc3RyaW5naWZ5KFBPU1NJQkxFX1JPTEVTKX1gKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIUFycmF5LmlzQXJyYXkocGFydHMpKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJRXJyb3IoXCJDb250ZW50IHNob3VsZCBoYXZlICdwYXJ0cycgcHJvcGVydHkgd2l0aCBhbiBhcnJheSBvZiBQYXJ0c1wiKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAocGFydHMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJRXJyb3IoXCJFYWNoIENvbnRlbnQgc2hvdWxkIGhhdmUgYXQgbGVhc3Qgb25lIHBhcnRcIik7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgY291bnRGaWVsZHMgPSB7XG4gICAgICAgICAgICB0ZXh0OiAwLFxuICAgICAgICAgICAgaW5saW5lRGF0YTogMCxcbiAgICAgICAgICAgIGZ1bmN0aW9uQ2FsbDogMCxcbiAgICAgICAgICAgIGZ1bmN0aW9uUmVzcG9uc2U6IDAsXG4gICAgICAgICAgICBmaWxlRGF0YTogMCxcbiAgICAgICAgICAgIGV4ZWN1dGFibGVDb2RlOiAwLFxuICAgICAgICAgICAgY29kZUV4ZWN1dGlvblJlc3VsdDogMCxcbiAgICAgICAgfTtcbiAgICAgICAgZm9yIChjb25zdCBwYXJ0IG9mIHBhcnRzKSB7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGtleSBvZiBWQUxJRF9QQVJUX0ZJRUxEUykge1xuICAgICAgICAgICAgICAgIGlmIChrZXkgaW4gcGFydCkge1xuICAgICAgICAgICAgICAgICAgICBjb3VudEZpZWxkc1trZXldICs9IDE7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHZhbGlkUGFydHMgPSBWQUxJRF9QQVJUU19QRVJfUk9MRVtyb2xlXTtcbiAgICAgICAgZm9yIChjb25zdCBrZXkgb2YgVkFMSURfUEFSVF9GSUVMRFMpIHtcbiAgICAgICAgICAgIGlmICghdmFsaWRQYXJ0cy5pbmNsdWRlcyhrZXkpICYmIGNvdW50RmllbGRzW2tleV0gPiAwKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEdvb2dsZUdlbmVyYXRpdmVBSUVycm9yKGBDb250ZW50IHdpdGggcm9sZSAnJHtyb2xlfScgY2FuJ3QgY29udGFpbiAnJHtrZXl9JyBwYXJ0YCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcHJldkNvbnRlbnQgPSB0cnVlO1xuICAgIH1cbn1cblxuLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IDIwMjQgR29vZ2xlIExMQ1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8qKlxuICogRG8gbm90IGxvZyBhIG1lc3NhZ2UgZm9yIHRoaXMgZXJyb3IuXG4gKi9cbmNvbnN0IFNJTEVOVF9FUlJPUiA9IFwiU0lMRU5UX0VSUk9SXCI7XG4vKipcbiAqIENoYXRTZXNzaW9uIGNsYXNzIHRoYXQgZW5hYmxlcyBzZW5kaW5nIGNoYXQgbWVzc2FnZXMgYW5kIHN0b3Jlc1xuICogaGlzdG9yeSBvZiBzZW50IGFuZCByZWNlaXZlZCBtZXNzYWdlcyBzbyBmYXIuXG4gKlxuICogQHB1YmxpY1xuICovXG5jbGFzcyBDaGF0U2Vzc2lvbiB7XG4gICAgY29uc3RydWN0b3IoYXBpS2V5LCBtb2RlbCwgcGFyYW1zLCBfcmVxdWVzdE9wdGlvbnMgPSB7fSkge1xuICAgICAgICB0aGlzLm1vZGVsID0gbW9kZWw7XG4gICAgICAgIHRoaXMucGFyYW1zID0gcGFyYW1zO1xuICAgICAgICB0aGlzLl9yZXF1ZXN0T3B0aW9ucyA9IF9yZXF1ZXN0T3B0aW9ucztcbiAgICAgICAgdGhpcy5faGlzdG9yeSA9IFtdO1xuICAgICAgICB0aGlzLl9zZW5kUHJvbWlzZSA9IFByb21pc2UucmVzb2x2ZSgpO1xuICAgICAgICB0aGlzLl9hcGlLZXkgPSBhcGlLZXk7XG4gICAgICAgIGlmIChwYXJhbXMgPT09IG51bGwgfHwgcGFyYW1zID09PSB2b2lkIDAgPyB2b2lkIDAgOiBwYXJhbXMuaGlzdG9yeSkge1xuICAgICAgICAgICAgdmFsaWRhdGVDaGF0SGlzdG9yeShwYXJhbXMuaGlzdG9yeSk7XG4gICAgICAgICAgICB0aGlzLl9oaXN0b3J5ID0gcGFyYW1zLmhpc3Rvcnk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0cyB0aGUgY2hhdCBoaXN0b3J5IHNvIGZhci4gQmxvY2tlZCBwcm9tcHRzIGFyZSBub3QgYWRkZWQgdG8gaGlzdG9yeS5cbiAgICAgKiBCbG9ja2VkIGNhbmRpZGF0ZXMgYXJlIG5vdCBhZGRlZCB0byBoaXN0b3J5LCBub3IgYXJlIHRoZSBwcm9tcHRzIHRoYXRcbiAgICAgKiBnZW5lcmF0ZWQgdGhlbS5cbiAgICAgKi9cbiAgICBhc3luYyBnZXRIaXN0b3J5KCkge1xuICAgICAgICBhd2FpdCB0aGlzLl9zZW5kUHJvbWlzZTtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2hpc3Rvcnk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNlbmRzIGEgY2hhdCBtZXNzYWdlIGFuZCByZWNlaXZlcyBhIG5vbi1zdHJlYW1pbmdcbiAgICAgKiB7QGxpbmsgR2VuZXJhdGVDb250ZW50UmVzdWx0fS5cbiAgICAgKlxuICAgICAqIEZpZWxkcyBzZXQgaW4gdGhlIG9wdGlvbmFsIHtAbGluayBTaW5nbGVSZXF1ZXN0T3B0aW9uc30gcGFyYW1ldGVyIHdpbGxcbiAgICAgKiB0YWtlIHByZWNlZGVuY2Ugb3ZlciB0aGUge0BsaW5rIFJlcXVlc3RPcHRpb25zfSB2YWx1ZXMgcHJvdmlkZWQgdG9cbiAgICAgKiB7QGxpbmsgR29vZ2xlR2VuZXJhdGl2ZUFJLmdldEdlbmVyYXRpdmVNb2RlbCB9LlxuICAgICAqL1xuICAgIGFzeW5jIHNlbmRNZXNzYWdlKHJlcXVlc3QsIHJlcXVlc3RPcHRpb25zID0ge30pIHtcbiAgICAgICAgdmFyIF9hLCBfYiwgX2MsIF9kLCBfZSwgX2Y7XG4gICAgICAgIGF3YWl0IHRoaXMuX3NlbmRQcm9taXNlO1xuICAgICAgICBjb25zdCBuZXdDb250ZW50ID0gZm9ybWF0TmV3Q29udGVudChyZXF1ZXN0KTtcbiAgICAgICAgY29uc3QgZ2VuZXJhdGVDb250ZW50UmVxdWVzdCA9IHtcbiAgICAgICAgICAgIHNhZmV0eVNldHRpbmdzOiAoX2EgPSB0aGlzLnBhcmFtcykgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnNhZmV0eVNldHRpbmdzLFxuICAgICAgICAgICAgZ2VuZXJhdGlvbkNvbmZpZzogKF9iID0gdGhpcy5wYXJhbXMpID09PSBudWxsIHx8IF9iID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYi5nZW5lcmF0aW9uQ29uZmlnLFxuICAgICAgICAgICAgdG9vbHM6IChfYyA9IHRoaXMucGFyYW1zKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2MudG9vbHMsXG4gICAgICAgICAgICB0b29sQ29uZmlnOiAoX2QgPSB0aGlzLnBhcmFtcykgPT09IG51bGwgfHwgX2QgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9kLnRvb2xDb25maWcsXG4gICAgICAgICAgICBzeXN0ZW1JbnN0cnVjdGlvbjogKF9lID0gdGhpcy5wYXJhbXMpID09PSBudWxsIHx8IF9lID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZS5zeXN0ZW1JbnN0cnVjdGlvbixcbiAgICAgICAgICAgIGNhY2hlZENvbnRlbnQ6IChfZiA9IHRoaXMucGFyYW1zKSA9PT0gbnVsbCB8fCBfZiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2YuY2FjaGVkQ29udGVudCxcbiAgICAgICAgICAgIGNvbnRlbnRzOiBbLi4udGhpcy5faGlzdG9yeSwgbmV3Q29udGVudF0sXG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IGNoYXRTZXNzaW9uUmVxdWVzdE9wdGlvbnMgPSBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHRoaXMuX3JlcXVlc3RPcHRpb25zKSwgcmVxdWVzdE9wdGlvbnMpO1xuICAgICAgICBsZXQgZmluYWxSZXN1bHQ7XG4gICAgICAgIC8vIEFkZCBvbnRvIHRoZSBjaGFpbi5cbiAgICAgICAgdGhpcy5fc2VuZFByb21pc2UgPSB0aGlzLl9zZW5kUHJvbWlzZVxuICAgICAgICAgICAgLnRoZW4oKCkgPT4gZ2VuZXJhdGVDb250ZW50KHRoaXMuX2FwaUtleSwgdGhpcy5tb2RlbCwgZ2VuZXJhdGVDb250ZW50UmVxdWVzdCwgY2hhdFNlc3Npb25SZXF1ZXN0T3B0aW9ucykpXG4gICAgICAgICAgICAudGhlbigocmVzdWx0KSA9PiB7XG4gICAgICAgICAgICB2YXIgX2E7XG4gICAgICAgICAgICBpZiAocmVzdWx0LnJlc3BvbnNlLmNhbmRpZGF0ZXMgJiZcbiAgICAgICAgICAgICAgICByZXN1bHQucmVzcG9uc2UuY2FuZGlkYXRlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5faGlzdG9yeS5wdXNoKG5ld0NvbnRlbnQpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlQ29udGVudCA9IE9iamVjdC5hc3NpZ24oeyBwYXJ0czogW10sIFxuICAgICAgICAgICAgICAgICAgICAvLyBSZXNwb25zZSBzZWVtcyB0byBjb21lIGJhY2sgd2l0aG91dCBhIHJvbGUgc2V0LlxuICAgICAgICAgICAgICAgICAgICByb2xlOiBcIm1vZGVsXCIgfSwgKF9hID0gcmVzdWx0LnJlc3BvbnNlLmNhbmRpZGF0ZXMpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYVswXS5jb250ZW50KTtcbiAgICAgICAgICAgICAgICB0aGlzLl9oaXN0b3J5LnB1c2gocmVzcG9uc2VDb250ZW50KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnN0IGJsb2NrRXJyb3JNZXNzYWdlID0gZm9ybWF0QmxvY2tFcnJvck1lc3NhZ2UocmVzdWx0LnJlc3BvbnNlKTtcbiAgICAgICAgICAgICAgICBpZiAoYmxvY2tFcnJvck1lc3NhZ2UpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKGBzZW5kTWVzc2FnZSgpIHdhcyB1bnN1Y2Nlc3NmdWwuICR7YmxvY2tFcnJvck1lc3NhZ2V9LiBJbnNwZWN0IHJlc3BvbnNlIG9iamVjdCBmb3IgZGV0YWlscy5gKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBmaW5hbFJlc3VsdCA9IHJlc3VsdDtcbiAgICAgICAgfSk7XG4gICAgICAgIGF3YWl0IHRoaXMuX3NlbmRQcm9taXNlO1xuICAgICAgICByZXR1cm4gZmluYWxSZXN1bHQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNlbmRzIGEgY2hhdCBtZXNzYWdlIGFuZCByZWNlaXZlcyB0aGUgcmVzcG9uc2UgYXMgYVxuICAgICAqIHtAbGluayBHZW5lcmF0ZUNvbnRlbnRTdHJlYW1SZXN1bHR9IGNvbnRhaW5pbmcgYW4gaXRlcmFibGUgc3RyZWFtXG4gICAgICogYW5kIGEgcmVzcG9uc2UgcHJvbWlzZS5cbiAgICAgKlxuICAgICAqIEZpZWxkcyBzZXQgaW4gdGhlIG9wdGlvbmFsIHtAbGluayBTaW5nbGVSZXF1ZXN0T3B0aW9uc30gcGFyYW1ldGVyIHdpbGxcbiAgICAgKiB0YWtlIHByZWNlZGVuY2Ugb3ZlciB0aGUge0BsaW5rIFJlcXVlc3RPcHRpb25zfSB2YWx1ZXMgcHJvdmlkZWQgdG9cbiAgICAgKiB7QGxpbmsgR29vZ2xlR2VuZXJhdGl2ZUFJLmdldEdlbmVyYXRpdmVNb2RlbCB9LlxuICAgICAqL1xuICAgIGFzeW5jIHNlbmRNZXNzYWdlU3RyZWFtKHJlcXVlc3QsIHJlcXVlc3RPcHRpb25zID0ge30pIHtcbiAgICAgICAgdmFyIF9hLCBfYiwgX2MsIF9kLCBfZSwgX2Y7XG4gICAgICAgIGF3YWl0IHRoaXMuX3NlbmRQcm9taXNlO1xuICAgICAgICBjb25zdCBuZXdDb250ZW50ID0gZm9ybWF0TmV3Q29udGVudChyZXF1ZXN0KTtcbiAgICAgICAgY29uc3QgZ2VuZXJhdGVDb250ZW50UmVxdWVzdCA9IHtcbiAgICAgICAgICAgIHNhZmV0eVNldHRpbmdzOiAoX2EgPSB0aGlzLnBhcmFtcykgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnNhZmV0eVNldHRpbmdzLFxuICAgICAgICAgICAgZ2VuZXJhdGlvbkNvbmZpZzogKF9iID0gdGhpcy5wYXJhbXMpID09PSBudWxsIHx8IF9iID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYi5nZW5lcmF0aW9uQ29uZmlnLFxuICAgICAgICAgICAgdG9vbHM6IChfYyA9IHRoaXMucGFyYW1zKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2MudG9vbHMsXG4gICAgICAgICAgICB0b29sQ29uZmlnOiAoX2QgPSB0aGlzLnBhcmFtcykgPT09IG51bGwgfHwgX2QgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9kLnRvb2xDb25maWcsXG4gICAgICAgICAgICBzeXN0ZW1JbnN0cnVjdGlvbjogKF9lID0gdGhpcy5wYXJhbXMpID09PSBudWxsIHx8IF9lID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZS5zeXN0ZW1JbnN0cnVjdGlvbixcbiAgICAgICAgICAgIGNhY2hlZENvbnRlbnQ6IChfZiA9IHRoaXMucGFyYW1zKSA9PT0gbnVsbCB8fCBfZiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2YuY2FjaGVkQ29udGVudCxcbiAgICAgICAgICAgIGNvbnRlbnRzOiBbLi4udGhpcy5faGlzdG9yeSwgbmV3Q29udGVudF0sXG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IGNoYXRTZXNzaW9uUmVxdWVzdE9wdGlvbnMgPSBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHRoaXMuX3JlcXVlc3RPcHRpb25zKSwgcmVxdWVzdE9wdGlvbnMpO1xuICAgICAgICBjb25zdCBzdHJlYW1Qcm9taXNlID0gZ2VuZXJhdGVDb250ZW50U3RyZWFtKHRoaXMuX2FwaUtleSwgdGhpcy5tb2RlbCwgZ2VuZXJhdGVDb250ZW50UmVxdWVzdCwgY2hhdFNlc3Npb25SZXF1ZXN0T3B0aW9ucyk7XG4gICAgICAgIC8vIEFkZCBvbnRvIHRoZSBjaGFpbi5cbiAgICAgICAgdGhpcy5fc2VuZFByb21pc2UgPSB0aGlzLl9zZW5kUHJvbWlzZVxuICAgICAgICAgICAgLnRoZW4oKCkgPT4gc3RyZWFtUHJvbWlzZSlcbiAgICAgICAgICAgIC8vIFRoaXMgbXVzdCBiZSBoYW5kbGVkIHRvIGF2b2lkIHVuaGFuZGxlZCByZWplY3Rpb24sIGJ1dCBqdW1wXG4gICAgICAgICAgICAvLyB0byB0aGUgZmluYWwgY2F0Y2ggYmxvY2sgd2l0aCBhIGxhYmVsIHRvIG5vdCBsb2cgdGhpcyBlcnJvci5cbiAgICAgICAgICAgIC5jYXRjaCgoX2lnbm9yZWQpID0+IHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihTSUxFTlRfRVJST1IpO1xuICAgICAgICB9KVxuICAgICAgICAgICAgLnRoZW4oKHN0cmVhbVJlc3VsdCkgPT4gc3RyZWFtUmVzdWx0LnJlc3BvbnNlKVxuICAgICAgICAgICAgLnRoZW4oKHJlc3BvbnNlKSA9PiB7XG4gICAgICAgICAgICBpZiAocmVzcG9uc2UuY2FuZGlkYXRlcyAmJiByZXNwb25zZS5jYW5kaWRhdGVzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgICAgICB0aGlzLl9oaXN0b3J5LnB1c2gobmV3Q29udGVudCk7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2VDb250ZW50ID0gT2JqZWN0LmFzc2lnbih7fSwgcmVzcG9uc2UuY2FuZGlkYXRlc1swXS5jb250ZW50KTtcbiAgICAgICAgICAgICAgICAvLyBSZXNwb25zZSBzZWVtcyB0byBjb21lIGJhY2sgd2l0aG91dCBhIHJvbGUgc2V0LlxuICAgICAgICAgICAgICAgIGlmICghcmVzcG9uc2VDb250ZW50LnJvbGUpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2VDb250ZW50LnJvbGUgPSBcIm1vZGVsXCI7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMuX2hpc3RvcnkucHVzaChyZXNwb25zZUNvbnRlbnQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc3QgYmxvY2tFcnJvck1lc3NhZ2UgPSBmb3JtYXRCbG9ja0Vycm9yTWVzc2FnZShyZXNwb25zZSk7XG4gICAgICAgICAgICAgICAgaWYgKGJsb2NrRXJyb3JNZXNzYWdlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2Fybihgc2VuZE1lc3NhZ2VTdHJlYW0oKSB3YXMgdW5zdWNjZXNzZnVsLiAke2Jsb2NrRXJyb3JNZXNzYWdlfS4gSW5zcGVjdCByZXNwb25zZSBvYmplY3QgZm9yIGRldGFpbHMuYCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9KVxuICAgICAgICAgICAgLmNhdGNoKChlKSA9PiB7XG4gICAgICAgICAgICAvLyBFcnJvcnMgaW4gc3RyZWFtUHJvbWlzZSBhcmUgYWxyZWFkeSBjYXRjaGFibGUgYnkgdGhlIHVzZXIgYXNcbiAgICAgICAgICAgIC8vIHN0cmVhbVByb21pc2UgaXMgcmV0dXJuZWQuXG4gICAgICAgICAgICAvLyBBdm9pZCBkdXBsaWNhdGluZyB0aGUgZXJyb3IgbWVzc2FnZSBpbiBsb2dzLlxuICAgICAgICAgICAgaWYgKGUubWVzc2FnZSAhPT0gU0lMRU5UX0VSUk9SKSB7XG4gICAgICAgICAgICAgICAgLy8gVXNlcnMgZG8gbm90IGhhdmUgYWNjZXNzIHRvIF9zZW5kUHJvbWlzZSB0byBjYXRjaCBlcnJvcnNcbiAgICAgICAgICAgICAgICAvLyBkb3duc3RyZWFtIGZyb20gc3RyZWFtUHJvbWlzZSwgc28gdGhleSBzaG91bGQgbm90IHRocm93LlxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4gc3RyZWFtUHJvbWlzZTtcbiAgICB9XG59XG5cbi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCAyMDI0IEdvb2dsZSBMTENcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5hc3luYyBmdW5jdGlvbiBjb3VudFRva2VucyhhcGlLZXksIG1vZGVsLCBwYXJhbXMsIHNpbmdsZVJlcXVlc3RPcHRpb25zKSB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBtYWtlTW9kZWxSZXF1ZXN0KG1vZGVsLCBUYXNrLkNPVU5UX1RPS0VOUywgYXBpS2V5LCBmYWxzZSwgSlNPTi5zdHJpbmdpZnkocGFyYW1zKSwgc2luZ2xlUmVxdWVzdE9wdGlvbnMpO1xuICAgIHJldHVybiByZXNwb25zZS5qc29uKCk7XG59XG5cbi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCAyMDI0IEdvb2dsZSBMTENcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5hc3luYyBmdW5jdGlvbiBlbWJlZENvbnRlbnQoYXBpS2V5LCBtb2RlbCwgcGFyYW1zLCByZXF1ZXN0T3B0aW9ucykge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbWFrZU1vZGVsUmVxdWVzdChtb2RlbCwgVGFzay5FTUJFRF9DT05URU5ULCBhcGlLZXksIGZhbHNlLCBKU09OLnN0cmluZ2lmeShwYXJhbXMpLCByZXF1ZXN0T3B0aW9ucyk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmpzb24oKTtcbn1cbmFzeW5jIGZ1bmN0aW9uIGJhdGNoRW1iZWRDb250ZW50cyhhcGlLZXksIG1vZGVsLCBwYXJhbXMsIHJlcXVlc3RPcHRpb25zKSB7XG4gICAgY29uc3QgcmVxdWVzdHNXaXRoTW9kZWwgPSBwYXJhbXMucmVxdWVzdHMubWFwKChyZXF1ZXN0KSA9PiB7XG4gICAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHJlcXVlc3QpLCB7IG1vZGVsIH0pO1xuICAgIH0pO1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbWFrZU1vZGVsUmVxdWVzdChtb2RlbCwgVGFzay5CQVRDSF9FTUJFRF9DT05URU5UUywgYXBpS2V5LCBmYWxzZSwgSlNPTi5zdHJpbmdpZnkoeyByZXF1ZXN0czogcmVxdWVzdHNXaXRoTW9kZWwgfSksIHJlcXVlc3RPcHRpb25zKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xufVxuXG4vKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgMjAyNCBHb29nbGUgTExDXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuLyoqXG4gKiBDbGFzcyBmb3IgZ2VuZXJhdGl2ZSBtb2RlbCBBUElzLlxuICogQHB1YmxpY1xuICovXG5jbGFzcyBHZW5lcmF0aXZlTW9kZWwge1xuICAgIGNvbnN0cnVjdG9yKGFwaUtleSwgbW9kZWxQYXJhbXMsIF9yZXF1ZXN0T3B0aW9ucyA9IHt9KSB7XG4gICAgICAgIHRoaXMuYXBpS2V5ID0gYXBpS2V5O1xuICAgICAgICB0aGlzLl9yZXF1ZXN0T3B0aW9ucyA9IF9yZXF1ZXN0T3B0aW9ucztcbiAgICAgICAgaWYgKG1vZGVsUGFyYW1zLm1vZGVsLmluY2x1ZGVzKFwiL1wiKSkge1xuICAgICAgICAgICAgLy8gTW9kZWxzIG1heSBiZSBuYW1lZCBcIm1vZGVscy9tb2RlbC1uYW1lXCIgb3IgXCJ0dW5lZE1vZGVscy9tb2RlbC1uYW1lXCJcbiAgICAgICAgICAgIHRoaXMubW9kZWwgPSBtb2RlbFBhcmFtcy5tb2RlbDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIC8vIElmIHBhdGggaXMgbm90IGluY2x1ZGVkLCBhc3N1bWUgaXQncyBhIG5vbi10dW5lZCBtb2RlbC5cbiAgICAgICAgICAgIHRoaXMubW9kZWwgPSBgbW9kZWxzLyR7bW9kZWxQYXJhbXMubW9kZWx9YDtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmdlbmVyYXRpb25Db25maWcgPSBtb2RlbFBhcmFtcy5nZW5lcmF0aW9uQ29uZmlnIHx8IHt9O1xuICAgICAgICB0aGlzLnNhZmV0eVNldHRpbmdzID0gbW9kZWxQYXJhbXMuc2FmZXR5U2V0dGluZ3MgfHwgW107XG4gICAgICAgIHRoaXMudG9vbHMgPSBtb2RlbFBhcmFtcy50b29scztcbiAgICAgICAgdGhpcy50b29sQ29uZmlnID0gbW9kZWxQYXJhbXMudG9vbENvbmZpZztcbiAgICAgICAgdGhpcy5zeXN0ZW1JbnN0cnVjdGlvbiA9IGZvcm1hdFN5c3RlbUluc3RydWN0aW9uKG1vZGVsUGFyYW1zLnN5c3RlbUluc3RydWN0aW9uKTtcbiAgICAgICAgdGhpcy5jYWNoZWRDb250ZW50ID0gbW9kZWxQYXJhbXMuY2FjaGVkQ29udGVudDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogTWFrZXMgYSBzaW5nbGUgbm9uLXN0cmVhbWluZyBjYWxsIHRvIHRoZSBtb2RlbFxuICAgICAqIGFuZCByZXR1cm5zIGFuIG9iamVjdCBjb250YWluaW5nIGEgc2luZ2xlIHtAbGluayBHZW5lcmF0ZUNvbnRlbnRSZXNwb25zZX0uXG4gICAgICpcbiAgICAgKiBGaWVsZHMgc2V0IGluIHRoZSBvcHRpb25hbCB7QGxpbmsgU2luZ2xlUmVxdWVzdE9wdGlvbnN9IHBhcmFtZXRlciB3aWxsXG4gICAgICogdGFrZSBwcmVjZWRlbmNlIG92ZXIgdGhlIHtAbGluayBSZXF1ZXN0T3B0aW9uc30gdmFsdWVzIHByb3ZpZGVkIHRvXG4gICAgICoge0BsaW5rIEdvb2dsZUdlbmVyYXRpdmVBSS5nZXRHZW5lcmF0aXZlTW9kZWwgfS5cbiAgICAgKi9cbiAgICBhc3luYyBnZW5lcmF0ZUNvbnRlbnQocmVxdWVzdCwgcmVxdWVzdE9wdGlvbnMgPSB7fSkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIGNvbnN0IGZvcm1hdHRlZFBhcmFtcyA9IGZvcm1hdEdlbmVyYXRlQ29udGVudElucHV0KHJlcXVlc3QpO1xuICAgICAgICBjb25zdCBnZW5lcmF0aXZlTW9kZWxSZXF1ZXN0T3B0aW9ucyA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5fcmVxdWVzdE9wdGlvbnMpLCByZXF1ZXN0T3B0aW9ucyk7XG4gICAgICAgIHJldHVybiBnZW5lcmF0ZUNvbnRlbnQodGhpcy5hcGlLZXksIHRoaXMubW9kZWwsIE9iamVjdC5hc3NpZ24oeyBnZW5lcmF0aW9uQ29uZmlnOiB0aGlzLmdlbmVyYXRpb25Db25maWcsIHNhZmV0eVNldHRpbmdzOiB0aGlzLnNhZmV0eVNldHRpbmdzLCB0b29sczogdGhpcy50b29scywgdG9vbENvbmZpZzogdGhpcy50b29sQ29uZmlnLCBzeXN0ZW1JbnN0cnVjdGlvbjogdGhpcy5zeXN0ZW1JbnN0cnVjdGlvbiwgY2FjaGVkQ29udGVudDogKF9hID0gdGhpcy5jYWNoZWRDb250ZW50KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EubmFtZSB9LCBmb3JtYXR0ZWRQYXJhbXMpLCBnZW5lcmF0aXZlTW9kZWxSZXF1ZXN0T3B0aW9ucyk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIE1ha2VzIGEgc2luZ2xlIHN0cmVhbWluZyBjYWxsIHRvIHRoZSBtb2RlbCBhbmQgcmV0dXJucyBhbiBvYmplY3RcbiAgICAgKiBjb250YWluaW5nIGFuIGl0ZXJhYmxlIHN0cmVhbSB0aGF0IGl0ZXJhdGVzIG92ZXIgYWxsIGNodW5rcyBpbiB0aGVcbiAgICAgKiBzdHJlYW1pbmcgcmVzcG9uc2UgYXMgd2VsbCBhcyBhIHByb21pc2UgdGhhdCByZXR1cm5zIHRoZSBmaW5hbFxuICAgICAqIGFnZ3JlZ2F0ZWQgcmVzcG9uc2UuXG4gICAgICpcbiAgICAgKiBGaWVsZHMgc2V0IGluIHRoZSBvcHRpb25hbCB7QGxpbmsgU2luZ2xlUmVxdWVzdE9wdGlvbnN9IHBhcmFtZXRlciB3aWxsXG4gICAgICogdGFrZSBwcmVjZWRlbmNlIG92ZXIgdGhlIHtAbGluayBSZXF1ZXN0T3B0aW9uc30gdmFsdWVzIHByb3ZpZGVkIHRvXG4gICAgICoge0BsaW5rIEdvb2dsZUdlbmVyYXRpdmVBSS5nZXRHZW5lcmF0aXZlTW9kZWwgfS5cbiAgICAgKi9cbiAgICBhc3luYyBnZW5lcmF0ZUNvbnRlbnRTdHJlYW0ocmVxdWVzdCwgcmVxdWVzdE9wdGlvbnMgPSB7fSkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIGNvbnN0IGZvcm1hdHRlZFBhcmFtcyA9IGZvcm1hdEdlbmVyYXRlQ29udGVudElucHV0KHJlcXVlc3QpO1xuICAgICAgICBjb25zdCBnZW5lcmF0aXZlTW9kZWxSZXF1ZXN0T3B0aW9ucyA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5fcmVxdWVzdE9wdGlvbnMpLCByZXF1ZXN0T3B0aW9ucyk7XG4gICAgICAgIHJldHVybiBnZW5lcmF0ZUNvbnRlbnRTdHJlYW0odGhpcy5hcGlLZXksIHRoaXMubW9kZWwsIE9iamVjdC5hc3NpZ24oeyBnZW5lcmF0aW9uQ29uZmlnOiB0aGlzLmdlbmVyYXRpb25Db25maWcsIHNhZmV0eVNldHRpbmdzOiB0aGlzLnNhZmV0eVNldHRpbmdzLCB0b29sczogdGhpcy50b29scywgdG9vbENvbmZpZzogdGhpcy50b29sQ29uZmlnLCBzeXN0ZW1JbnN0cnVjdGlvbjogdGhpcy5zeXN0ZW1JbnN0cnVjdGlvbiwgY2FjaGVkQ29udGVudDogKF9hID0gdGhpcy5jYWNoZWRDb250ZW50KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EubmFtZSB9LCBmb3JtYXR0ZWRQYXJhbXMpLCBnZW5lcmF0aXZlTW9kZWxSZXF1ZXN0T3B0aW9ucyk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldHMgYSBuZXcge0BsaW5rIENoYXRTZXNzaW9ufSBpbnN0YW5jZSB3aGljaCBjYW4gYmUgdXNlZCBmb3JcbiAgICAgKiBtdWx0aS10dXJuIGNoYXRzLlxuICAgICAqL1xuICAgIHN0YXJ0Q2hhdChzdGFydENoYXRQYXJhbXMpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICByZXR1cm4gbmV3IENoYXRTZXNzaW9uKHRoaXMuYXBpS2V5LCB0aGlzLm1vZGVsLCBPYmplY3QuYXNzaWduKHsgZ2VuZXJhdGlvbkNvbmZpZzogdGhpcy5nZW5lcmF0aW9uQ29uZmlnLCBzYWZldHlTZXR0aW5nczogdGhpcy5zYWZldHlTZXR0aW5ncywgdG9vbHM6IHRoaXMudG9vbHMsIHRvb2xDb25maWc6IHRoaXMudG9vbENvbmZpZywgc3lzdGVtSW5zdHJ1Y3Rpb246IHRoaXMuc3lzdGVtSW5zdHJ1Y3Rpb24sIGNhY2hlZENvbnRlbnQ6IChfYSA9IHRoaXMuY2FjaGVkQ29udGVudCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLm5hbWUgfSwgc3RhcnRDaGF0UGFyYW1zKSwgdGhpcy5fcmVxdWVzdE9wdGlvbnMpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDb3VudHMgdGhlIHRva2VucyBpbiB0aGUgcHJvdmlkZWQgcmVxdWVzdC5cbiAgICAgKlxuICAgICAqIEZpZWxkcyBzZXQgaW4gdGhlIG9wdGlvbmFsIHtAbGluayBTaW5nbGVSZXF1ZXN0T3B0aW9uc30gcGFyYW1ldGVyIHdpbGxcbiAgICAgKiB0YWtlIHByZWNlZGVuY2Ugb3ZlciB0aGUge0BsaW5rIFJlcXVlc3RPcHRpb25zfSB2YWx1ZXMgcHJvdmlkZWQgdG9cbiAgICAgKiB7QGxpbmsgR29vZ2xlR2VuZXJhdGl2ZUFJLmdldEdlbmVyYXRpdmVNb2RlbCB9LlxuICAgICAqL1xuICAgIGFzeW5jIGNvdW50VG9rZW5zKHJlcXVlc3QsIHJlcXVlc3RPcHRpb25zID0ge30pIHtcbiAgICAgICAgY29uc3QgZm9ybWF0dGVkUGFyYW1zID0gZm9ybWF0Q291bnRUb2tlbnNJbnB1dChyZXF1ZXN0LCB7XG4gICAgICAgICAgICBtb2RlbDogdGhpcy5tb2RlbCxcbiAgICAgICAgICAgIGdlbmVyYXRpb25Db25maWc6IHRoaXMuZ2VuZXJhdGlvbkNvbmZpZyxcbiAgICAgICAgICAgIHNhZmV0eVNldHRpbmdzOiB0aGlzLnNhZmV0eVNldHRpbmdzLFxuICAgICAgICAgICAgdG9vbHM6IHRoaXMudG9vbHMsXG4gICAgICAgICAgICB0b29sQ29uZmlnOiB0aGlzLnRvb2xDb25maWcsXG4gICAgICAgICAgICBzeXN0ZW1JbnN0cnVjdGlvbjogdGhpcy5zeXN0ZW1JbnN0cnVjdGlvbixcbiAgICAgICAgICAgIGNhY2hlZENvbnRlbnQ6IHRoaXMuY2FjaGVkQ29udGVudCxcbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnN0IGdlbmVyYXRpdmVNb2RlbFJlcXVlc3RPcHRpb25zID0gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCB0aGlzLl9yZXF1ZXN0T3B0aW9ucyksIHJlcXVlc3RPcHRpb25zKTtcbiAgICAgICAgcmV0dXJuIGNvdW50VG9rZW5zKHRoaXMuYXBpS2V5LCB0aGlzLm1vZGVsLCBmb3JtYXR0ZWRQYXJhbXMsIGdlbmVyYXRpdmVNb2RlbFJlcXVlc3RPcHRpb25zKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRW1iZWRzIHRoZSBwcm92aWRlZCBjb250ZW50LlxuICAgICAqXG4gICAgICogRmllbGRzIHNldCBpbiB0aGUgb3B0aW9uYWwge0BsaW5rIFNpbmdsZVJlcXVlc3RPcHRpb25zfSBwYXJhbWV0ZXIgd2lsbFxuICAgICAqIHRha2UgcHJlY2VkZW5jZSBvdmVyIHRoZSB7QGxpbmsgUmVxdWVzdE9wdGlvbnN9IHZhbHVlcyBwcm92aWRlZCB0b1xuICAgICAqIHtAbGluayBHb29nbGVHZW5lcmF0aXZlQUkuZ2V0R2VuZXJhdGl2ZU1vZGVsIH0uXG4gICAgICovXG4gICAgYXN5bmMgZW1iZWRDb250ZW50KHJlcXVlc3QsIHJlcXVlc3RPcHRpb25zID0ge30pIHtcbiAgICAgICAgY29uc3QgZm9ybWF0dGVkUGFyYW1zID0gZm9ybWF0RW1iZWRDb250ZW50SW5wdXQocmVxdWVzdCk7XG4gICAgICAgIGNvbnN0IGdlbmVyYXRpdmVNb2RlbFJlcXVlc3RPcHRpb25zID0gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCB0aGlzLl9yZXF1ZXN0T3B0aW9ucyksIHJlcXVlc3RPcHRpb25zKTtcbiAgICAgICAgcmV0dXJuIGVtYmVkQ29udGVudCh0aGlzLmFwaUtleSwgdGhpcy5tb2RlbCwgZm9ybWF0dGVkUGFyYW1zLCBnZW5lcmF0aXZlTW9kZWxSZXF1ZXN0T3B0aW9ucyk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEVtYmVkcyBhbiBhcnJheSBvZiB7QGxpbmsgRW1iZWRDb250ZW50UmVxdWVzdH1zLlxuICAgICAqXG4gICAgICogRmllbGRzIHNldCBpbiB0aGUgb3B0aW9uYWwge0BsaW5rIFNpbmdsZVJlcXVlc3RPcHRpb25zfSBwYXJhbWV0ZXIgd2lsbFxuICAgICAqIHRha2UgcHJlY2VkZW5jZSBvdmVyIHRoZSB7QGxpbmsgUmVxdWVzdE9wdGlvbnN9IHZhbHVlcyBwcm92aWRlZCB0b1xuICAgICAqIHtAbGluayBHb29nbGVHZW5lcmF0aXZlQUkuZ2V0R2VuZXJhdGl2ZU1vZGVsIH0uXG4gICAgICovXG4gICAgYXN5bmMgYmF0Y2hFbWJlZENvbnRlbnRzKGJhdGNoRW1iZWRDb250ZW50UmVxdWVzdCwgcmVxdWVzdE9wdGlvbnMgPSB7fSkge1xuICAgICAgICBjb25zdCBnZW5lcmF0aXZlTW9kZWxSZXF1ZXN0T3B0aW9ucyA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5fcmVxdWVzdE9wdGlvbnMpLCByZXF1ZXN0T3B0aW9ucyk7XG4gICAgICAgIHJldHVybiBiYXRjaEVtYmVkQ29udGVudHModGhpcy5hcGlLZXksIHRoaXMubW9kZWwsIGJhdGNoRW1iZWRDb250ZW50UmVxdWVzdCwgZ2VuZXJhdGl2ZU1vZGVsUmVxdWVzdE9wdGlvbnMpO1xuICAgIH1cbn1cblxuLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IDIwMjQgR29vZ2xlIExMQ1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8qKlxuICogVG9wLWxldmVsIGNsYXNzIGZvciB0aGlzIFNES1xuICogQHB1YmxpY1xuICovXG5jbGFzcyBHb29nbGVHZW5lcmF0aXZlQUkge1xuICAgIGNvbnN0cnVjdG9yKGFwaUtleSkge1xuICAgICAgICB0aGlzLmFwaUtleSA9IGFwaUtleTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0cyBhIHtAbGluayBHZW5lcmF0aXZlTW9kZWx9IGluc3RhbmNlIGZvciB0aGUgcHJvdmlkZWQgbW9kZWwgbmFtZS5cbiAgICAgKi9cbiAgICBnZXRHZW5lcmF0aXZlTW9kZWwobW9kZWxQYXJhbXMsIHJlcXVlc3RPcHRpb25zKSB7XG4gICAgICAgIGlmICghbW9kZWxQYXJhbXMubW9kZWwpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBHb29nbGVHZW5lcmF0aXZlQUlFcnJvcihgTXVzdCBwcm92aWRlIGEgbW9kZWwgbmFtZS4gYCArXG4gICAgICAgICAgICAgICAgYEV4YW1wbGU6IGdlbmFpLmdldEdlbmVyYXRpdmVNb2RlbCh7IG1vZGVsOiAnbXktbW9kZWwtbmFtZScgfSlgKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbmV3IEdlbmVyYXRpdmVNb2RlbCh0aGlzLmFwaUtleSwgbW9kZWxQYXJhbXMsIHJlcXVlc3RPcHRpb25zKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ3JlYXRlcyBhIHtAbGluayBHZW5lcmF0aXZlTW9kZWx9IGluc3RhbmNlIGZyb20gcHJvdmlkZWQgY29udGVudCBjYWNoZS5cbiAgICAgKi9cbiAgICBnZXRHZW5lcmF0aXZlTW9kZWxGcm9tQ2FjaGVkQ29udGVudChjYWNoZWRDb250ZW50LCBtb2RlbFBhcmFtcywgcmVxdWVzdE9wdGlvbnMpIHtcbiAgICAgICAgaWYgKCFjYWNoZWRDb250ZW50Lm5hbWUpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBHb29nbGVHZW5lcmF0aXZlQUlSZXF1ZXN0SW5wdXRFcnJvcihcIkNhY2hlZCBjb250ZW50IG11c3QgY29udGFpbiBhIGBuYW1lYCBmaWVsZC5cIik7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFjYWNoZWRDb250ZW50Lm1vZGVsKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJUmVxdWVzdElucHV0RXJyb3IoXCJDYWNoZWQgY29udGVudCBtdXN0IGNvbnRhaW4gYSBgbW9kZWxgIGZpZWxkLlwiKTtcbiAgICAgICAgfVxuICAgICAgICAvKipcbiAgICAgICAgICogTm90IGNoZWNraW5nIHRvb2xzIGFuZCB0b29sQ29uZmlnIGZvciBub3cgYXMgaXQgd291bGQgcmVxdWlyZSBhIGRlZXBcbiAgICAgICAgICogZXF1YWxpdHkgY29tcGFyaXNvbiBhbmQgaXNuJ3QgbGlrZWx5IHRvIGJlIGEgY29tbW9uIGNhc2UuXG4gICAgICAgICAqL1xuICAgICAgICBjb25zdCBkaXNhbGxvd2VkRHVwbGljYXRlcyA9IFtcIm1vZGVsXCIsIFwic3lzdGVtSW5zdHJ1Y3Rpb25cIl07XG4gICAgICAgIGZvciAoY29uc3Qga2V5IG9mIGRpc2FsbG93ZWREdXBsaWNhdGVzKSB7XG4gICAgICAgICAgICBpZiAoKG1vZGVsUGFyYW1zID09PSBudWxsIHx8IG1vZGVsUGFyYW1zID09PSB2b2lkIDAgPyB2b2lkIDAgOiBtb2RlbFBhcmFtc1trZXldKSAmJlxuICAgICAgICAgICAgICAgIGNhY2hlZENvbnRlbnRba2V5XSAmJlxuICAgICAgICAgICAgICAgIChtb2RlbFBhcmFtcyA9PT0gbnVsbCB8fCBtb2RlbFBhcmFtcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogbW9kZWxQYXJhbXNba2V5XSkgIT09IGNhY2hlZENvbnRlbnRba2V5XSkge1xuICAgICAgICAgICAgICAgIGlmIChrZXkgPT09IFwibW9kZWxcIikge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBtb2RlbFBhcmFtc0NvbXAgPSBtb2RlbFBhcmFtcy5tb2RlbC5zdGFydHNXaXRoKFwibW9kZWxzL1wiKVxuICAgICAgICAgICAgICAgICAgICAgICAgPyBtb2RlbFBhcmFtcy5tb2RlbC5yZXBsYWNlKFwibW9kZWxzL1wiLCBcIlwiKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiBtb2RlbFBhcmFtcy5tb2RlbDtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgY2FjaGVkQ29udGVudENvbXAgPSBjYWNoZWRDb250ZW50Lm1vZGVsLnN0YXJ0c1dpdGgoXCJtb2RlbHMvXCIpXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGNhY2hlZENvbnRlbnQubW9kZWwucmVwbGFjZShcIm1vZGVscy9cIiwgXCJcIilcbiAgICAgICAgICAgICAgICAgICAgICAgIDogY2FjaGVkQ29udGVudC5tb2RlbDtcbiAgICAgICAgICAgICAgICAgICAgaWYgKG1vZGVsUGFyYW1zQ29tcCA9PT0gY2FjaGVkQ29udGVudENvbXApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBHb29nbGVHZW5lcmF0aXZlQUlSZXF1ZXN0SW5wdXRFcnJvcihgRGlmZmVyZW50IHZhbHVlIGZvciBcIiR7a2V5fVwiIHNwZWNpZmllZCBpbiBtb2RlbFBhcmFtc2AgK1xuICAgICAgICAgICAgICAgICAgICBgICgke21vZGVsUGFyYW1zW2tleV19KSBhbmQgY2FjaGVkQ29udGVudCAoJHtjYWNoZWRDb250ZW50W2tleV19KWApO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IG1vZGVsUGFyYW1zRnJvbUNhY2hlID0gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBtb2RlbFBhcmFtcyksIHsgbW9kZWw6IGNhY2hlZENvbnRlbnQubW9kZWwsIHRvb2xzOiBjYWNoZWRDb250ZW50LnRvb2xzLCB0b29sQ29uZmlnOiBjYWNoZWRDb250ZW50LnRvb2xDb25maWcsIHN5c3RlbUluc3RydWN0aW9uOiBjYWNoZWRDb250ZW50LnN5c3RlbUluc3RydWN0aW9uLCBjYWNoZWRDb250ZW50IH0pO1xuICAgICAgICByZXR1cm4gbmV3IEdlbmVyYXRpdmVNb2RlbCh0aGlzLmFwaUtleSwgbW9kZWxQYXJhbXNGcm9tQ2FjaGUsIHJlcXVlc3RPcHRpb25zKTtcbiAgICB9XG59XG5cbmV4cG9ydHMuQ2hhdFNlc3Npb24gPSBDaGF0U2Vzc2lvbjtcbmV4cG9ydHMuR2VuZXJhdGl2ZU1vZGVsID0gR2VuZXJhdGl2ZU1vZGVsO1xuZXhwb3J0cy5Hb29nbGVHZW5lcmF0aXZlQUkgPSBHb29nbGVHZW5lcmF0aXZlQUk7XG5leHBvcnRzLkdvb2dsZUdlbmVyYXRpdmVBSUVycm9yID0gR29vZ2xlR2VuZXJhdGl2ZUFJRXJyb3I7XG5leHBvcnRzLkdvb2dsZUdlbmVyYXRpdmVBSUZldGNoRXJyb3IgPSBHb29nbGVHZW5lcmF0aXZlQUlGZXRjaEVycm9yO1xuZXhwb3J0cy5Hb29nbGVHZW5lcmF0aXZlQUlSZXF1ZXN0SW5wdXRFcnJvciA9IEdvb2dsZUdlbmVyYXRpdmVBSVJlcXVlc3RJbnB1dEVycm9yO1xuZXhwb3J0cy5Hb29nbGVHZW5lcmF0aXZlQUlSZXNwb25zZUVycm9yID0gR29vZ2xlR2VuZXJhdGl2ZUFJUmVzcG9uc2VFcnJvcjtcbmV4cG9ydHMuUE9TU0lCTEVfUk9MRVMgPSBQT1NTSUJMRV9ST0xFUztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@google/generative-ai/dist/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@google/generative-ai/dist/server/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@google/generative-ai/dist/server/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar fs = __webpack_require__(/*! fs */ \"fs\");\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Basic error type for this SDK.\n * @public\n */\nclass GoogleGenerativeAIError extends Error {\n    constructor(message) {\n        super(`[GoogleGenerativeAI Error]: ${message}`);\n    }\n}\n/**\n * Error class covering HTTP errors when calling the server. Includes HTTP\n * status, statusText, and optional details, if provided in the server response.\n * @public\n */\nclass GoogleGenerativeAIFetchError extends GoogleGenerativeAIError {\n    constructor(message, status, statusText, errorDetails) {\n        super(message);\n        this.status = status;\n        this.statusText = statusText;\n        this.errorDetails = errorDetails;\n    }\n}\n/**\n * Errors in the contents of a request originating from user input.\n * @public\n */\nclass GoogleGenerativeAIRequestInputError extends GoogleGenerativeAIError {\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_BASE_URL = \"https://generativelanguage.googleapis.com\";\nconst DEFAULT_API_VERSION = \"v1beta\";\n/**\n * We can't `require` package.json if this runs on web. We will use rollup to\n * swap in the version number here at build time.\n */\nconst PACKAGE_VERSION = \"0.21.0\";\nconst PACKAGE_LOG_HEADER = \"genai-js\";\nvar Task;\n(function (Task) {\n    Task[\"GENERATE_CONTENT\"] = \"generateContent\";\n    Task[\"STREAM_GENERATE_CONTENT\"] = \"streamGenerateContent\";\n    Task[\"COUNT_TOKENS\"] = \"countTokens\";\n    Task[\"EMBED_CONTENT\"] = \"embedContent\";\n    Task[\"BATCH_EMBED_CONTENTS\"] = \"batchEmbedContents\";\n})(Task || (Task = {}));\n/**\n * Simple, but may become more complex if we add more versions to log.\n */\nfunction getClientHeaders(requestOptions) {\n    const clientHeaders = [];\n    if (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiClient) {\n        clientHeaders.push(requestOptions.apiClient);\n    }\n    clientHeaders.push(`${PACKAGE_LOG_HEADER}/${PACKAGE_VERSION}`);\n    return clientHeaders.join(\" \");\n}\nasync function makeRequest(url, fetchOptions, fetchFn = fetch) {\n    let response;\n    try {\n        response = await fetchFn(url, fetchOptions);\n    }\n    catch (e) {\n        handleResponseError(e, url);\n    }\n    if (!response.ok) {\n        await handleResponseNotOk(response, url);\n    }\n    return response;\n}\nfunction handleResponseError(e, url) {\n    let err = e;\n    if (!(e instanceof GoogleGenerativeAIFetchError ||\n        e instanceof GoogleGenerativeAIRequestInputError)) {\n        err = new GoogleGenerativeAIError(`Error fetching from ${url.toString()}: ${e.message}`);\n        err.stack = e.stack;\n    }\n    throw err;\n}\nasync function handleResponseNotOk(response, url) {\n    let message = \"\";\n    let errorDetails;\n    try {\n        const json = await response.json();\n        message = json.error.message;\n        if (json.error.details) {\n            message += ` ${JSON.stringify(json.error.details)}`;\n            errorDetails = json.error.details;\n        }\n    }\n    catch (e) {\n        // ignored\n    }\n    throw new GoogleGenerativeAIFetchError(`Error fetching from ${url.toString()}: [${response.status} ${response.statusText}] ${message}`, response.status, response.statusText, errorDetails);\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar RpcTask;\n(function (RpcTask) {\n    RpcTask[\"UPLOAD\"] = \"upload\";\n    RpcTask[\"LIST\"] = \"list\";\n    RpcTask[\"GET\"] = \"get\";\n    RpcTask[\"DELETE\"] = \"delete\";\n    RpcTask[\"UPDATE\"] = \"update\";\n    RpcTask[\"CREATE\"] = \"create\";\n})(RpcTask || (RpcTask = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst taskToMethod = {\n    [RpcTask.UPLOAD]: \"POST\",\n    [RpcTask.LIST]: \"GET\",\n    [RpcTask.GET]: \"GET\",\n    [RpcTask.DELETE]: \"DELETE\",\n    [RpcTask.UPDATE]: \"PATCH\",\n    [RpcTask.CREATE]: \"POST\",\n};\nclass ServerRequestUrl {\n    constructor(task, apiKey, requestOptions) {\n        this.task = task;\n        this.apiKey = apiKey;\n        this.requestOptions = requestOptions;\n    }\n    appendPath(path) {\n        this._url.pathname = this._url.pathname + `/${path}`;\n    }\n    appendParam(key, value) {\n        this._url.searchParams.append(key, value);\n    }\n    toString() {\n        return this._url.toString();\n    }\n}\nclass CachedContentUrl extends ServerRequestUrl {\n    constructor(task, apiKey, requestOptions) {\n        var _a, _b;\n        super(task, apiKey, requestOptions);\n        this.task = task;\n        this.apiKey = apiKey;\n        this.requestOptions = requestOptions;\n        const apiVersion = ((_a = this.requestOptions) === null || _a === void 0 ? void 0 : _a.apiVersion) || DEFAULT_API_VERSION;\n        const baseUrl = ((_b = this.requestOptions) === null || _b === void 0 ? void 0 : _b.baseUrl) || DEFAULT_BASE_URL;\n        let initialUrl = baseUrl;\n        initialUrl += `/${apiVersion}/cachedContents`;\n        this._url = new URL(initialUrl);\n    }\n}\nclass FilesRequestUrl extends ServerRequestUrl {\n    constructor(task, apiKey, requestOptions) {\n        var _a, _b;\n        super(task, apiKey, requestOptions);\n        this.task = task;\n        this.apiKey = apiKey;\n        this.requestOptions = requestOptions;\n        const apiVersion = ((_a = this.requestOptions) === null || _a === void 0 ? void 0 : _a.apiVersion) || DEFAULT_API_VERSION;\n        const baseUrl = ((_b = this.requestOptions) === null || _b === void 0 ? void 0 : _b.baseUrl) || DEFAULT_BASE_URL;\n        let initialUrl = baseUrl;\n        if (this.task === RpcTask.UPLOAD) {\n            initialUrl += `/upload`;\n        }\n        initialUrl += `/${apiVersion}/files`;\n        this._url = new URL(initialUrl);\n    }\n}\nfunction getHeaders(url) {\n    const headers = new Headers();\n    headers.append(\"x-goog-api-client\", getClientHeaders(url.requestOptions));\n    headers.append(\"x-goog-api-key\", url.apiKey);\n    return headers;\n}\nasync function makeServerRequest(url, headers, body, fetchFn = fetch) {\n    const requestInit = {\n        method: taskToMethod[url.task],\n        headers,\n    };\n    if (body) {\n        requestInit.body = body;\n    }\n    const signal = getSignal(url.requestOptions);\n    if (signal) {\n        requestInit.signal = signal;\n    }\n    return makeRequest(url.toString(), requestInit, fetchFn);\n}\n/**\n * Create an AbortSignal based on the timeout and signal in the\n * RequestOptions.\n */\nfunction getSignal(requestOptions) {\n    if ((requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.signal) !== undefined || (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeout) >= 0) {\n        const controller = new AbortController();\n        if ((requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeout) >= 0) {\n            setTimeout(() => controller.abort(), requestOptions.timeout);\n        }\n        if (requestOptions.signal) {\n            requestOptions.signal.addEventListener(\"abort\", () => {\n                controller.abort();\n            });\n        }\n        return controller.signal;\n    }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Class for managing GoogleAI file uploads.\n * @public\n */\nclass GoogleAIFileManager {\n    constructor(apiKey, _requestOptions = {}) {\n        this.apiKey = apiKey;\n        this._requestOptions = _requestOptions;\n    }\n    /**\n     * Upload a file.\n     */\n    async uploadFile(filePath, fileMetadata) {\n        const file = fs.readFileSync(filePath);\n        const url = new FilesRequestUrl(RpcTask.UPLOAD, this.apiKey, this._requestOptions);\n        const uploadHeaders = getHeaders(url);\n        const boundary = generateBoundary();\n        uploadHeaders.append(\"X-Goog-Upload-Protocol\", \"multipart\");\n        uploadHeaders.append(\"Content-Type\", `multipart/related; boundary=${boundary}`);\n        const uploadMetadata = getUploadMetadata(fileMetadata);\n        // Multipart formatting code taken from @firebase/storage\n        const metadataString = JSON.stringify({ file: uploadMetadata });\n        const preBlobPart = \"--\" +\n            boundary +\n            \"\\r\\n\" +\n            \"Content-Type: application/json; charset=utf-8\\r\\n\\r\\n\" +\n            metadataString +\n            \"\\r\\n--\" +\n            boundary +\n            \"\\r\\n\" +\n            \"Content-Type: \" +\n            fileMetadata.mimeType +\n            \"\\r\\n\\r\\n\";\n        const postBlobPart = \"\\r\\n--\" + boundary + \"--\";\n        const blob = new Blob([preBlobPart, file, postBlobPart]);\n        const response = await makeServerRequest(url, uploadHeaders, blob);\n        return response.json();\n    }\n    /**\n     * List all uploaded files.\n     *\n     * Any fields set in the optional {@link SingleRequestOptions} parameter will take\n     * precedence over the {@link RequestOptions} values provided at the time of the\n     * {@link GoogleAIFileManager} initialization.\n     */\n    async listFiles(listParams, requestOptions = {}) {\n        const filesRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        const url = new FilesRequestUrl(RpcTask.LIST, this.apiKey, filesRequestOptions);\n        if (listParams === null || listParams === void 0 ? void 0 : listParams.pageSize) {\n            url.appendParam(\"pageSize\", listParams.pageSize.toString());\n        }\n        if (listParams === null || listParams === void 0 ? void 0 : listParams.pageToken) {\n            url.appendParam(\"pageToken\", listParams.pageToken);\n        }\n        const uploadHeaders = getHeaders(url);\n        const response = await makeServerRequest(url, uploadHeaders);\n        return response.json();\n    }\n    /**\n     * Get metadata for file with given ID.\n     *\n     * Any fields set in the optional {@link SingleRequestOptions} parameter will take\n     * precedence over the {@link RequestOptions} values provided at the time of the\n     * {@link GoogleAIFileManager} initialization.\n     */\n    async getFile(fileId, requestOptions = {}) {\n        const filesRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        const url = new FilesRequestUrl(RpcTask.GET, this.apiKey, filesRequestOptions);\n        url.appendPath(parseFileId(fileId));\n        const uploadHeaders = getHeaders(url);\n        const response = await makeServerRequest(url, uploadHeaders);\n        return response.json();\n    }\n    /**\n     * Delete file with given ID.\n     */\n    async deleteFile(fileId) {\n        const url = new FilesRequestUrl(RpcTask.DELETE, this.apiKey, this._requestOptions);\n        url.appendPath(parseFileId(fileId));\n        const uploadHeaders = getHeaders(url);\n        await makeServerRequest(url, uploadHeaders);\n    }\n}\n/**\n * If fileId is prepended with \"files/\", remove prefix\n */\nfunction parseFileId(fileId) {\n    if (fileId.startsWith(\"files/\")) {\n        return fileId.split(\"files/\")[1];\n    }\n    if (!fileId) {\n        throw new GoogleGenerativeAIError(`Invalid fileId ${fileId}. ` +\n            `Must be in the format \"files/filename\" or \"filename\"`);\n    }\n    return fileId;\n}\nfunction generateBoundary() {\n    let str = \"\";\n    for (let i = 0; i < 2; i++) {\n        str = str + Math.random().toString().slice(2);\n    }\n    return str;\n}\nfunction getUploadMetadata(inputMetadata) {\n    if (!inputMetadata.mimeType) {\n        throw new GoogleGenerativeAIRequestInputError(\"Must provide a mimeType.\");\n    }\n    const uploadMetadata = {\n        mimeType: inputMetadata.mimeType,\n        displayName: inputMetadata.displayName,\n    };\n    if (inputMetadata.name) {\n        uploadMetadata.name = inputMetadata.name.includes(\"/\")\n            ? inputMetadata.name\n            : `files/${inputMetadata.name}`;\n    }\n    return uploadMetadata;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction formatSystemInstruction(input) {\n    // null or undefined\n    if (input == null) {\n        return undefined;\n    }\n    else if (typeof input === \"string\") {\n        return { role: \"system\", parts: [{ text: input }] };\n    }\n    else if (input.text) {\n        return { role: \"system\", parts: [input] };\n    }\n    else if (input.parts) {\n        if (!input.role) {\n            return { role: \"system\", parts: input.parts };\n        }\n        else {\n            return input;\n        }\n    }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Class for managing GoogleAI content caches.\n * @public\n */\nclass GoogleAICacheManager {\n    constructor(apiKey, _requestOptions) {\n        this.apiKey = apiKey;\n        this._requestOptions = _requestOptions;\n    }\n    /**\n     * Upload a new content cache\n     */\n    async create(createOptions) {\n        const newCachedContent = Object.assign({}, createOptions);\n        if (createOptions.ttlSeconds) {\n            if (createOptions.expireTime) {\n                throw new GoogleGenerativeAIRequestInputError(\"You cannot specify both `ttlSeconds` and `expireTime` when creating\" +\n                    \" a content cache. You must choose one.\");\n            }\n            if (createOptions.systemInstruction) {\n                newCachedContent.systemInstruction = formatSystemInstruction(createOptions.systemInstruction);\n            }\n            newCachedContent.ttl = createOptions.ttlSeconds.toString() + \"s\";\n            delete newCachedContent.ttlSeconds;\n        }\n        if (!newCachedContent.model) {\n            throw new GoogleGenerativeAIRequestInputError(\"Cached content must contain a `model` field.\");\n        }\n        if (!newCachedContent.model.includes(\"/\")) {\n            // If path is not included, assume it's a non-tuned model.\n            newCachedContent.model = `models/${newCachedContent.model}`;\n        }\n        const url = new CachedContentUrl(RpcTask.CREATE, this.apiKey, this._requestOptions);\n        const headers = getHeaders(url);\n        const response = await makeServerRequest(url, headers, JSON.stringify(newCachedContent));\n        return response.json();\n    }\n    /**\n     * List all uploaded content caches\n     */\n    async list(listParams) {\n        const url = new CachedContentUrl(RpcTask.LIST, this.apiKey, this._requestOptions);\n        if (listParams === null || listParams === void 0 ? void 0 : listParams.pageSize) {\n            url.appendParam(\"pageSize\", listParams.pageSize.toString());\n        }\n        if (listParams === null || listParams === void 0 ? void 0 : listParams.pageToken) {\n            url.appendParam(\"pageToken\", listParams.pageToken);\n        }\n        const headers = getHeaders(url);\n        const response = await makeServerRequest(url, headers);\n        return response.json();\n    }\n    /**\n     * Get a content cache\n     */\n    async get(name) {\n        const url = new CachedContentUrl(RpcTask.GET, this.apiKey, this._requestOptions);\n        url.appendPath(parseCacheName(name));\n        const headers = getHeaders(url);\n        const response = await makeServerRequest(url, headers);\n        return response.json();\n    }\n    /**\n     * Update an existing content cache\n     */\n    async update(name, updateParams) {\n        const url = new CachedContentUrl(RpcTask.UPDATE, this.apiKey, this._requestOptions);\n        url.appendPath(parseCacheName(name));\n        const headers = getHeaders(url);\n        const formattedCachedContent = Object.assign({}, updateParams.cachedContent);\n        if (updateParams.cachedContent.ttlSeconds) {\n            formattedCachedContent.ttl =\n                updateParams.cachedContent.ttlSeconds.toString() + \"s\";\n            delete formattedCachedContent.ttlSeconds;\n        }\n        if (updateParams.updateMask) {\n            url.appendParam(\"update_mask\", updateParams.updateMask.map((prop) => camelToSnake(prop)).join(\",\"));\n        }\n        const response = await makeServerRequest(url, headers, JSON.stringify(formattedCachedContent));\n        return response.json();\n    }\n    /**\n     * Delete content cache with given name\n     */\n    async delete(name) {\n        const url = new CachedContentUrl(RpcTask.DELETE, this.apiKey, this._requestOptions);\n        url.appendPath(parseCacheName(name));\n        const headers = getHeaders(url);\n        await makeServerRequest(url, headers);\n    }\n}\n/**\n * If cache name is prepended with \"cachedContents/\", remove prefix\n */\nfunction parseCacheName(name) {\n    if (name.startsWith(\"cachedContents/\")) {\n        return name.split(\"cachedContents/\")[1];\n    }\n    if (!name) {\n        throw new GoogleGenerativeAIError(`Invalid name ${name}. ` +\n            `Must be in the format \"cachedContents/name\" or \"name\"`);\n    }\n    return name;\n}\nfunction camelToSnake(str) {\n    return str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);\n}\n\n/**\n * Processing state of the `File`.\n * @public\n */\nexports.FileState = void 0;\n(function (FileState) {\n    // The default value. This value is used if the state is omitted.\n    FileState[\"STATE_UNSPECIFIED\"] = \"STATE_UNSPECIFIED\";\n    // File is being processed and cannot be used for inference yet.\n    FileState[\"PROCESSING\"] = \"PROCESSING\";\n    // File is processed and available for inference.\n    FileState[\"ACTIVE\"] = \"ACTIVE\";\n    // File failed processing.\n    FileState[\"FAILED\"] = \"FAILED\";\n})(exports.FileState || (exports.FileState = {}));\n\n/**\n * Contains the list of OpenAPI data types\n * as defined by https://swagger.io/docs/specification/data-models/data-types/\n * @public\n */\nexports.SchemaType = void 0;\n(function (SchemaType) {\n    /** String type. */\n    SchemaType[\"STRING\"] = \"string\";\n    /** Number type. */\n    SchemaType[\"NUMBER\"] = \"number\";\n    /** Integer type. */\n    SchemaType[\"INTEGER\"] = \"integer\";\n    /** Boolean type. */\n    SchemaType[\"BOOLEAN\"] = \"boolean\";\n    /** Array type. */\n    SchemaType[\"ARRAY\"] = \"array\";\n    /** Object type. */\n    SchemaType[\"OBJECT\"] = \"object\";\n})(exports.SchemaType || (exports.SchemaType = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @public\n */\nexports.ExecutableCodeLanguage = void 0;\n(function (ExecutableCodeLanguage) {\n    ExecutableCodeLanguage[\"LANGUAGE_UNSPECIFIED\"] = \"language_unspecified\";\n    ExecutableCodeLanguage[\"PYTHON\"] = \"python\";\n})(exports.ExecutableCodeLanguage || (exports.ExecutableCodeLanguage = {}));\n/**\n * Possible outcomes of code execution.\n * @public\n */\nexports.Outcome = void 0;\n(function (Outcome) {\n    /**\n     * Unspecified status. This value should not be used.\n     */\n    Outcome[\"OUTCOME_UNSPECIFIED\"] = \"outcome_unspecified\";\n    /**\n     * Code execution completed successfully.\n     */\n    Outcome[\"OUTCOME_OK\"] = \"outcome_ok\";\n    /**\n     * Code execution finished but with a failure. `stderr` should contain the\n     * reason.\n     */\n    Outcome[\"OUTCOME_FAILED\"] = \"outcome_failed\";\n    /**\n     * Code execution ran for too long, and was cancelled. There may or may not\n     * be a partial output present.\n     */\n    Outcome[\"OUTCOME_DEADLINE_EXCEEDED\"] = \"outcome_deadline_exceeded\";\n})(exports.Outcome || (exports.Outcome = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Possible roles.\n * @public\n */\n/**\n * Harm categories that would cause prompts or candidates to be blocked.\n * @public\n */\nvar HarmCategory;\n(function (HarmCategory) {\n    HarmCategory[\"HARM_CATEGORY_UNSPECIFIED\"] = \"HARM_CATEGORY_UNSPECIFIED\";\n    HarmCategory[\"HARM_CATEGORY_HATE_SPEECH\"] = \"HARM_CATEGORY_HATE_SPEECH\";\n    HarmCategory[\"HARM_CATEGORY_SEXUALLY_EXPLICIT\"] = \"HARM_CATEGORY_SEXUALLY_EXPLICIT\";\n    HarmCategory[\"HARM_CATEGORY_HARASSMENT\"] = \"HARM_CATEGORY_HARASSMENT\";\n    HarmCategory[\"HARM_CATEGORY_DANGEROUS_CONTENT\"] = \"HARM_CATEGORY_DANGEROUS_CONTENT\";\n})(HarmCategory || (HarmCategory = {}));\n/**\n * Threshold above which a prompt or candidate will be blocked.\n * @public\n */\nvar HarmBlockThreshold;\n(function (HarmBlockThreshold) {\n    // Threshold is unspecified.\n    HarmBlockThreshold[\"HARM_BLOCK_THRESHOLD_UNSPECIFIED\"] = \"HARM_BLOCK_THRESHOLD_UNSPECIFIED\";\n    // Content with NEGLIGIBLE will be allowed.\n    HarmBlockThreshold[\"BLOCK_LOW_AND_ABOVE\"] = \"BLOCK_LOW_AND_ABOVE\";\n    // Content with NEGLIGIBLE and LOW will be allowed.\n    HarmBlockThreshold[\"BLOCK_MEDIUM_AND_ABOVE\"] = \"BLOCK_MEDIUM_AND_ABOVE\";\n    // Content with NEGLIGIBLE, LOW, and MEDIUM will be allowed.\n    HarmBlockThreshold[\"BLOCK_ONLY_HIGH\"] = \"BLOCK_ONLY_HIGH\";\n    // All content will be allowed.\n    HarmBlockThreshold[\"BLOCK_NONE\"] = \"BLOCK_NONE\";\n})(HarmBlockThreshold || (HarmBlockThreshold = {}));\n/**\n * Probability that a prompt or candidate matches a harm category.\n * @public\n */\nvar HarmProbability;\n(function (HarmProbability) {\n    // Probability is unspecified.\n    HarmProbability[\"HARM_PROBABILITY_UNSPECIFIED\"] = \"HARM_PROBABILITY_UNSPECIFIED\";\n    // Content has a negligible chance of being unsafe.\n    HarmProbability[\"NEGLIGIBLE\"] = \"NEGLIGIBLE\";\n    // Content has a low chance of being unsafe.\n    HarmProbability[\"LOW\"] = \"LOW\";\n    // Content has a medium chance of being unsafe.\n    HarmProbability[\"MEDIUM\"] = \"MEDIUM\";\n    // Content has a high chance of being unsafe.\n    HarmProbability[\"HIGH\"] = \"HIGH\";\n})(HarmProbability || (HarmProbability = {}));\n/**\n * Reason that a prompt was blocked.\n * @public\n */\nvar BlockReason;\n(function (BlockReason) {\n    // A blocked reason was not specified.\n    BlockReason[\"BLOCKED_REASON_UNSPECIFIED\"] = \"BLOCKED_REASON_UNSPECIFIED\";\n    // Content was blocked by safety settings.\n    BlockReason[\"SAFETY\"] = \"SAFETY\";\n    // Content was blocked, but the reason is uncategorized.\n    BlockReason[\"OTHER\"] = \"OTHER\";\n})(BlockReason || (BlockReason = {}));\n/**\n * Reason that a candidate finished.\n * @public\n */\nvar FinishReason;\n(function (FinishReason) {\n    // Default value. This value is unused.\n    FinishReason[\"FINISH_REASON_UNSPECIFIED\"] = \"FINISH_REASON_UNSPECIFIED\";\n    // Natural stop point of the model or provided stop sequence.\n    FinishReason[\"STOP\"] = \"STOP\";\n    // The maximum number of tokens as specified in the request was reached.\n    FinishReason[\"MAX_TOKENS\"] = \"MAX_TOKENS\";\n    // The candidate content was flagged for safety reasons.\n    FinishReason[\"SAFETY\"] = \"SAFETY\";\n    // The candidate content was flagged for recitation reasons.\n    FinishReason[\"RECITATION\"] = \"RECITATION\";\n    // The candidate content was flagged for using an unsupported language.\n    FinishReason[\"LANGUAGE\"] = \"LANGUAGE\";\n    // Unknown reason.\n    FinishReason[\"OTHER\"] = \"OTHER\";\n})(FinishReason || (FinishReason = {}));\n/**\n * Task type for embedding content.\n * @public\n */\nvar TaskType;\n(function (TaskType) {\n    TaskType[\"TASK_TYPE_UNSPECIFIED\"] = \"TASK_TYPE_UNSPECIFIED\";\n    TaskType[\"RETRIEVAL_QUERY\"] = \"RETRIEVAL_QUERY\";\n    TaskType[\"RETRIEVAL_DOCUMENT\"] = \"RETRIEVAL_DOCUMENT\";\n    TaskType[\"SEMANTIC_SIMILARITY\"] = \"SEMANTIC_SIMILARITY\";\n    TaskType[\"CLASSIFICATION\"] = \"CLASSIFICATION\";\n    TaskType[\"CLUSTERING\"] = \"CLUSTERING\";\n})(TaskType || (TaskType = {}));\n/**\n * @public\n */\nexports.FunctionCallingMode = void 0;\n(function (FunctionCallingMode) {\n    // Unspecified function calling mode. This value should not be used.\n    FunctionCallingMode[\"MODE_UNSPECIFIED\"] = \"MODE_UNSPECIFIED\";\n    // Default model behavior, model decides to predict either a function call\n    // or a natural language repspose.\n    FunctionCallingMode[\"AUTO\"] = \"AUTO\";\n    // Model is constrained to always predicting a function call only.\n    // If \"allowed_function_names\" are set, the predicted function call will be\n    // limited to any one of \"allowed_function_names\", else the predicted\n    // function call will be any one of the provided \"function_declarations\".\n    FunctionCallingMode[\"ANY\"] = \"ANY\";\n    // Model will not predict any function call. Model behavior is same as when\n    // not passing any function declarations.\n    FunctionCallingMode[\"NONE\"] = \"NONE\";\n})(exports.FunctionCallingMode || (exports.FunctionCallingMode = {}));\n/**\n * The mode of the predictor to be used in dynamic retrieval.\n * @public\n */\nvar DynamicRetrievalMode;\n(function (DynamicRetrievalMode) {\n    // Unspecified function calling mode. This value should not be used.\n    DynamicRetrievalMode[\"MODE_UNSPECIFIED\"] = \"MODE_UNSPECIFIED\";\n    // Run retrieval only when system decides it is necessary.\n    DynamicRetrievalMode[\"MODE_DYNAMIC\"] = \"MODE_DYNAMIC\";\n})(DynamicRetrievalMode || (DynamicRetrievalMode = {}));\n\nexports.GoogleAICacheManager = GoogleAICacheManager;\nexports.GoogleAIFileManager = GoogleAIFileManager;\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@google/generative-ai/dist/server/index.js\n");

/***/ })

};
;