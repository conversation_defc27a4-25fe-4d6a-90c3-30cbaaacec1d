/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/chat/route";
exports.ids = ["app/api/ai/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fchat%2Froute&page=%2Fapi%2Fai%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fchat%2Froute.ts&appDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fchat%2Froute&page=%2Fapi%2Fai%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fchat%2Froute.ts&appDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_projects_New_folder_Vice_Vice_src_app_api_ai_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/chat/route.ts */ \"(rsc)/./src/app/api/ai/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/chat/route\",\n        pathname: \"/api/ai/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/chat/route\"\n    },\n    resolvedPagePath: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\app\\\\api\\\\ai\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_projects_New_folder_Vice_Vice_src_app_api_ai_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fchat%2Froute&page=%2Fapi%2Fai%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fchat%2Froute.ts&appDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/chat/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/ai/chat/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\n// Initialize Gemini AI\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI('AIzaSyBd16TuIPxTFqvEtC-E-UQgsGTvnHJ51bQ');\n// Daily usage tracking\nconst getDailyUsageKey = ()=>{\n    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format\n    return `vice-ai-usage-${today}`;\n};\nconst checkDailyLimit = ()=>{\n    if (true) return {\n        allowed: true,\n        count: 0\n    }; // Server-side, allow\n    const usageKey = getDailyUsageKey();\n    const currentUsage = parseInt(localStorage.getItem(usageKey) || '0');\n    const dailyLimit = 3;\n    return {\n        allowed: currentUsage < dailyLimit,\n        count: currentUsage,\n        remaining: Math.max(0, dailyLimit - currentUsage)\n    };\n};\nconst incrementDailyUsage = ()=>{\n    if (true) return; // Server-side, skip\n    const usageKey = getDailyUsageKey();\n    const currentUsage = parseInt(localStorage.getItem(usageKey) || '0');\n    localStorage.setItem(usageKey, (currentUsage + 1).toString());\n};\nasync function POST(request) {\n    try {\n        console.log('[AI CHAT] 🚀 Received chat request');\n        const body = await request.json();\n        const { message, history = [], checkUsage = true } = body;\n        console.log('[AI CHAT] 📝 Message:', message);\n        console.log('[AI CHAT] 📚 History length:', history.length);\n        if (!message || typeof message !== 'string') {\n            console.log('[AI CHAT] ❌ Invalid message format');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Message is required and must be a string'\n            }, {\n                status: 400\n            });\n        }\n        // Check daily usage limit (client-side check will be done in frontend)\n        if (checkUsage) {\n            const userAgent = request.headers.get('user-agent') || '';\n            const isFromClient = userAgent.includes('Mozilla'); // Basic client detection\n            if (isFromClient) {\n                // For client requests, we'll rely on frontend validation\n                // Server-side tracking would require user authentication\n                console.log('[AI CHAT] 📊 Usage check requested from client');\n            }\n        }\n        // Strict check for coding/development topics only\n        const messageLower = message.toLowerCase().trim();\n        // First check for obvious non-coding topics that should be blocked\n        const nonTechKeywords = [\n            'weather',\n            'temperature',\n            'climate',\n            'rain',\n            'snow',\n            'sunny',\n            'cloudy',\n            'food',\n            'recipe',\n            'cooking',\n            'restaurant',\n            'meal',\n            'dinner',\n            'lunch',\n            'breakfast',\n            'movie',\n            'film',\n            'cinema',\n            'actor',\n            'actress',\n            'director',\n            'netflix',\n            'tv show',\n            'music',\n            'song',\n            'singer',\n            'band',\n            'album',\n            'concert',\n            'spotify',\n            'playlist',\n            'sports',\n            'football',\n            'basketball',\n            'soccer',\n            'tennis',\n            'baseball',\n            'hockey',\n            'politics',\n            'government',\n            'president',\n            'election',\n            'vote',\n            'politician',\n            'health',\n            'doctor',\n            'medicine',\n            'hospital',\n            'disease',\n            'symptoms',\n            'treatment',\n            'travel',\n            'vacation',\n            'hotel',\n            'flight',\n            'airport',\n            'tourism',\n            'destination',\n            'shopping',\n            'store',\n            'mall',\n            'buy',\n            'purchase',\n            'price',\n            'discount',\n            'sale',\n            'relationship',\n            'dating',\n            'marriage',\n            'family',\n            'friend',\n            'love',\n            'romance',\n            'education',\n            'school',\n            'university',\n            'college',\n            'student',\n            'teacher',\n            'homework',\n            'news',\n            'current events',\n            'breaking news',\n            'headline',\n            'article',\n            'journalist',\n            'fashion',\n            'clothes',\n            'style',\n            'outfit',\n            'brand',\n            'designer',\n            'trend',\n            'animal',\n            'pet',\n            'dog',\n            'cat',\n            'bird',\n            'fish',\n            'wildlife',\n            'zoo',\n            'history',\n            'historical',\n            'ancient',\n            'medieval',\n            'war',\n            'battle',\n            'empire',\n            'geography',\n            'country',\n            'city',\n            'capital',\n            'continent',\n            'ocean',\n            'mountain'\n        ];\n        // Check if message contains non-coding topics\n        const hasNonTechTopic = nonTechKeywords.some((keyword)=>messageLower.includes(keyword));\n        // Comprehensive coding keywords\n        const techKeywords = [\n            'code',\n            'coding',\n            'programming',\n            'development',\n            'software',\n            'app',\n            'application',\n            'website',\n            'web',\n            'program',\n            'script',\n            'algorithm',\n            'function',\n            'variable',\n            'javascript',\n            'python',\n            'java',\n            'html',\n            'css',\n            'react',\n            'node',\n            'typescript',\n            'api',\n            'database',\n            'framework',\n            'library',\n            'debug',\n            'error',\n            'bug',\n            'git',\n            'npm',\n            'build',\n            'deploy',\n            'server',\n            'client',\n            'frontend',\n            'backend',\n            'fullstack',\n            'component',\n            'interface',\n            'class',\n            'object',\n            'array',\n            'string',\n            'number',\n            'async',\n            'await',\n            'promise',\n            'callback',\n            'event',\n            'handler',\n            'method',\n            'property',\n            'parameter',\n            'return',\n            'import',\n            'export',\n            'module',\n            'package',\n            'version',\n            'install',\n            'compile',\n            'bundle',\n            'optimize',\n            'performance',\n            'testing',\n            'unit test',\n            'integration',\n            'security',\n            'authentication',\n            'authorization',\n            'encryption',\n            'token',\n            'jwt',\n            'rest',\n            'graphql',\n            'docker',\n            'kubernetes',\n            'aws',\n            'azure',\n            'cloud',\n            'devops',\n            'ci/cd',\n            'mongodb',\n            'sql',\n            'mysql',\n            'postgresql',\n            'redis',\n            'nosql',\n            'query'\n        ];\n        const hasTechKeywords = techKeywords.some((keyword)=>messageLower.includes(keyword));\n        // Check for coding-related patterns\n        const codingPatterns = [\n            /\\b(how to|how do i|how can i)\\b.*(code|program|build|create|develop|implement|make).*(app|website|function|component|feature|system)/i,\n            /\\b(what is|what are|explain|define)\\b.*(programming|coding|development|software|framework|library|api|database)/i,\n            /\\b(error|bug|issue|problem|exception|crash|fail)\\b.*(code|programming|development|app|website|function)/i,\n            /\\b(learn|tutorial|guide|example|course)\\b.*(programming|coding|development|web|app|software)/i,\n            /\\.(js|ts|jsx|tsx|py|java|cpp|cs|php|rb|go|rs|html|css|scss|json|xml|sql)(\\b|$)/i\n        ];\n        const hasCodePattern = codingPatterns.some((pattern)=>pattern.test(messageLower));\n        // Determine if it's tech-related\n        const isTechRelated = hasTechKeywords || hasCodePattern || messageLower.includes('vice') && (messageLower.includes('ide') || messageLower.includes('development'));\n        // Block if it has non-tech topics and no tech context\n        const isNonTech = hasNonTechTopic && !isTechRelated;\n        if (isNonTech) {\n            console.log('[AI CHAT] 🚫 Non-tech topic detected, blocking:', message.substring(0, 50) + '...');\n            // For clearly non-tech questions, give brief response and redirect\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response: `I'm Vice AI, a coding assistant focused exclusively on programming and development topics. I can only help with:\\n\\n• **Programming languages** (JavaScript, Python, React, etc.)\\n• **Web development** (HTML, CSS, frameworks, APIs)\\n• **Software engineering** (debugging, architecture, best practices)\\n• **Development tools** (Git, npm, IDEs, deployment)\\n• **Vice platform** features and workflows\\n\\nPlease ask me a coding or development-related question!`,\n                success: true,\n                restricted: true\n            });\n        }\n        console.log('[AI CHAT] ✅ Tech-related topic detected, processing with Gemini');\n        // Create the prompt with context for tech-related questions\n        const systemPrompt = `You are Vice AI, a professional coding assistant. You are EXCLUSIVELY focused on programming, web development, software engineering, and coding topics.\n\nCRITICAL RULES:\n1. NEVER start responses with \"Hey there\" or similar casual greetings\n2. Be direct and helpful - get straight to answering the question\n3. ONLY answer coding and development questions - refuse all other topics\n4. If a question is not about coding/development, respond: \"I'm Vice AI, a coding assistant. I only help with programming and development. Please ask a coding-related question!\"\n5. Explain programming concepts clearly with practical examples\n6. Provide actionable, practical advice for developers\n7. Keep responses helpful but not overly verbose\n8. STAY STRICTLY ON TOPIC - only discuss coding, development, and technical subjects\n\nEXPERTISE AREAS (ONLY respond to these topics):\n- Programming languages: JavaScript, TypeScript, Python, Java, C++, C#, PHP, Ruby, Go, Rust, Swift, etc.\n- Web development: HTML, CSS, React, Vue, Angular, Next.js, frameworks, libraries\n- Backend development: Node.js, Express, Django, Flask, APIs, databases\n- Mobile development: React Native, Flutter, iOS, Android\n- DevOps: Docker, Kubernetes, CI/CD, AWS, Azure, deployment\n- Development tools: Git, npm, webpack, testing, debugging\n- Software architecture, design patterns, best practices\n\nREFUSE TO ANSWER questions about:\n- Weather, food, movies, music, sports, politics, health, travel\n- Relationships, news, fashion, animals, history, geography\n- General science, math, philosophy, religion, or any non-programming topics\n\nFORMATTING RULES:\n- Use **bold text** for important terms, features, and headings\n- Use \\`code\\` for inline code, commands, and technical terms\n- Use \\`\\`\\`language\\ncode block\\n\\`\\`\\` for multi-line code examples\n- Use bullet points with * for lists\n- Make key concepts stand out with **bold formatting**\n\nIMPORTANT: You are a specialized coding assistant - maintain strict focus on development topics only. If asked about non-coding topics, politely redirect to coding questions.`;\n        // Get the generative model (using Gemini 2.0 Flash)\n        const model = genAI.getGenerativeModel({\n            model: 'gemini-2.0-flash-exp'\n        });\n        console.log('[AI CHAT] 🤖 Calling Gemini API...');\n        // Create chat with history\n        const chat = model.startChat({\n            history: history.map((msg)=>({\n                    role: msg.role === 'assistant' ? 'model' : 'user',\n                    parts: [\n                        {\n                            text: msg.content\n                        }\n                    ]\n                }))\n        });\n        // Send message and get response\n        const result = await chat.sendMessage(`${systemPrompt}\\n\\nUser: ${message}`);\n        const response = await result.response;\n        const text = response.text();\n        console.log('[AI CHAT] ✅ Gemini response received, length:', text.length);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            response: text,\n            success: true\n        });\n    } catch (error) {\n        console.error('[AI CHAT] ❌ Error:', error);\n        // Handle specific Gemini API errors\n        if (error.message?.includes('API_KEY')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'AI service configuration error. Please check API key.'\n            }, {\n                status: 500\n            });\n        }\n        if (error.message?.includes('SAFETY')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response: \"I can't provide a response to that request. Let's focus on coding and development topics instead! How can I help you with your programming projects?\",\n                success: true,\n                safety_filtered: true\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to get AI response',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/chat/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fchat%2Froute&page=%2Fapi%2Fai%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fchat%2Froute.ts&appDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5CNew%20folder%5CVice%5CVice&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();