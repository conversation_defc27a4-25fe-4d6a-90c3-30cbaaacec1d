"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/stack/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/create-project-modal.tsx":
/*!*********************************************************!*\
  !*** ./src/components/project/create-project-modal.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateProjectModal: () => (/* binding */ CreateProjectModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Terminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* __next_internal_client_entry_do_not_use__ CreateProjectModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CreateProjectModal(param) {\n    let { open, onOpenChange, stackName, projectTitle = '' } = param;\n    var _startingCommands_stackName;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [projectName, setProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(projectTitle);\n    const [projectPath, setProjectPath] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\ViceProjects\\\\\".concat(stackName.toLowerCase()));\n    const [projectDescription, setProjectDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCommands, setShowCommands] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLoading, setShowLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentCommand, setCurrentCommand] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [commandProgress, setCommandProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCommandRunning, setIsCommandRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Update path when stackName changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateProjectModal.useEffect\": ()=>{\n            setProjectPath(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\ViceProjects\\\\\".concat(stackName.toLowerCase()));\n        }\n    }[\"CreateProjectModal.useEffect\"], [\n        stackName\n    ]);\n    // Validate and format project name (no spaces, no caps, use hyphens)\n    const validateProjectName = (name)=>{\n        return name.trim().toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n    };\n    // Handle project name input with real-time validation\n    const handleProjectNameChange = (e)=>{\n        const value = e.target.value;\n        setProjectName(value);\n    };\n    // Listen for command progress events from Electron\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateProjectModal.useEffect\": ()=>{\n            if ( true && window.electronAPI) {\n                const handleCommandProgress = {\n                    \"CreateProjectModal.useEffect.handleCommandProgress\": (data)=>{\n                        console.log('[COMMAND PROGRESS] 📨 Received:', data);\n                        if (data.status === 'running') {\n                            setCurrentCommand(data.command);\n                            setIsCommandRunning(true);\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"▶️ Running: \".concat(data.command)\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                        } else if (data.status === 'completed') {\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"✅ Completed: \".concat(data.command)\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            setIsCommandRunning(false);\n                        } else if (data.status === 'error') {\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"❌ Error: \".concat(data.command, \" - \").concat(data.error || 'Unknown error')\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            setIsCommandRunning(false);\n                        } else if (data.status === 'starting-ide') {\n                            setCurrentCommand(\"Starting IDE for \".concat(data.projectName || 'project', \"...\"));\n                            setIsCommandRunning(true);\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"\\uD83D\\uDE80 Starting IDE and returning to dashboard...\"\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            // Close loading screen and return to dashboard\n                            setTimeout({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": ()=>{\n                                    handleLoadingComplete();\n                                }\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"], 3000);\n                        } else if (data.status === 'ide-opened') {\n                            setCommandProgress({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": (prev)=>[\n                                        ...prev,\n                                        \"✅ IDE opened successfully! Returning to dashboard...\"\n                                    ]\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"]);\n                            setTimeout({\n                                \"CreateProjectModal.useEffect.handleCommandProgress\": ()=>{\n                                    handleLoadingComplete();\n                                }\n                            }[\"CreateProjectModal.useEffect.handleCommandProgress\"], 1000);\n                        }\n                    }\n                }[\"CreateProjectModal.useEffect.handleCommandProgress\"];\n                // Listen for the IPC event\n                if (window.electronAPI.onCommandProgress) {\n                    window.electronAPI.onCommandProgress(handleCommandProgress);\n                }\n                return ({\n                    \"CreateProjectModal.useEffect\": ()=>{\n                        console.log('[PROJECT] Cleaning up command progress listener');\n                    }\n                })[\"CreateProjectModal.useEffect\"];\n            }\n        }\n    }[\"CreateProjectModal.useEffect\"], []);\n    const handleLoadingComplete = ()=>{\n        setShowLoading(false);\n        setIsCreating(false);\n        setCurrentCommand('');\n        setCommandProgress([]);\n        setIsCommandRunning(false);\n        toast({\n            title: 'Project Ready',\n            description: \"\".concat(projectName, \" has been created and opened in the IDE.\")\n        });\n    };\n    // Starting commands for different stacks\n    const startingCommands = {\n        'Mern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express mongoose cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/models/User.js server/routes/api.js'\n        ],\n        'Pern': [\n            'npx create-react-app my-app',\n            'cd my-app',\n            'npm install express pg cors dotenv',\n            'mkdir server',\n            'touch server/index.js server/db.js server/routes/api.js'\n        ],\n        'NextJS': [\n            'npx create-next-app@latest my-app',\n            'cd my-app',\n            'npm install'\n        ],\n        'Django': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install django djangorestframework',\n            'django-admin startproject myproject .',\n            'python manage.py startapp myapp'\n        ],\n        'Flask': [\n            'python -m venv venv',\n            'venv\\\\Scripts\\\\activate',\n            'pip install flask flask-sqlalchemy flask-cors',\n            'mkdir app',\n            'touch app/__init__.py app/routes.py app/models.py'\n        ],\n        'Bash': [\n            'mkdir my-script',\n            'cd my-script',\n            'touch script.sh',\n            'chmod +x script.sh',\n            'echo \"#!/bin/bash\" > script.sh'\n        ]\n    };\n    const handleCreate = async ()=>{\n        if (!projectName.trim()) {\n            toast({\n                title: 'Project name required',\n                description: 'Please enter a name for your project.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        setIsCreating(true);\n        try {\n            // In a real app, this would create the project\n            // For now, we'll just simulate the creation process\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Show success message\n            toast({\n                title: 'Project created',\n                description: \"\".concat(projectName, \" has been created successfully.\")\n            });\n            // Close the modal\n            onOpenChange(false);\n        } catch (error) {\n            toast({\n                title: 'Failed to create project',\n                description: 'An error occurred while creating the project.',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"sm:max-w-[550px] md:max-w-[600px] rounded-xl overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-20 w-40\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/logo.png\",\n                            alt: \"VICE Logo\",\n                            fill: true,\n                            style: {\n                                objectFit: 'contain'\n                            },\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-[-100%]' : 'translate-x-0'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                    className: \"mb-[5] pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                            className: \"text-center\",\n                                            children: \"Create New Project\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                            className: \"text-center text-sm text-muted-foreground\",\n                                            children: [\n                                                \"Create a new \",\n                                                stackName,\n                                                \" project. Fill in the details below to get started.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4 py-4 px-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"name\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"name\",\n                                                    value: projectName,\n                                                    onChange: (e)=>setProjectName(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"My Awesome Project\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"path\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Path\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"path\",\n                                                    value: projectPath,\n                                                    onChange: (e)=>setProjectPath(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"Documents/ViceProjects/\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"description\",\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                    id: \"description\",\n                                                    value: projectDescription,\n                                                    onChange: (e)=>setProjectDescription(e.target.value),\n                                                    className: \"w-full\",\n                                                    placeholder: \"A brief description of your project\",\n                                                    rows: 3\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-2 mb-4 ml-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"text-xs text-muted-foreground flex items-center gap-1 p-0 h-auto\",\n                                                onClick: ()=>setShowCommands(true),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3.5 w-3.5 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"See Starting Commands\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-3.5 w-3.5 ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                            className: \"flex justify-end items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>onOpenChange(false),\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        onClick: handleCreate,\n                                                        disabled: isCreating,\n                                                        children: isCreating ? 'Creating...' : 'Create Project'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-in-out \".concat(showCommands ? 'translate-x-0' : 'translate-x-[100%]'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 ml-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"pl-0 flex items-center gap-1\",\n                                        onClick: ()=>setShowCommands(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Project Form\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                            children: [\n                                                \"Starting Commands for \",\n                                                stackName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                                            children: [\n                                                \"Use these commands to set up your \",\n                                                stackName,\n                                                \" project manually.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-4 px-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-muted p-4 rounded-md font-mono text-xs sm:text-sm overflow-auto max-h-[250px]\",\n                                        children: ((_startingCommands_stackName = startingCommands[stackName]) === null || _startingCommands_stackName === void 0 ? void 0 : _startingCommands_stackName.map((command, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground mr-2 flex-shrink-0\",\n                                                        children: \"$\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"break-all\",\n                                                        children: command\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                \"No commands available for \",\n                                                stackName,\n                                                \".\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>onOpenChange(false),\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n            lineNumber: 203,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\New folder\\\\Vice\\\\Vice\\\\src\\\\components\\\\project\\\\create-project-modal.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateProjectModal, \"+Wy90OJPGbFq7Wx8RMMP1TbpegU=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = CreateProjectModal;\nvar _c;\n$RefreshReg$(_c, \"CreateProjectModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3Byb2plY3QvY3JlYXRlLXByb2plY3QtbW9kYWwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDcEI7QUFRQztBQUNnQjtBQUNGO0FBQ0E7QUFDTTtBQUNQO0FBQ29CO0FBVTFELFNBQVNrQixtQkFBbUIsS0FLVDtRQUxTLEVBQ2pDQyxJQUFJLEVBQ0pDLFlBQVksRUFDWkMsU0FBUyxFQUNUQyxlQUFlLEVBQUUsRUFDTyxHQUxTO1FBeVNsQkM7O0lBblNmLE1BQU0sRUFBRUMsS0FBSyxFQUFFLEdBQUdWLDBEQUFRQTtJQUMxQixNQUFNLENBQUNXLGFBQWFDLGVBQWUsR0FBR3pCLCtDQUFRQSxDQUFDcUI7SUFDL0MsTUFBTSxDQUFDSyxhQUFhQyxlQUFlLEdBQUczQiwrQ0FBUUEsQ0FBQyw2Q0FBcUUsT0FBeEJvQixVQUFVUSxXQUFXO0lBQ2pILE1BQU0sQ0FBQ0Msb0JBQW9CQyxzQkFBc0IsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQytCLFlBQVlDLGNBQWMsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2lDLGNBQWNDLGdCQUFnQixHQUFHbEMsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDbUMsYUFBYUMsZUFBZSxHQUFHcEMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDcUMsZ0JBQWdCQyxrQkFBa0IsR0FBR3RDLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ3VDLGlCQUFpQkMsbUJBQW1CLEdBQUd4QywrQ0FBUUEsQ0FBVyxFQUFFO0lBQ25FLE1BQU0sQ0FBQ3lDLGtCQUFrQkMsb0JBQW9CLEdBQUcxQywrQ0FBUUEsQ0FBQztJQUV6RCxxQ0FBcUM7SUFDckNDLGdEQUFTQTt3Q0FBQztZQUNSMEIsZUFBZSw2Q0FBcUUsT0FBeEJQLFVBQVVRLFdBQVc7UUFDbkY7dUNBQUc7UUFBQ1I7S0FBVTtJQUVkLHFFQUFxRTtJQUNyRSxNQUFNdUIsc0JBQXNCLENBQUNDO1FBQzNCLE9BQU9BLEtBQUtDLElBQUksR0FBR2pCLFdBQVcsR0FBR2tCLE9BQU8sQ0FBQyxRQUFRLEtBQUtBLE9BQU8sQ0FBQyxlQUFlO0lBQy9FO0lBRUEsc0RBQXNEO0lBQ3RELE1BQU1DLDBCQUEwQixDQUFDQztRQUMvQixNQUFNQyxRQUFRRCxFQUFFRSxNQUFNLENBQUNELEtBQUs7UUFDNUJ4QixlQUFld0I7SUFDakI7SUFFQSxtREFBbUQ7SUFDbkRoRCxnREFBU0E7d0NBQUM7WUFDUixJQUFJLEtBQTZCLElBQUlrRCxPQUFPQyxXQUFXLEVBQUU7Z0JBQ3ZELE1BQU1DOzBFQUF3QixDQUFDQzt3QkFDN0JDLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUNGO3dCQUUvQyxJQUFJQSxLQUFLRyxNQUFNLEtBQUssV0FBVzs0QkFDN0JuQixrQkFBa0JnQixLQUFLSSxPQUFPOzRCQUM5QmhCLG9CQUFvQjs0QkFDcEJGO3NGQUFtQm1CLENBQUFBLE9BQVE7MkNBQUlBO3dDQUFPLGVBQTJCLE9BQWJMLEtBQUtJLE9BQU87cUNBQUc7O3dCQUNyRSxPQUFPLElBQUlKLEtBQUtHLE1BQU0sS0FBSyxhQUFhOzRCQUN0Q2pCO3NGQUFtQm1CLENBQUFBLE9BQVE7MkNBQ3RCQTt3Q0FDRixnQkFBNEIsT0FBYkwsS0FBS0ksT0FBTztxQ0FDN0I7OzRCQUNEaEIsb0JBQW9CO3dCQUN0QixPQUFPLElBQUlZLEtBQUtHLE1BQU0sS0FBSyxTQUFTOzRCQUNsQ2pCO3NGQUFtQm1CLENBQUFBLE9BQVE7MkNBQUlBO3dDQUFPLFlBQTZCTCxPQUFsQkEsS0FBS0ksT0FBTyxFQUFDLE9BQW1DLE9BQTlCSixLQUFLTSxLQUFLLElBQUk7cUNBQWtCOzs0QkFDbkdsQixvQkFBb0I7d0JBQ3RCLE9BQU8sSUFBSVksS0FBS0csTUFBTSxLQUFLLGdCQUFnQjs0QkFDekNuQixrQkFBa0Isb0JBQWtELE9BQTlCZ0IsS0FBSzlCLFdBQVcsSUFBSSxXQUFVOzRCQUNwRWtCLG9CQUFvQjs0QkFDcEJGO3NGQUFtQm1CLENBQUFBLE9BQVE7MkNBQUlBO3dDQUFPO3FDQUErQzs7NEJBRXJGLCtDQUErQzs0QkFDL0NFO3NGQUFXO29DQUNUQztnQ0FDRjtxRkFBRzt3QkFDTCxPQUFPLElBQUlSLEtBQUtHLE1BQU0sS0FBSyxjQUFjOzRCQUN2Q2pCO3NGQUFtQm1CLENBQUFBLE9BQVE7MkNBQUlBO3dDQUFPO3FDQUFzRDs7NEJBQzVGRTtzRkFBVztvQ0FDVEM7Z0NBQ0Y7cUZBQUc7d0JBQ0w7b0JBQ0Y7O2dCQUVBLDJCQUEyQjtnQkFDM0IsSUFBSVgsT0FBT0MsV0FBVyxDQUFDVyxpQkFBaUIsRUFBRTtvQkFDeENaLE9BQU9DLFdBQVcsQ0FBQ1csaUJBQWlCLENBQUNWO2dCQUN2QztnQkFFQTtvREFBTzt3QkFDTEUsUUFBUUMsR0FBRyxDQUFDO29CQUNkOztZQUNGO1FBQ0Y7dUNBQUcsRUFBRTtJQUVMLE1BQU1NLHdCQUF3QjtRQUM1QjFCLGVBQWU7UUFDZkosY0FBYztRQUNkTSxrQkFBa0I7UUFDbEJFLG1CQUFtQixFQUFFO1FBQ3JCRSxvQkFBb0I7UUFFcEJuQixNQUFNO1lBQ0p5QyxPQUFPO1lBQ1BDLGFBQWEsR0FBZSxPQUFaekMsYUFBWTtRQUM5QjtJQUNGO0lBRUEseUNBQXlDO0lBQ3pDLE1BQU1GLG1CQUFtQjtRQUN2QixRQUFRO1lBQ047WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0QsUUFBUTtZQUNOO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUNELFVBQVU7WUFDUjtZQUNBO1lBQ0E7U0FDRDtRQUNELFVBQVU7WUFDUjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDRCxTQUFTO1lBQ1A7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0QsUUFBUTtZQUNOO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7U0FDRDtJQUNIO0lBRUEsTUFBTTRDLGVBQWU7UUFDbkIsSUFBSSxDQUFDMUMsWUFBWXFCLElBQUksSUFBSTtZQUN2QnRCLE1BQU07Z0JBQ0p5QyxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiRSxTQUFTO1lBQ1g7WUFDQTtRQUNGO1FBRUFuQyxjQUFjO1FBRWQsSUFBSTtZQUNGLCtDQUErQztZQUMvQyxvREFBb0Q7WUFDcEQsTUFBTSxJQUFJb0MsUUFBUUMsQ0FBQUEsVUFBV1IsV0FBV1EsU0FBUztZQUVqRCx1QkFBdUI7WUFDdkI5QyxNQUFNO2dCQUNKeUMsT0FBTztnQkFDUEMsYUFBYSxHQUFlLE9BQVp6QyxhQUFZO1lBQzlCO1lBRUEsa0JBQWtCO1lBQ2xCTCxhQUFhO1FBQ2YsRUFBRSxPQUFPeUMsT0FBTztZQUNkckMsTUFBTTtnQkFDSnlDLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JFLFNBQVM7WUFDWDtRQUNGLFNBQVU7WUFDUm5DLGNBQWM7UUFDaEI7SUFDRjtJQUVBLHFCQUNFLDhEQUFDN0IseURBQU1BO1FBQUNlLE1BQU1BO1FBQU1DLGNBQWNBO2tCQUNoQyw0RUFBQ2YsZ0VBQWFBO1lBQUNrRSxXQUFVOzs4QkFFdkIsOERBQUNDO29CQUFJRCxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTtrQ0FDYiw0RUFBQ3BFLGtEQUFLQTs0QkFDSnNFLEtBQUk7NEJBQ0pDLEtBQUk7NEJBQ0pDLElBQUk7NEJBQ0pDLE9BQU87Z0NBQUVDLFdBQVc7NEJBQVU7NEJBQzlCQyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7OzhCQU1kLDhEQUFDTjtvQkFBSUQsV0FBVTs7c0NBRWIsOERBQUNDOzRCQUNDRCxXQUFXLGlEQUVWLE9BRENyQyxlQUFlLHdCQUF3Qjs7OENBR3pDLDhEQUFDMUIsK0RBQVlBO29DQUFDK0QsV0FBVTs7c0RBQ3RCLDhEQUFDOUQsOERBQVdBOzRDQUFDOEQsV0FBVTtzREFBYzs7Ozs7O3NEQUNyQyw4REFBQ2pFLG9FQUFpQkE7NENBQUNpRSxXQUFVOztnREFBNEM7Z0RBQ3pEbEQ7Z0RBQVU7Ozs7Ozs7Ozs7Ozs7OENBRzVCLDhEQUFDbUQ7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUMzRCx1REFBS0E7b0RBQUNtRSxTQUFRO29EQUFPUixXQUFVOzhEQUFzQjs7Ozs7OzhEQUd0RCw4REFBQzVELHVEQUFLQTtvREFDSnFFLElBQUc7b0RBQ0g5QixPQUFPekI7b0RBQ1B3RCxVQUFVLENBQUNoQyxJQUFNdkIsZUFBZXVCLEVBQUVFLE1BQU0sQ0FBQ0QsS0FBSztvREFDOUNxQixXQUFVO29EQUNWVyxhQUFZO29EQUNaQyxTQUFTOzs7Ozs7Ozs7Ozs7c0RBR2IsOERBQUNYOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQzNELHVEQUFLQTtvREFBQ21FLFNBQVE7b0RBQU9SLFdBQVU7OERBQXNCOzs7Ozs7OERBR3RELDhEQUFDNUQsdURBQUtBO29EQUNKcUUsSUFBRztvREFDSDlCLE9BQU92QjtvREFDUHNELFVBQVUsQ0FBQ2hDLElBQU1yQixlQUFlcUIsRUFBRUUsTUFBTSxDQUFDRCxLQUFLO29EQUM5Q3FCLFdBQVU7b0RBQ1ZXLGFBQVk7Ozs7Ozs7Ozs7OztzREFHaEIsOERBQUNWOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQzNELHVEQUFLQTtvREFBQ21FLFNBQVE7b0RBQWNSLFdBQVU7OERBQXNCOzs7Ozs7OERBRzdELDhEQUFDMUQsNkRBQVFBO29EQUNQbUUsSUFBRztvREFDSDlCLE9BQU9wQjtvREFDUG1ELFVBQVUsQ0FBQ2hDLElBQU1sQixzQkFBc0JrQixFQUFFRSxNQUFNLENBQUNELEtBQUs7b0RBQ3JEcUIsV0FBVTtvREFDVlcsYUFBWTtvREFDWkUsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUlaLDhEQUFDWjtvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFVO3NEQUNiLDRFQUFDN0QseURBQU1BO2dEQUNMMEQsU0FBUTtnREFDUmlCLE1BQUs7Z0RBQ0xkLFdBQVU7Z0RBQ1ZlLFNBQVMsSUFBTW5ELGdCQUFnQjs7a0VBRS9CLDhEQUFDbEIsMkdBQVFBO3dEQUFDc0QsV0FBVTs7Ozs7O29EQUFxQjtrRUFFekMsOERBQUN2RCw0R0FBWUE7d0RBQUN1RCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzREFJNUIsOERBQUNoRSwrREFBWUE7NENBQUNnRSxXQUFVO3NEQUN0Qiw0RUFBQ0M7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDN0QseURBQU1BO3dEQUFDMEQsU0FBUTt3REFBVWtCLFNBQVMsSUFBTWxFLGFBQWE7a0VBQVE7Ozs7OztrRUFHOUQsOERBQUNWLHlEQUFNQTt3REFBQzRFLFNBQVNuQjt3REFBY29CLFVBQVV2RDtrRUFDdENBLGFBQWEsZ0JBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FReEMsOERBQUN3Qzs0QkFDQ0QsV0FBVyxxRkFFVixPQURDckMsZUFBZSxrQkFBa0I7OzhDQUduQyw4REFBQ3NDO29DQUFJRCxXQUFVOzhDQUNiLDRFQUFDN0QseURBQU1BO3dDQUNMMEQsU0FBUTt3Q0FDUmlCLE1BQUs7d0NBQ0xkLFdBQVU7d0NBQ1ZlLFNBQVMsSUFBTW5ELGdCQUFnQjs7MERBRS9CLDhEQUFDcEIsNEdBQVNBO2dEQUFDd0QsV0FBVTs7Ozs7OzRDQUFZOzs7Ozs7Ozs7Ozs7OENBS3JDLDhEQUFDL0QsK0RBQVlBOztzREFDWCw4REFBQ0MsOERBQVdBOztnREFBQztnREFBdUJZOzs7Ozs7O3NEQUNwQyw4REFBQ2Ysb0VBQWlCQTs7Z0RBQUM7Z0RBQ2tCZTtnREFBVTs7Ozs7Ozs7Ozs7Ozs4Q0FJakQsOERBQUNtRDtvQ0FBSUQsV0FBVTs4Q0FDYiw0RUFBQ0M7d0NBQUlELFdBQVU7a0RBQ1poRCxFQUFBQSw4QkFBQUEsZ0JBQWdCLENBQUNGLFVBQTJDLGNBQTVERSxrREFBQUEsNEJBQThEaUUsR0FBRyxDQUFDLENBQUM3QixTQUFTOEIsc0JBQzNFLDhEQUFDakI7Z0RBQWdCRCxXQUFVOztrRUFDekIsOERBQUNtQjt3REFBS25CLFdBQVU7a0VBQTJDOzs7Ozs7a0VBQzNELDhEQUFDbUI7d0RBQUtuQixXQUFVO2tFQUFhWjs7Ozs7OzsrQ0FGckI4Qjs7Ozt3RUFLViw4REFBQ2pCOzRDQUFJRCxXQUFVOztnREFBd0I7Z0RBQTJCbEQ7Z0RBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUtsRiw4REFBQ2QsK0RBQVlBOzhDQUNYLDRFQUFDRyx5REFBTUE7d0NBQUMwRCxTQUFRO3dDQUFVa0IsU0FBUyxJQUFNbEUsYUFBYTtrREFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVM1RTtHQTlUZ0JGOztRQU1JSixzREFBUUE7OztLQU5aSSIsInNvdXJjZXMiOlsiRDpcXHByb2plY3RzXFxOZXcgZm9sZGVyXFxWaWNlXFxWaWNlXFxzcmNcXGNvbXBvbmVudHNcXHByb2plY3RcXGNyZWF0ZS1wcm9qZWN0LW1vZGFsLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnO1xuaW1wb3J0IHtcbiAgRGlhbG9nLFxuICBEaWFsb2dDb250ZW50LFxuICBEaWFsb2dEZXNjcmlwdGlvbixcbiAgRGlhbG9nRm9vdGVyLFxuICBEaWFsb2dIZWFkZXIsXG4gIERpYWxvZ1RpdGxlLFxufSBmcm9tICdAL2NvbXBvbmVudHMvdWkvZGlhbG9nJztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nO1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvaW5wdXQnO1xuaW1wb3J0IHsgTGFiZWwgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvbGFiZWwnO1xuaW1wb3J0IHsgVGV4dGFyZWEgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdGV4dGFyZWEnO1xuaW1wb3J0IHsgdXNlVG9hc3QgfSBmcm9tICdAL2hvb2tzL3VzZS10b2FzdCc7XG5pbXBvcnQgeyBBcnJvd0xlZnQsIENoZXZyb25SaWdodCwgVGVybWluYWwgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IFByb2plY3RDcmVhdGlvbkxvYWRpbmcgZnJvbSAnLi9wcm9qZWN0LWNyZWF0aW9uLWxvYWRpbmcnO1xuXG5pbnRlcmZhY2UgQ3JlYXRlUHJvamVjdE1vZGFsUHJvcHMge1xuICBvcGVuOiBib29sZWFuO1xuICBvbk9wZW5DaGFuZ2U6IChvcGVuOiBib29sZWFuKSA9PiB2b2lkO1xuICBzdGFja05hbWU6IHN0cmluZztcbiAgcHJvamVjdFRpdGxlPzogc3RyaW5nO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gQ3JlYXRlUHJvamVjdE1vZGFsKHtcbiAgb3BlbixcbiAgb25PcGVuQ2hhbmdlLFxuICBzdGFja05hbWUsXG4gIHByb2plY3RUaXRsZSA9ICcnLFxufTogQ3JlYXRlUHJvamVjdE1vZGFsUHJvcHMpIHtcbiAgY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKTtcbiAgY29uc3QgW3Byb2plY3ROYW1lLCBzZXRQcm9qZWN0TmFtZV0gPSB1c2VTdGF0ZShwcm9qZWN0VGl0bGUpO1xuICBjb25zdCBbcHJvamVjdFBhdGgsIHNldFByb2plY3RQYXRoXSA9IHVzZVN0YXRlKGBDOlxcXFxVc2Vyc1xcXFxVc2VyXFxcXERvY3VtZW50c1xcXFxWaWNlUHJvamVjdHNcXFxcJHtzdGFja05hbWUudG9Mb3dlckNhc2UoKX1gKTtcbiAgY29uc3QgW3Byb2plY3REZXNjcmlwdGlvbiwgc2V0UHJvamVjdERlc2NyaXB0aW9uXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2lzQ3JlYXRpbmcsIHNldElzQ3JlYXRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd0NvbW1hbmRzLCBzZXRTaG93Q29tbWFuZHNdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd0xvYWRpbmcsIHNldFNob3dMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2N1cnJlbnRDb21tYW5kLCBzZXRDdXJyZW50Q29tbWFuZF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtjb21tYW5kUHJvZ3Jlc3MsIHNldENvbW1hbmRQcm9ncmVzc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oW10pO1xuICBjb25zdCBbaXNDb21tYW5kUnVubmluZywgc2V0SXNDb21tYW5kUnVubmluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gVXBkYXRlIHBhdGggd2hlbiBzdGFja05hbWUgY2hhbmdlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldFByb2plY3RQYXRoKGBDOlxcXFxVc2Vyc1xcXFxVc2VyXFxcXERvY3VtZW50c1xcXFxWaWNlUHJvamVjdHNcXFxcJHtzdGFja05hbWUudG9Mb3dlckNhc2UoKX1gKTtcbiAgfSwgW3N0YWNrTmFtZV0pO1xuXG4gIC8vIFZhbGlkYXRlIGFuZCBmb3JtYXQgcHJvamVjdCBuYW1lIChubyBzcGFjZXMsIG5vIGNhcHMsIHVzZSBoeXBoZW5zKVxuICBjb25zdCB2YWxpZGF0ZVByb2plY3ROYW1lID0gKG5hbWU6IHN0cmluZykgPT4ge1xuICAgIHJldHVybiBuYW1lLnRyaW0oKS50b0xvd2VyQ2FzZSgpLnJlcGxhY2UoL1xccysvZywgJy0nKS5yZXBsYWNlKC9bXmEtejAtOS1dL2csICcnKTtcbiAgfTtcblxuICAvLyBIYW5kbGUgcHJvamVjdCBuYW1lIGlucHV0IHdpdGggcmVhbC10aW1lIHZhbGlkYXRpb25cbiAgY29uc3QgaGFuZGxlUHJvamVjdE5hbWVDaGFuZ2UgPSAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCB2YWx1ZSA9IGUudGFyZ2V0LnZhbHVlO1xuICAgIHNldFByb2plY3ROYW1lKHZhbHVlKTtcbiAgfTtcblxuICAvLyBMaXN0ZW4gZm9yIGNvbW1hbmQgcHJvZ3Jlc3MgZXZlbnRzIGZyb20gRWxlY3Ryb25cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LmVsZWN0cm9uQVBJKSB7XG4gICAgICBjb25zdCBoYW5kbGVDb21tYW5kUHJvZ3Jlc3MgPSAoZGF0YTogYW55KSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdbQ09NTUFORCBQUk9HUkVTU10g8J+TqCBSZWNlaXZlZDonLCBkYXRhKTtcblxuICAgICAgICBpZiAoZGF0YS5zdGF0dXMgPT09ICdydW5uaW5nJykge1xuICAgICAgICAgIHNldEN1cnJlbnRDb21tYW5kKGRhdGEuY29tbWFuZCk7XG4gICAgICAgICAgc2V0SXNDb21tYW5kUnVubmluZyh0cnVlKTtcbiAgICAgICAgICBzZXRDb21tYW5kUHJvZ3Jlc3MocHJldiA9PiBbLi4ucHJldiwgYOKWtu+4jyBSdW5uaW5nOiAke2RhdGEuY29tbWFuZH1gXSk7XG4gICAgICAgIH0gZWxzZSBpZiAoZGF0YS5zdGF0dXMgPT09ICdjb21wbGV0ZWQnKSB7XG4gICAgICAgICAgc2V0Q29tbWFuZFByb2dyZXNzKHByZXYgPT4gW1xuICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgIGDinIUgQ29tcGxldGVkOiAke2RhdGEuY29tbWFuZH1gXG4gICAgICAgICAgXSk7XG4gICAgICAgICAgc2V0SXNDb21tYW5kUnVubmluZyhmYWxzZSk7XG4gICAgICAgIH0gZWxzZSBpZiAoZGF0YS5zdGF0dXMgPT09ICdlcnJvcicpIHtcbiAgICAgICAgICBzZXRDb21tYW5kUHJvZ3Jlc3MocHJldiA9PiBbLi4ucHJldiwgYOKdjCBFcnJvcjogJHtkYXRhLmNvbW1hbmR9IC0gJHtkYXRhLmVycm9yIHx8ICdVbmtub3duIGVycm9yJ31gXSk7XG4gICAgICAgICAgc2V0SXNDb21tYW5kUnVubmluZyhmYWxzZSk7XG4gICAgICAgIH0gZWxzZSBpZiAoZGF0YS5zdGF0dXMgPT09ICdzdGFydGluZy1pZGUnKSB7XG4gICAgICAgICAgc2V0Q3VycmVudENvbW1hbmQoYFN0YXJ0aW5nIElERSBmb3IgJHtkYXRhLnByb2plY3ROYW1lIHx8ICdwcm9qZWN0J30uLi5gKTtcbiAgICAgICAgICBzZXRJc0NvbW1hbmRSdW5uaW5nKHRydWUpO1xuICAgICAgICAgIHNldENvbW1hbmRQcm9ncmVzcyhwcmV2ID0+IFsuLi5wcmV2LCBg8J+agCBTdGFydGluZyBJREUgYW5kIHJldHVybmluZyB0byBkYXNoYm9hcmQuLi5gXSk7XG5cbiAgICAgICAgICAvLyBDbG9zZSBsb2FkaW5nIHNjcmVlbiBhbmQgcmV0dXJuIHRvIGRhc2hib2FyZFxuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgaGFuZGxlTG9hZGluZ0NvbXBsZXRlKCk7XG4gICAgICAgICAgfSwgMzAwMCk7XG4gICAgICAgIH0gZWxzZSBpZiAoZGF0YS5zdGF0dXMgPT09ICdpZGUtb3BlbmVkJykge1xuICAgICAgICAgIHNldENvbW1hbmRQcm9ncmVzcyhwcmV2ID0+IFsuLi5wcmV2LCBg4pyFIElERSBvcGVuZWQgc3VjY2Vzc2Z1bGx5ISBSZXR1cm5pbmcgdG8gZGFzaGJvYXJkLi4uYF0pO1xuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgaGFuZGxlTG9hZGluZ0NvbXBsZXRlKCk7XG4gICAgICAgICAgfSwgMTAwMCk7XG4gICAgICAgIH1cbiAgICAgIH07XG5cbiAgICAgIC8vIExpc3RlbiBmb3IgdGhlIElQQyBldmVudFxuICAgICAgaWYgKHdpbmRvdy5lbGVjdHJvbkFQSS5vbkNvbW1hbmRQcm9ncmVzcykge1xuICAgICAgICB3aW5kb3cuZWxlY3Ryb25BUEkub25Db21tYW5kUHJvZ3Jlc3MoaGFuZGxlQ29tbWFuZFByb2dyZXNzKTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coJ1tQUk9KRUNUXSBDbGVhbmluZyB1cCBjb21tYW5kIHByb2dyZXNzIGxpc3RlbmVyJyk7XG4gICAgICB9O1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIGNvbnN0IGhhbmRsZUxvYWRpbmdDb21wbGV0ZSA9ICgpID0+IHtcbiAgICBzZXRTaG93TG9hZGluZyhmYWxzZSk7XG4gICAgc2V0SXNDcmVhdGluZyhmYWxzZSk7XG4gICAgc2V0Q3VycmVudENvbW1hbmQoJycpO1xuICAgIHNldENvbW1hbmRQcm9ncmVzcyhbXSk7XG4gICAgc2V0SXNDb21tYW5kUnVubmluZyhmYWxzZSk7XG5cbiAgICB0b2FzdCh7XG4gICAgICB0aXRsZTogJ1Byb2plY3QgUmVhZHknLFxuICAgICAgZGVzY3JpcHRpb246IGAke3Byb2plY3ROYW1lfSBoYXMgYmVlbiBjcmVhdGVkIGFuZCBvcGVuZWQgaW4gdGhlIElERS5gLFxuICAgIH0pO1xuICB9O1xuXG4gIC8vIFN0YXJ0aW5nIGNvbW1hbmRzIGZvciBkaWZmZXJlbnQgc3RhY2tzXG4gIGNvbnN0IHN0YXJ0aW5nQ29tbWFuZHMgPSB7XG4gICAgJ01lcm4nOiBbXG4gICAgICAnbnB4IGNyZWF0ZS1yZWFjdC1hcHAgbXktYXBwJyxcbiAgICAgICdjZCBteS1hcHAnLFxuICAgICAgJ25wbSBpbnN0YWxsIGV4cHJlc3MgbW9uZ29vc2UgY29ycyBkb3RlbnYnLFxuICAgICAgJ21rZGlyIHNlcnZlcicsXG4gICAgICAndG91Y2ggc2VydmVyL2luZGV4LmpzIHNlcnZlci9tb2RlbHMvVXNlci5qcyBzZXJ2ZXIvcm91dGVzL2FwaS5qcydcbiAgICBdLFxuICAgICdQZXJuJzogW1xuICAgICAgJ25weCBjcmVhdGUtcmVhY3QtYXBwIG15LWFwcCcsXG4gICAgICAnY2QgbXktYXBwJyxcbiAgICAgICducG0gaW5zdGFsbCBleHByZXNzIHBnIGNvcnMgZG90ZW52JyxcbiAgICAgICdta2RpciBzZXJ2ZXInLFxuICAgICAgJ3RvdWNoIHNlcnZlci9pbmRleC5qcyBzZXJ2ZXIvZGIuanMgc2VydmVyL3JvdXRlcy9hcGkuanMnXG4gICAgXSxcbiAgICAnTmV4dEpTJzogW1xuICAgICAgJ25weCBjcmVhdGUtbmV4dC1hcHBAbGF0ZXN0IG15LWFwcCcsXG4gICAgICAnY2QgbXktYXBwJyxcbiAgICAgICducG0gaW5zdGFsbCdcbiAgICBdLFxuICAgICdEamFuZ28nOiBbXG4gICAgICAncHl0aG9uIC1tIHZlbnYgdmVudicsXG4gICAgICAndmVudlxcXFxTY3JpcHRzXFxcXGFjdGl2YXRlJyxcbiAgICAgICdwaXAgaW5zdGFsbCBkamFuZ28gZGphbmdvcmVzdGZyYW1ld29yaycsXG4gICAgICAnZGphbmdvLWFkbWluIHN0YXJ0cHJvamVjdCBteXByb2plY3QgLicsXG4gICAgICAncHl0aG9uIG1hbmFnZS5weSBzdGFydGFwcCBteWFwcCdcbiAgICBdLFxuICAgICdGbGFzayc6IFtcbiAgICAgICdweXRob24gLW0gdmVudiB2ZW52JyxcbiAgICAgICd2ZW52XFxcXFNjcmlwdHNcXFxcYWN0aXZhdGUnLFxuICAgICAgJ3BpcCBpbnN0YWxsIGZsYXNrIGZsYXNrLXNxbGFsY2hlbXkgZmxhc2stY29ycycsXG4gICAgICAnbWtkaXIgYXBwJyxcbiAgICAgICd0b3VjaCBhcHAvX19pbml0X18ucHkgYXBwL3JvdXRlcy5weSBhcHAvbW9kZWxzLnB5J1xuICAgIF0sXG4gICAgJ0Jhc2gnOiBbXG4gICAgICAnbWtkaXIgbXktc2NyaXB0JyxcbiAgICAgICdjZCBteS1zY3JpcHQnLFxuICAgICAgJ3RvdWNoIHNjcmlwdC5zaCcsXG4gICAgICAnY2htb2QgK3ggc2NyaXB0LnNoJyxcbiAgICAgICdlY2hvIFwiIyEvYmluL2Jhc2hcIiA+IHNjcmlwdC5zaCdcbiAgICBdXG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ3JlYXRlID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghcHJvamVjdE5hbWUudHJpbSgpKSB7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAnUHJvamVjdCBuYW1lIHJlcXVpcmVkJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdQbGVhc2UgZW50ZXIgYSBuYW1lIGZvciB5b3VyIHByb2plY3QuJyxcbiAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJyxcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHNldElzQ3JlYXRpbmcodHJ1ZSk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gSW4gYSByZWFsIGFwcCwgdGhpcyB3b3VsZCBjcmVhdGUgdGhlIHByb2plY3RcbiAgICAgIC8vIEZvciBub3csIHdlJ2xsIGp1c3Qgc2ltdWxhdGUgdGhlIGNyZWF0aW9uIHByb2Nlc3NcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDAwKSk7XG5cbiAgICAgIC8vIFNob3cgc3VjY2VzcyBtZXNzYWdlXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAnUHJvamVjdCBjcmVhdGVkJyxcbiAgICAgICAgZGVzY3JpcHRpb246IGAke3Byb2plY3ROYW1lfSBoYXMgYmVlbiBjcmVhdGVkIHN1Y2Nlc3NmdWxseS5gLFxuICAgICAgfSk7XG5cbiAgICAgIC8vIENsb3NlIHRoZSBtb2RhbFxuICAgICAgb25PcGVuQ2hhbmdlKGZhbHNlKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ0ZhaWxlZCB0byBjcmVhdGUgcHJvamVjdCcsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnQW4gZXJyb3Igb2NjdXJyZWQgd2hpbGUgY3JlYXRpbmcgdGhlIHByb2plY3QuJyxcbiAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJyxcbiAgICAgIH0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0NyZWF0aW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8RGlhbG9nIG9wZW49e29wZW59IG9uT3BlbkNoYW5nZT17b25PcGVuQ2hhbmdlfT5cbiAgICAgIDxEaWFsb2dDb250ZW50IGNsYXNzTmFtZT1cInNtOm1heC13LVs1NTBweF0gbWQ6bWF4LXctWzYwMHB4XSByb3VuZGVkLXhsIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICB7LyogTG9nbyBhdCB0aGUgdG9wIGNlbnRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIG1iLTJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGgtMjAgdy00MFwiPlxuICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgIHNyYz1cIi9sb2dvLnBuZ1wiXG4gICAgICAgICAgICAgIGFsdD1cIlZJQ0UgTG9nb1wiXG4gICAgICAgICAgICAgIGZpbGxcbiAgICAgICAgICAgICAgc3R5bGU9e3sgb2JqZWN0Rml0OiAnY29udGFpbicgfX1cbiAgICAgICAgICAgICAgcHJpb3JpdHlcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBDb250ZW50IGNvbnRhaW5lciB3aXRoIGFuaW1hdGlvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICB7LyogUHJvamVjdCBGb3JtICovfVxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIGNsYXNzTmFtZT17YHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCAke1xuICAgICAgICAgICAgICBzaG93Q29tbWFuZHMgPyAndHJhbnNsYXRlLXgtWy0xMDAlXScgOiAndHJhbnNsYXRlLXgtMCdcbiAgICAgICAgICAgIH1gfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxEaWFsb2dIZWFkZXIgY2xhc3NOYW1lPVwibWItWzVdIHBiLTJcIj5cbiAgICAgICAgICAgICAgPERpYWxvZ1RpdGxlIGNsYXNzTmFtZT0ndGV4dC1jZW50ZXInPkNyZWF0ZSBOZXcgUHJvamVjdDwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgICAgIDxEaWFsb2dEZXNjcmlwdGlvbiBjbGFzc05hbWU9J3RleHQtY2VudGVyIHRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kJz5cbiAgICAgICAgICAgICAgICBDcmVhdGUgYSBuZXcge3N0YWNrTmFtZX0gcHJvamVjdC4gRmlsbCBpbiB0aGUgZGV0YWlscyBiZWxvdyB0byBnZXQgc3RhcnRlZC5cbiAgICAgICAgICAgICAgPC9EaWFsb2dEZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvRGlhbG9nSGVhZGVyPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC00IHB5LTQgcHgtMlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgZ2FwLTEuNVwiPlxuICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwibmFtZVwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgIE5hbWVcbiAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgaWQ9XCJuYW1lXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtwcm9qZWN0TmFtZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UHJvamVjdE5hbWUoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiTXkgQXdlc29tZSBQcm9qZWN0XCJcbiAgICAgICAgICAgICAgICAgIGF1dG9Gb2N1c1xuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgZ2FwLTEuNVwiPlxuICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwicGF0aFwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgIFBhdGhcbiAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgaWQ9XCJwYXRoXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtwcm9qZWN0UGF0aH1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UHJvamVjdFBhdGgoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRG9jdW1lbnRzL1ZpY2VQcm9qZWN0cy9cIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgZ2FwLTEuNVwiPlxuICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZGVzY3JpcHRpb25cIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICBEZXNjcmlwdGlvblxuICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgICBpZD1cImRlc2NyaXB0aW9uXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtwcm9qZWN0RGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFByb2plY3REZXNjcmlwdGlvbihlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJBIGJyaWVmIGRlc2NyaXB0aW9uIG9mIHlvdXIgcHJvamVjdFwiXG4gICAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2xcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtdC0yIG1iLTQgbWwtM1wiPlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmQgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTEgcC0wIGgtYXV0b1wiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Q29tbWFuZHModHJ1ZSl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFRlcm1pbmFsIGNsYXNzTmFtZT1cImgtMy41IHctMy41IG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgU2VlIFN0YXJ0aW5nIENvbW1hbmRzXG4gICAgICAgICAgICAgICAgICA8Q2hldnJvblJpZ2h0IGNsYXNzTmFtZT1cImgtMy41IHctMy41IG1sLTFcIiAvPlxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8RGlhbG9nRm9vdGVyIGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17KCkgPT4gb25PcGVuQ2hhbmdlKGZhbHNlKX0+XG4gICAgICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e2hhbmRsZUNyZWF0ZX0gZGlzYWJsZWQ9e2lzQ3JlYXRpbmd9PlxuICAgICAgICAgICAgICAgICAgICB7aXNDcmVhdGluZyA/ICdDcmVhdGluZy4uLicgOiAnQ3JlYXRlIFByb2plY3QnfVxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvRGlhbG9nRm9vdGVyPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU3RhcnRpbmcgQ29tbWFuZHMgKi99XG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgYWJzb2x1dGUgdG9wLTAgbGVmdC0wIHctZnVsbCBoLWZ1bGwgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0ICR7XG4gICAgICAgICAgICAgIHNob3dDb21tYW5kcyA/ICd0cmFuc2xhdGUteC0wJyA6ICd0cmFuc2xhdGUteC1bMTAwJV0nXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTQgbWwtM1wiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBsLTAgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDb21tYW5kcyhmYWxzZSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgIEJhY2sgdG8gUHJvamVjdCBGb3JtXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxEaWFsb2dIZWFkZXI+XG4gICAgICAgICAgICAgIDxEaWFsb2dUaXRsZT5TdGFydGluZyBDb21tYW5kcyBmb3Ige3N0YWNrTmFtZX08L0RpYWxvZ1RpdGxlPlxuICAgICAgICAgICAgICA8RGlhbG9nRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgVXNlIHRoZXNlIGNvbW1hbmRzIHRvIHNldCB1cCB5b3VyIHtzdGFja05hbWV9IHByb2plY3QgbWFudWFsbHkuXG4gICAgICAgICAgICAgIDwvRGlhbG9nRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0RpYWxvZ0hlYWRlcj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS00IHB4LTNcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1tdXRlZCBwLTQgcm91bmRlZC1tZCBmb250LW1vbm8gdGV4dC14cyBzbTp0ZXh0LXNtIG92ZXJmbG93LWF1dG8gbWF4LWgtWzI1MHB4XVwiPlxuICAgICAgICAgICAgICAgIHtzdGFydGluZ0NvbW1hbmRzW3N0YWNrTmFtZSBhcyBrZXlvZiB0eXBlb2Ygc3RhcnRpbmdDb21tYW5kc10/Lm1hcCgoY29tbWFuZCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtci0yIGZsZXgtc2hyaW5rLTBcIj4kPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJicmVhay1hbGxcIj57Y29tbWFuZH08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKSB8fCAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPk5vIGNvbW1hbmRzIGF2YWlsYWJsZSBmb3Ige3N0YWNrTmFtZX0uPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPERpYWxvZ0Zvb3Rlcj5cbiAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9eygpID0+IG9uT3BlbkNoYW5nZShmYWxzZSl9PlxuICAgICAgICAgICAgICAgIENsb3NlXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9EaWFsb2dGb290ZXI+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9EaWFsb2dDb250ZW50PlxuICAgIDwvRGlhbG9nPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJJbWFnZSIsIkRpYWxvZyIsIkRpYWxvZ0NvbnRlbnQiLCJEaWFsb2dEZXNjcmlwdGlvbiIsIkRpYWxvZ0Zvb3RlciIsIkRpYWxvZ0hlYWRlciIsIkRpYWxvZ1RpdGxlIiwiQnV0dG9uIiwiSW5wdXQiLCJMYWJlbCIsIlRleHRhcmVhIiwidXNlVG9hc3QiLCJBcnJvd0xlZnQiLCJDaGV2cm9uUmlnaHQiLCJUZXJtaW5hbCIsIkNyZWF0ZVByb2plY3RNb2RhbCIsIm9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJzdGFja05hbWUiLCJwcm9qZWN0VGl0bGUiLCJzdGFydGluZ0NvbW1hbmRzIiwidG9hc3QiLCJwcm9qZWN0TmFtZSIsInNldFByb2plY3ROYW1lIiwicHJvamVjdFBhdGgiLCJzZXRQcm9qZWN0UGF0aCIsInRvTG93ZXJDYXNlIiwicHJvamVjdERlc2NyaXB0aW9uIiwic2V0UHJvamVjdERlc2NyaXB0aW9uIiwiaXNDcmVhdGluZyIsInNldElzQ3JlYXRpbmciLCJzaG93Q29tbWFuZHMiLCJzZXRTaG93Q29tbWFuZHMiLCJzaG93TG9hZGluZyIsInNldFNob3dMb2FkaW5nIiwiY3VycmVudENvbW1hbmQiLCJzZXRDdXJyZW50Q29tbWFuZCIsImNvbW1hbmRQcm9ncmVzcyIsInNldENvbW1hbmRQcm9ncmVzcyIsImlzQ29tbWFuZFJ1bm5pbmciLCJzZXRJc0NvbW1hbmRSdW5uaW5nIiwidmFsaWRhdGVQcm9qZWN0TmFtZSIsIm5hbWUiLCJ0cmltIiwicmVwbGFjZSIsImhhbmRsZVByb2plY3ROYW1lQ2hhbmdlIiwiZSIsInZhbHVlIiwidGFyZ2V0Iiwid2luZG93IiwiZWxlY3Ryb25BUEkiLCJoYW5kbGVDb21tYW5kUHJvZ3Jlc3MiLCJkYXRhIiwiY29uc29sZSIsImxvZyIsInN0YXR1cyIsImNvbW1hbmQiLCJwcmV2IiwiZXJyb3IiLCJzZXRUaW1lb3V0IiwiaGFuZGxlTG9hZGluZ0NvbXBsZXRlIiwib25Db21tYW5kUHJvZ3Jlc3MiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiaGFuZGxlQ3JlYXRlIiwidmFyaWFudCIsIlByb21pc2UiLCJyZXNvbHZlIiwiY2xhc3NOYW1lIiwiZGl2Iiwic3JjIiwiYWx0IiwiZmlsbCIsInN0eWxlIiwib2JqZWN0Rml0IiwicHJpb3JpdHkiLCJodG1sRm9yIiwiaWQiLCJvbkNoYW5nZSIsInBsYWNlaG9sZGVyIiwiYXV0b0ZvY3VzIiwicm93cyIsInNpemUiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJtYXAiLCJpbmRleCIsInNwYW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/create-project-modal.tsx\n"));

/***/ })

});